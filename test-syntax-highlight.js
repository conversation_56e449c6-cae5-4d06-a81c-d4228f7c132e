// 这是一个测试文件，用于验证语法高亮
const message = "Hello, World!";
let count = 42;
var isActive = true;

/**
 * 这是一个多行注释
 * 用于测试注释的高亮显示
 */
function greetUser(name, age) {
  if (name && age > 0) {
    console.log(`Hello ${name}, you are ${age} years old!`);
    return true;
  } else {
    console.error("Invalid parameters");
    return false;
  }
}

// 类定义
class User {
  constructor(name, email) {
    this.name = name;
    this.email = email;
    this.id = Math.random();
  }
  
  getName() {
    return this.name;
  }
  
  setEmail(newEmail) {
    if (newEmail.includes('@')) {
      this.email = newEmail;
    } else {
      throw new Error('Invalid email format');
    }
  }
}

// 箭头函数
const calculateSum = (a, b) => a + b;

// 数组和对象
const numbers = [1, 2, 3, 4, 5];
const userInfo = {
  name: "<PERSON>",
  age: 30,
  hobbies: ["reading", "coding", "gaming"],
  address: {
    street: "123 Main St",
    city: "New York",
    zipCode: "10001"
  }
};

// 正则表达式
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// 模板字符串
const template = `
  User: ${userInfo.name}
  Age: ${userInfo.age}
  Email: ${userInfo.email || 'Not provided'}
`;

// 异步函数
async function fetchData(url) {
  try {
    const response = await fetch(url);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}

// 导出
export { User, greetUser, calculateSum };
export default fetchData;
