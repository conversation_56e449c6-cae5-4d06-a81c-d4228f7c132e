#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 清理构建目录
clean_build() {
    print_info "清理构建目录..."
    
    # 清理前端构建
    if [ -d "dist" ]; then
        rm -rf dist
        print_info "已删除 dist 目录"
    fi
    
    # 清理 Rust 构建
    if [ -d "src-tauri/target" ]; then
        rm -rf src-tauri/target
        print_info "已删除 src-tauri/target 目录"
    fi
    
    print_success "构建目录清理完成"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查 Node.js
    check_command node
    NODE_VERSION=$(node -v)
    print_info "Node.js 版本: $NODE_VERSION"
    
    # 检查 npm
    check_command npm
    NPM_VERSION=$(npm -v)
    print_info "npm 版本: $NPM_VERSION"
    
    # 检查 Rust
    check_command rustc
    RUST_VERSION=$(rustc --version)
    print_info "Rust 版本: $RUST_VERSION"
    
    # 检查 Cargo
    check_command cargo
    CARGO_VERSION=$(cargo --version)
    print_info "Cargo 版本: $CARGO_VERSION"
    
    # 检查 Tauri CLI
    if ! npm list -g @tauri-apps/cli &> /dev/null; then
        print_warning "Tauri CLI 未全局安装，将使用本地安装"
    else
        TAURI_VERSION=$(npm list -g @tauri-apps/cli | grep @tauri-apps/cli)
        print_info "Tauri CLI 版本: $TAURI_VERSION"
    fi
    
    print_success "依赖检查完成"
}

# 安装依赖
install_dependencies() {
    print_info "安装依赖..."
    
    # 安装 npm 依赖
    print_info "安装 npm 依赖..."
    npm install
    
    if [ $? -ne 0 ]; then
        print_error "npm 依赖安装失败"
        exit 1
    fi
    
    print_success "依赖安装完成"
}

# 构建前端
build_frontend() {
    print_info "构建前端..."
    
    npm run build
    
    if [ $? -ne 0 ]; then
        print_error "前端构建失败"
        exit 1
    fi
    
    print_success "前端构建完成"
}

# 构建 Tauri 应用
build_tauri() {
    print_info "构建 Tauri 应用..."
    
    # 检查是否需要构建开发版本
    if [ "$1" == "dev" ]; then
        print_info "构建开发版本..."
        npm run tauri dev
    else
        print_info "构建发布版本..."
        npm run tauri build
    fi
    
    if [ $? -ne 0 ]; then
        print_error "Tauri 应用构建失败"
        exit 1
    fi
    
    print_success "Tauri 应用构建完成"
}

# 主函数
main() {
    print_info "开始构建 Taskers 应用..."
    
    # 解析命令行参数
    BUILD_TYPE="release"
    CLEAN=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dev)
                BUILD_TYPE="dev"
                shift
                ;;
            --clean)
                CLEAN=true
                shift
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 如果需要清理构建目录
    if [ "$CLEAN" = true ]; then
        clean_build
    fi
    
    # 检查依赖
    check_dependencies
    
    # 安装依赖
    install_dependencies
    
    # 如果是开发版本，直接启动开发环境
    if [ "$BUILD_TYPE" == "dev" ]; then
        build_tauri "dev"
    else
        # 构建前端
        build_frontend
        
        # 构建 Tauri 应用
        build_tauri
    fi
    
    print_success "Taskers 应用构建完成"
}

# 执行主函数
main "$@"
