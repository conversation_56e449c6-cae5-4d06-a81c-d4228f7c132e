# Taskers - 多功能任务与测试管理系统

![Taskers Logo](./src/assets/logo.svg)

## 项目简介
Taskers 是一个现代化的多功能任务与测试管理系统，集成了任务调度、远程执行、测试用例管理、缺陷跟踪和测试计划等功能，旨在提供一个高效、可靠的全流程测试与任务管理解决方案。本应用基于Tauri框架开发，提供跨平台支持，具有轻量级、高性能的特点。

## 功能特性

### 1. 任务管理与执行
- 任务创建、编辑和删除
- 任务分组与嵌套
- 定时调度执行
- 本地与远程执行支持
- 任务执行状态实时监控
- 自定义执行命令

### 2. 执行代理管理
- 本地Master执行代理
- 远程SSH执行代理
- 代理状态监控
- 代理连接测试
- 标签分类管理

### 3. 测试用例管理
- 测试用例的创建、编辑和删除
- 批量导入Excel测试用例
- 用例优先级设置（高、中、低）
- 测试步骤和预期结果管理
- 实际结果记录
- 用例状态跟踪（未开始、进行中、通过、失败、阻塞）
- 自动化测试标记

### 4. 缺陷管理
- 缺陷创建与跟踪
- 严重程度和优先级设置
- 缺陷状态流转（新建、进行中、已解决、已关闭）
- 缺陷重开功能
- 关联测试用例

### 5. 测试计划
- 测试计划创建与管理
- 测试用例关联
- 进度跟踪
- 执行人分配
- 时间计划管理

### 6. 终端集成
- 内置PTY终端
- 交互式命令执行
- 终端会话管理

### 7. AI聊天助手
- 集成AI对话功能
- 多种API支持
- 对话历史管理

## 技术架构

### 前端技术栈
- React 18
- TypeScript
- Ant Design 组件库
- Vite 构建工具
- XTerm.js 终端模拟

### 后端技术栈
- Tauri 框架
- Rust 语言
- SQLite 数据库
- Tokio 异步运行时
- Rusqlite 数据库驱动
- SSH2 远程连接

## 数据库设计

### Tasks 表
- id: 任务唯一标识
- name: 任务名称
- description: 任务描述
- working_dir: 工作目录
- schedule: 调度表达式
- status: 任务状态
- agent_id: 执行代理ID
- parent_id: 父任务ID
- is_group: 是否为任务组
- execution_mode: 执行模式
- child_tasks: 子任务列表
- test_cases: 关联测试用例
- loop_count: 循环执行次数
- custom_command: 自定义命令

### Agents 表
- id: 代理唯一标识
- name: 代理名称
- ip: IP地址
- username: 用户名
- password: 密码
- work_dir: 工作目录
- status: 状态
- last_seen: 最后在线时间
- ping_latency: 延迟
- labels: 标签
- description: 描述
- max_jobs: 最大并发任务数

### TestCases 表
- id: 用例唯一标识
- name: 用例名称
- description: 用例描述
- priority: 优先级（High/Medium/Low）
- status: 状态（NotStarted/InProgress/Passed/Failed/Blocked）
- expected_result: 预期结果
- actual_result: 实际结果
- created_at: 创建时间
- updated_at: 更新时间
- created_by: 创建者
- assigned_to: 分配给
- tags: 标签
- module_id: 所属模块
- preconditions: 前置条件
- test_steps: 测试步骤
- is_automated: 是否自动化

### Projects 表
- id: 项目唯一标识
- name: 项目名称
- description: 项目描述
- status: 项目状态
- created_at: 创建时间
- updated_at: 更新时间
- created_by: 创建者
- leader: 项目负责人
- tags: 项目标签

### Defects 表
- id: 缺陷唯一标识
- title: 缺陷标题
- description: 缺陷描述
- severity: 严重程度
- priority: 优先级
- status: 状态
- project_id: 所属项目
- module_id: 所属模块
- steps_to_reproduce: 复现步骤
- test_cases: 关联用例

### TestPlans 表
- id: 计划唯一标识
- name: 计划名称
- description: 计划描述
- status: 状态
- start_date: 开始日期
- end_date: 结束日期
- project_id: 所属项目
- test_cases: 关联用例
- progress: 执行进度
- executor: 执行人

## 开发环境配置

### 系统要求
- Node.js 16+
- Rust 1.60+
- Tauri CLI

### 快速启动
```bash
# 使用启动脚本（推荐）
./start-app.sh

# 或使用构建脚本
./build.sh --dev  # 开发模式
./build.sh        # 发布模式
./build.sh --clean  # 清理并重新构建
```

### 前端开发
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 后端开发
```bash
# 安装Tauri CLI
cargo install tauri-cli

# 构建并运行
cargo tauri dev

# 构建发布版本
cargo tauri build
```

### 日志系统
日志文件保存在项目根目录的 `logs` 文件夹中，格式为 `tasker-YYYYMMDD-HHMMSS.log`。日志包含时间戳、模块名、函数名和详细信息，便于调试和问题排查。

## 使用说明

### 基本流程
1. 首先创建执行代理，设置连接信息
2. 创建任务，设置执行步骤和调度计划
3. 创建测试用例，详细描述测试步骤和预期结果
4. 将测试用例关联到任务中
5. 执行任务，监控执行状态
6. 如发现问题，创建缺陷并关联相关用例
7. 创建测试计划，关联测试用例并分配执行人
8. 跟踪测试计划执行进度

### Excel导入测试用例
1. 准备符合格式的Excel文件，包含以下列：
   - 名称（必填）
   - 描述
   - 优先级（高/中/低）
   - 状态（未开始/进行中/通过/失败/阻塞）
   - 预期结果
   - 分配给
   - 标签
   - 前置条件
   - 测试步骤
   - 是否自动化（是/否）
2. 在测试用例管理界面点击“导入Excel”
3. 选择准备好的Excel文件
4. 系统会自动解析并导入测试用例

## 贡献指南
欢迎贡献代码、报告问题或提出新功能建议。请遵循以下步骤：

1. Fork本仓库
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启一个Pull Request

## 许可证
本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式
如有任何问题或建议，请通过以下方式联系我们：
- 电子邮件：<EMAIL>
- GitHub Issues：[提交问题](https://github.com/yourusername/taskers/issues)