#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
这是一个用于测试语法高亮的 Python 文件
包含了各种 Python 语法元素
"""

import os
import sys
import json
from typing import List, Dict, Optional, Union
from datetime import datetime, timedelta
import asyncio
import re

# 常量定义
VERSION = "1.0.0"
DEBUG = True
MAX_RETRIES = 3

# 全局变量
user_count = 0
active_sessions = []

class User:
    """用户类，演示类的定义和方法"""
    
    def __init__(self, name: str, email: str, age: int = 0):
        self.name = name
        self.email = email
        self.age = age
        self.created_at = datetime.now()
        self._id = self._generate_id()
    
    def _generate_id(self) -> str:
        """生成用户ID"""
        import uuid
        return str(uuid.uuid4())
    
    @property
    def is_adult(self) -> bool:
        """检查是否成年"""
        return self.age >= 18
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def to_dict(self) -> Dict[str, Union[str, int, bool]]:
        """转换为字典"""
        return {
            'id': self._id,
            'name': self.name,
            'email': self.email,
            'age': self.age,
            'is_adult': self.is_adult,
            'created_at': self.created_at.isoformat()
        }
    
    def __str__(self) -> str:
        return f"User(name='{self.name}', email='{self.email}', age={self.age})"
    
    def __repr__(self) -> str:
        return self.__str__()

def calculate_fibonacci(n: int) -> List[int]:
    """计算斐波那契数列"""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    fib = [0, 1]
    for i in range(2, n):
        fib.append(fib[i-1] + fib[i-2])
    
    return fib

async def fetch_data(url: str, timeout: int = 30) -> Optional[Dict]:
    """异步获取数据"""
    try:
        # 模拟异步HTTP请求
        await asyncio.sleep(0.1)
        
        # 模拟返回数据
        return {
            'url': url,
            'status': 200,
            'data': f'Data from {url}',
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        print(f"Error fetching data from {url}: {e}")
        return None

def process_users(users_data: List[Dict]) -> List[User]:
    """处理用户数据"""
    users = []
    
    for user_data in users_data:
        try:
            # 数据验证
            if not all(key in user_data for key in ['name', 'email']):
                print(f"Invalid user data: {user_data}")
                continue
            
            # 创建用户对象
            user = User(
                name=user_data['name'],
                email=user_data['email'],
                age=user_data.get('age', 0)
            )
            
            # 验证邮箱
            if not User.validate_email(user.email):
                print(f"Invalid email for user {user.name}: {user.email}")
                continue
            
            users.append(user)
            
        except Exception as e:
            print(f"Error processing user {user_data}: {e}")
    
    return users

def main():
    """主函数"""
    print("=" * 50)
    print("语法高亮测试程序")
    print("=" * 50)
    
    # 测试数据
    test_users = [
        {'name': 'Alice', 'email': '<EMAIL>', 'age': 25},
        {'name': 'Bob', 'email': '<EMAIL>', 'age': 17},
        {'name': 'Charlie', 'email': 'charlie@invalid-email', 'age': 30},
        {'name': 'Diana', 'email': '<EMAIL>', 'age': 22}
    ]
    
    # 处理用户
    users = process_users(test_users)
    
    print(f"\n成功处理 {len(users)} 个用户:")
    for user in users:
        print(f"  - {user}")
        print(f"    成年状态: {'是' if user.is_adult else '否'}")
    
    # 计算斐波那契数列
    print(f"\n斐波那契数列 (前10项): {calculate_fibonacci(10)}")
    
    # 字符串格式化示例
    current_time = datetime.now()
    formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"\n当前时间: {formatted_time}")
    
    # 列表推导式
    adult_users = [user for user in users if user.is_adult]
    print(f"\n成年用户数量: {len(adult_users)}")
    
    # 字典推导式
    user_ages = {user.name: user.age for user in users}
    print(f"用户年龄映射: {user_ages}")
    
    # 异常处理
    try:
        result = 10 / 0
    except ZeroDivisionError as e:
        print(f"\n捕获到异常: {e}")
    finally:
        print("程序执行完成")

if __name__ == "__main__":
    main()
