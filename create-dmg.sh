#!/bin/bash

set -e

# 检查是否安装了create-dmg
if ! command -v create-dmg &> /dev/null; then
    echo "正在安装create-dmg..."
    npm install --yes -g create-dmg
fi

# 设置变量
APP_NAME="taskers"
APP_VERSION="0.0.1"
DMG_NAME="${APP_NAME}-${APP_VERSION}.dmg"
APP_PATH="src-tauri/target/release/bundle/dmg/${APP_NAME}.app"
DMG_PATH="dist/${DMG_NAME}"

# 清理和创建dist目录
rm -rf "dist/${APP_NAME}"*.dmg
mkdir -p dist

# 检查应用程序是否存在
if [ ! -d "$APP_PATH" ]; then
    # 尝试其他可能的路径
    APP_PATH="src-tauri/target/release/${APP_NAME}.app"
    
    if [ ! -d "$APP_PATH" ]; then
        echo "错误: 找不到应用程序 $APP_PATH"
        echo "可能的构建输出路径:"
        echo "1. src-tauri/target/release/bundle/dmg/${APP_NAME}.app"
        echo "2. src-tauri/target/release/${APP_NAME}.app"
        echo "3. src-tauri/target/debug/bundle/dmg/${APP_NAME}.app"
        echo "正在尝试自动构建应用程序..."
    
        # 检查是否安装了Tauri CLI
        if ! command -v tauri &> /dev/null; then
            echo "错误: 找不到tauri命令，请先安装Tauri CLI"
            echo "可以通过运行 'npm install @tauri-apps/cli' 安装"
            exit 1
        fi
        
        # 尝试构建应用
        echo "正在运行 'npm run tauri build'..."
        if ! npm run tauri build; then
            echo "错误: 应用程序构建失败，请检查错误信息"
            exit 1
        fi
        
        # 再次检查应用程序是否存在
        if [ ! -d "$APP_PATH" ]; then
            echo "错误: 构建后仍然找不到应用程序 $APP_PATH"
            exit 1
        fi
    fi
fi

# 创建DMG包
echo "正在创建DMG包..."
create-dmg \
    --volname "$APP_NAME" \
    --volicon "src-tauri/icons/icon.icns" \
    --window-pos 200 120 \
    --window-size 800 500 \
    --icon-size 128 \
    --icon "$APP_NAME.app" 150 190 \
    --hide-extension "$APP_NAME.app" \
    --app-drop-link 450 190 \
    --no-internet-enable \
    --background "src-tauri/icons/icon.png" \
    "$DMG_PATH" \
    "$APP_PATH"

if [ $? -eq 0 ]; then
    echo "DMG包创建成功: $DMG_PATH"
    echo "你可以在dist目录下找到DMG安装包"
else
    echo "DMG包创建失败，请检查错误信息"
    exit 1
fi