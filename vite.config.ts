import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";

export default defineConfig({
  plugins: [react()],
  server: {
    port: 1420,
    strictPort: false,
    host: true
  },
  envPrefix: ["VITE_", "TAURI_"],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    target: process.env.TAURI_PLATFORM == "windows" ? "chrome105" : "safari13",
    minify: !process.env.TAURI_DEBUG ? "esbuild" : false,
    sourcemap: !!process.env.TAURI_DEBUG,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'antd'],
          chat: ['./src/components/chat/AIChat.tsx', './src/components/chat/ConversationSidebar.tsx', './src/components/chat/MessageInput.tsx', './src/components/chat/MessageList.tsx'],
        },
      },
      external: [
        '@tauri-apps/api/dialog',
        '@tauri-apps/api/core',
        '@tauri-apps/api/window',
        '@tauri-apps/api/event'
      ]
    },
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'antd'],
  },
});
