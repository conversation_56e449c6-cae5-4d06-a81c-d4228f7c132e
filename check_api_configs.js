import sqlite3 from 'sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const sqlite = sqlite3.verbose();

// 数据库路径
const dbPath = path.join(__dirname, 'tasks.db');
console.log('数据库路径:', dbPath);

// 连接到数据库
const db = new sqlite.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');

  // 查询 API 配置
  db.all('SELECT * FROM api_configs', [], (err, rows) => {
    if (err) {
      console.error('查询 API 配置失败:', err.message);
      closeDb();
      process.exit(1);
    }

    console.log('API 配置:');
    if (rows.length === 0) {
      console.log('- 没有找到 API 配置');
    } else {
      rows.forEach(row => {
        const models = JSON.parse(row.models || '[]');
        console.log(`- 提供商: ${row.provider}`);
        console.log(`  API 密钥: ${row.api_key.substring(0, 5)}...`);
        console.log(`  基础 URL: ${row.base_url}`);
        console.log(`  可用模型: ${models.join(', ')}`);
        console.log('');
      });
    }

    // 查询对话
    db.all('SELECT * FROM chat_conversations', [], (err, rows) => {
      if (err) {
        console.error('查询对话失败:', err.message);
        closeDb();
        process.exit(1);
      }

      console.log('对话:');
      if (rows.length === 0) {
        console.log('- 没有找到对话');
      } else {
        rows.forEach(row => {
          console.log(`- ID: ${row.id}`);
          console.log(`  标题: ${row.title}`);
          console.log(`  提供商: ${row.provider}`);
          console.log(`  模型: ${row.model}`);
          console.log(`  创建时间: ${row.created_at}`);
          console.log(`  更新时间: ${row.updated_at}`);
          console.log('');
        });
      }

      closeDb();
    });
  });
});

function closeDb() {
  db.close((err) => {
    if (err) {
      console.error('关闭数据库失败:', err.message);
    } else {
      console.log('数据库连接已关闭');
    }
  });
}
