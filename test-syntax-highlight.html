<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语法高亮测试页面</title>
    <style>
        /* CSS 样式测试 */
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f0f0f0;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        #demo-button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        #demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>语法高亮测试页面</h1>
        
        <p>这是一个用于测试 <span class="highlight">语法高亮</span> 功能的 HTML 页面。</p>
        
        <h2>功能列表</h2>
        <ul>
            <li>HTML 标签高亮</li>
            <li>CSS 样式高亮</li>
            <li>JavaScript 代码高亮</li>
            <li>属性和值的颜色区分</li>
        </ul>
        
        <h2>交互演示</h2>
        <button id="demo-button" onclick="showMessage()">点击测试</button>
        <div id="message-area"></div>
        
        <h2>表单示例</h2>
        <form action="#" method="post">
            <label for="username">用户名:</label>
            <input type="text" id="username" name="username" required>
            
            <label for="email">邮箱:</label>
            <input type="email" id="email" name="email" required>
            
            <label for="message">留言:</label>
            <textarea id="message" name="message" rows="4" cols="50"></textarea>
            
            <input type="submit" value="提交">
        </form>
    </div>

    <script>
        // JavaScript 代码测试
        function showMessage() {
            const messageArea = document.getElementById('message-area');
            const currentTime = new Date().toLocaleString();
            
            messageArea.innerHTML = `
                <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-left: 4px solid #4caf50;">
                    <strong>测试成功!</strong><br>
                    当前时间: ${currentTime}
                </div>
            `;
            
            // 添加一些动画效果
            messageArea.style.opacity = '0';
            setTimeout(() => {
                messageArea.style.transition = 'opacity 0.5s ease';
                messageArea.style.opacity = '1';
            }, 100);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 为表单添加验证
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('这只是一个演示，表单不会真正提交');
            });
        });
    </script>
</body>
</html>
