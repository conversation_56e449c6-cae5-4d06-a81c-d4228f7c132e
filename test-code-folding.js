// 测试代码折叠功能的 JavaScript 文件
// 包含多层嵌套结构，用于测试折叠和展开功能

/**
 * 用户管理类
 * 演示类的折叠功能
 */
class UserManager {
  constructor() {
    this.users = [];
    this.currentUser = null;
    this.settings = {
      theme: 'dark',
      language: 'zh-CN',
      notifications: true
    };
  }

  /**
   * 添加新用户
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} 创建的用户对象
   */
  async addUser(userData) {
    try {
      // 验证用户数据
      if (!userData.email || !userData.name) {
        throw new Error('缺少必要的用户信息');
      }

      // 检查邮箱是否已存在
      const existingUser = this.users.find(user => user.email === userData.email);
      if (existingUser) {
        throw new Error('邮箱已存在');
      }

      // 创建新用户
      const newUser = {
        id: this.generateUserId(),
        name: userData.name,
        email: userData.email,
        createdAt: new Date(),
        isActive: true,
        profile: {
          avatar: userData.avatar || null,
          bio: userData.bio || '',
          preferences: {
            theme: 'auto',
            notifications: {
              email: true,
              push: false,
              sms: false
            }
          }
        }
      };

      // 添加到用户列表
      this.users.push(newUser);
      
      // 触发事件
      this.emit('userAdded', newUser);
      
      return newUser;
    } catch (error) {
      console.error('添加用户失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户列表
   * @param {Object} filters - 过滤条件
   * @returns {Array} 用户列表
   */
  getUsers(filters = {}) {
    let filteredUsers = [...this.users];

    // 按状态过滤
    if (filters.isActive !== undefined) {
      filteredUsers = filteredUsers.filter(user => user.isActive === filters.isActive);
    }

    // 按创建时间过滤
    if (filters.dateRange) {
      const { start, end } = filters.dateRange;
      filteredUsers = filteredUsers.filter(user => {
        const userDate = new Date(user.createdAt);
        return userDate >= start && userDate <= end;
      });
    }

    // 按关键词搜索
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.name.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm)
      );
    }

    // 排序
    if (filters.sortBy) {
      filteredUsers.sort((a, b) => {
        switch (filters.sortBy) {
          case 'name':
            return a.name.localeCompare(b.name);
          case 'email':
            return a.email.localeCompare(b.email);
          case 'createdAt':
            return new Date(b.createdAt) - new Date(a.createdAt);
          default:
            return 0;
        }
      });
    }

    return filteredUsers;
  }

  /**
   * 更新用户信息
   * @param {string} userId - 用户ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新后的用户对象
   */
  async updateUser(userId, updateData) {
    try {
      const userIndex = this.users.findIndex(user => user.id === userId);
      if (userIndex === -1) {
        throw new Error('用户不存在');
      }

      const user = this.users[userIndex];
      
      // 深度合并更新数据
      const updatedUser = {
        ...user,
        ...updateData,
        profile: {
          ...user.profile,
          ...updateData.profile,
          preferences: {
            ...user.profile.preferences,
            ...updateData.profile?.preferences,
            notifications: {
              ...user.profile.preferences.notifications,
              ...updateData.profile?.preferences?.notifications
            }
          }
        },
        updatedAt: new Date()
      };

      this.users[userIndex] = updatedUser;
      this.emit('userUpdated', updatedUser);
      
      return updatedUser;
    } catch (error) {
      console.error('更新用户失败:', error);
      throw error;
    }
  }

  /**
   * 删除用户
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 删除结果
   */
  async deleteUser(userId) {
    try {
      const userIndex = this.users.findIndex(user => user.id === userId);
      if (userIndex === -1) {
        throw new Error('用户不存在');
      }

      const deletedUser = this.users.splice(userIndex, 1)[0];
      this.emit('userDeleted', deletedUser);
      
      return true;
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    }
  }

  /**
   * 生成用户ID
   * @returns {string} 唯一的用户ID
   */
  generateUserId() {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 事件发射器
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    console.log(`事件触发: ${event}`, data);
    // 这里可以实现真正的事件系统
  }
}

// 工具函数集合
const Utils = {
  /**
   * 格式化日期
   * @param {Date} date - 日期对象
   * @param {string} format - 格式字符串
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date, format = 'YYYY-MM-DD') {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  },

  /**
   * 验证邮箱格式
   * @param {string} email - 邮箱地址
   * @returns {boolean} 验证结果
   */
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * 深度克隆对象
   * @param {*} obj - 要克隆的对象
   * @returns {*} 克隆后的对象
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }

    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item));
    }

    if (typeof obj === 'object') {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }

    return obj;
  }
};

// 使用示例
async function main() {
  const userManager = new UserManager();

  try {
    // 添加测试用户
    const user1 = await userManager.addUser({
      name: '张三',
      email: '<EMAIL>',
      bio: '前端开发工程师'
    });

    const user2 = await userManager.addUser({
      name: '李四',
      email: '<EMAIL>',
      bio: '后端开发工程师'
    });

    // 获取用户列表
    const users = userManager.getUsers({
      isActive: true,
      sortBy: 'name'
    });

    console.log('用户列表:', users);

    // 更新用户信息
    await userManager.updateUser(user1.id, {
      profile: {
        bio: '高级前端开发工程师',
        preferences: {
          theme: 'dark',
          notifications: {
            email: false,
            push: true
          }
        }
      }
    });

    console.log('用户管理系统初始化完成');
  } catch (error) {
    console.error('初始化失败:', error);
  }
}

// 启动应用
main().catch(console.error);
