#!/bin/bash

# 定义要检查的端口
PORT=1420

# 检查Node.js是否可用
check_node() {
    # 尝试使用which命令找到node的路径
    NODE_PATH=$(which node 2>/dev/null)

    if [ -z "$NODE_PATH" ]; then
        # 如果which命令找不到node，尝试常见的安装位置
        for path in "/usr/local/bin/node" "/usr/bin/node" "$HOME/.nvm/versions/node/*/bin/node" "/opt/homebrew/bin/node" "/opt/local/bin/node"; do
            if [ -x "$path" ]; then
                NODE_PATH="$path"
                break
            fi
        done
    fi

    # 如果仍然找不到node，提示用户安装
    if [ -z "$NODE_PATH" ]; then
        echo "错误: 找不到node命令。请确保Node.js已安装并添加到PATH环境变量中。"
        echo "您可以通过以下方式安装Node.js:"
        echo "  - 使用Homebrew: brew install node"
        echo "  - 从官网下载: https://nodejs.org/"
        exit 1
    fi

    echo "找到Node.js: $NODE_PATH"
    export NODE_PATH
}

# 清理构建进程函数
clean_build_processes() {
    echo "正在清理可能的构建进程..."

    # 检测操作系统类型
    if [[ "$OSTYPE" == "darwin"* ]] || [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # macOS 或 Linux
        echo "检测到 Unix 类系统 (macOS/Linux)"

        # 查找并终止 cargo 相关进程
        CARGO_PIDS=$(ps aux | grep -E '[c]argo (build|check|run|test)' | awk '{print $2}')
        if [ ! -z "$CARGO_PIDS" ]; then
            echo "发现 Cargo 构建进程，正在终止..."
            for PID in $CARGO_PIDS; do
                echo "终止进程 $PID"
                kill -9 $PID 2>/dev/null
            done
            sleep 1
        fi

        # 查找并终止 rustc 进程
        RUSTC_PIDS=$(ps aux | grep '[r]ustc' | awk '{print $2}')
        if [ ! -z "$RUSTC_PIDS" ]; then
            echo "发现 Rustc 编译进程，正在终止..."
            for PID in $RUSTC_PIDS; do
                echo "终止进程 $PID"
                kill -9 $PID 2>/dev/null
            done
            sleep 1
        fi

        # 查找并终止 npm/node 构建进程
        NPM_PIDS=$(ps aux | grep -E '[n]pm (run|build|start)' | awk '{print $2}')
        if [ ! -z "$NPM_PIDS" ]; then
            echo "发现 NPM 构建进程，正在终止..."
            for PID in $NPM_PIDS; do
                echo "终止进程 $PID"
                kill -9 $PID 2>/dev/null
            done
            sleep 1
        fi

    elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        # Windows with Git Bash or similar
        echo "检测到 Windows 系统"

        # 查找并终止 cargo 相关进程
        tasklist | grep -i "cargo.exe" > temp.txt
        if [ -s temp.txt ]; then
            echo "发现 Cargo 构建进程，正在终止..."
            while read line; do
                PID=$(echo $line | awk '{print $2}')
                echo "终止进程 $PID"
                taskkill /F /PID $PID 2>/dev/null
            done < temp.txt
            sleep 1
        fi

        # 查找并终止 rustc 进程
        tasklist | grep -i "rustc.exe" > temp.txt
        if [ -s temp.txt ]; then
            echo "发现 Rustc 编译进程，正在终止..."
            while read line; do
                PID=$(echo $line | awk '{print $2}')
                echo "终止进程 $PID"
                taskkill /F /PID $PID 2>/dev/null
            done < temp.txt
            sleep 1
        fi

        # 查找并终止 npm/node 构建进程
        tasklist | grep -i "npm.exe" > temp.txt
        if [ -s temp.txt ]; then
            echo "发现 NPM 构建进程，正在终止..."
            while read line; do
                PID=$(echo $line | awk '{print $2}')
                echo "终止进程 $PID"
                taskkill /F /PID $PID 2>/dev/null
            done < temp.txt
            sleep 1
        fi

        rm -f temp.txt
    fi

    echo "构建进程清理完成"
}

# 清理锁文件函数
clean_lock_files() {
    echo "正在清理可能的锁文件..."

    # 清理 Cargo 锁文件
    if [ -d "src-tauri/target" ]; then
        echo "清理 Cargo 锁文件..."
        find src-tauri/target -name "*.lock" -type f -delete 2>/dev/null
        rm -f src-tauri/target/.rustc_info.json 2>/dev/null
    fi

    # 清理 NPM 锁文件
    if [ -d "node_modules" ]; then
        echo "清理 NPM 锁文件..."
        find node_modules -name ".lock-*" -type f -delete 2>/dev/null
    fi

    echo "锁文件清理完成"
}

# 清理端口函数
clean_port() {
    echo "正在检查端口 $PORT 占用情况..."
    # 先尝试使用 Node.js 脚本清理端口
    if [ -f "kill-port.mjs" ]; then
        echo "使用 Node.js 脚本清理端口..."
        "$NODE_PATH" kill-port.mjs
        # 添加延迟确保端口释放
        sleep 1
    fi

    # 检测操作系统类型并进行额外检查
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        echo "检测到 macOS 系统"
        RUNNING_PID=$(lsof -ti:$PORT)
        if [ ! -z "$RUNNING_PID" ]; then
            echo "端口 $PORT 被进程 $RUNNING_PID 占用，正在结束该进程..."
            kill -9 $RUNNING_PID
            sleep 1
            echo "进程已结束"
        else
            echo "端口 $PORT 未被占用"
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        echo "检测到 Linux 系统"
        RUNNING_PID=$(lsof -ti:$PORT)
        if [ ! -z "$RUNNING_PID" ]; then
            echo "端口 $PORT 被进程 $RUNNING_PID 占用，正在结束该进程..."
            kill -9 $RUNNING_PID
            sleep 1
            echo "进程已结束"
        else
            echo "端口 $PORT 未被占用"
        fi
    elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        # Windows with Git Bash or similar
        echo "检测到 Windows 系统"
        netstat -ano | findstr ":$PORT" > temp.txt
        if [ -s temp.txt ]; then
            PID=$(awk '{print $5}' temp.txt | head -1)
            echo "端口 $PORT 被进程 $PID 占用，正在结束该进程..."
            taskkill /F /PID $PID
            sleep 1
            echo "进程已结束"
        else
            echo "端口 $PORT 未被占用"
        fi
        rm -f temp.txt
    fi

    # 最终确认端口是否可用
    if nc -z localhost $PORT 2>/dev/null; then
        echo "警告：端口 $PORT 仍然被占用，应用可能无法正常启动"
    else
        echo "端口 $PORT 已成功释放，可以启动应用"
    fi
}

# 检查Node.js
check_node

# 清理构建进程
clean_build_processes

# 清理锁文件
clean_lock_files

# 执行端口清理
clean_port

# 复制kill-port.mjs到src-tauri目录确保两处都有脚本
if [ -f "kill-port.mjs" ] && [ -d "src-tauri" ]; then
    cp kill-port.mjs src-tauri/
    echo "已复制kill-port.mjs到src-tauri目录"
fi

# 启动应用
echo "正在启动应用..."

# 使用找到的Node.js路径运行npm命令
if command -v npm >/dev/null 2>&1; then
    npm run tauri dev
else
    # 如果npm命令不可用，尝试使用Node.js路径下的npm
    NPM_PATH="$(dirname "$NODE_PATH")/npm"
    if [ -x "$NPM_PATH" ]; then
        "$NPM_PATH" run tauri dev
    else
        echo "错误: 找不到npm命令。请确保npm已安装。"
        exit 1
    fi
fi