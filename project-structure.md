# Taskers 项目结构文档

## 项目概述
Taskers 是一个基于 Tauri + React 的跨平台任务管理应用，采用前后端分离架构，后端使用 Rust 实现核心功能，前端使用 React + TypeScript 构建用户界面。

## 技术栈
### 前端
- React
- TypeScript
- Ant Design
- Tauri API

### 后端
- Rust
- SQLite (数据存储)
- <PERSON><PERSON> (跨平台框架)

## 项目结构

### 前端结构 (`/src`)
```
src/
├── App.tsx              # 应用主入口
├── App.css              # 主样式文件
├── main.tsx             # React 渲染入口
├── types.d.ts           # TypeScript 类型定义
├── components/          # React 组件
│   ├── Dashboard.tsx        # 仪表盘组件
│   ├── TaskManagement.tsx   # 任务管理组件
│   ├── ExecutorManagement.tsx # 执行器管理组件
│   ├── TaskForm.tsx         # 任务表单组件
│   ├── TaskList.tsx         # 任务列表组件
│   └── ErrorBoundary.tsx    # 错误边界组件
└── assets/              # 静态资源
```

### 后端结构 (`/src-tauri/src`)
```
src-tauri/src/
├── main.rs             # 主程序入口
├── db.rs               # 数据库管理模块
├── models.rs           # 数据模型定义
├── scheduler.rs        # 任务调度器
├── executor.rs         # 任务执行器
├── operation_log.rs    # 操作日志
└── web_server.rs       # Web 服务器
```

## 核心功能模块

### 1. 任务管理系统
- 任务的 CRUD 操作
- 任务状态管理
- 任务调度和执行

### 2. 执行器管理
- 执行器注册和管理
- 执行器状态监控
- 任务分发和执行

### 3. 用户界面
- 响应式布局
- 任务操作界面
- 执行器管理界面
- 仪表盘数据展示

## 数据流
1. 前端通过 Tauri 命令与后端通信
2. 后端处理请求并操作数据库
3. 数据变更通过事件系统通知前端更新

## 关键特性
1. 跨平台支持 (Windows, macOS, Linux)
2. 本地数据存储
3. 实时任务状态更新
4. 模块化架构设计

## 配置文件
- `tauri.conf.json`: Tauri 应用配置
- `package.json`: 前端依赖配置
- `Cargo.toml`: Rust 项目配置

## 开发工具
- VS Code
- Rust 工具链
- Node.js 开发环境
- Tauri CLI