{"name": "taskers", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.1", "antd": "^5.24.8", "dayjs": "^1.11.13", "monaco-editor": "^0.52.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-monaco-editor": "^0.58.0", "react-resizable": "^3.0.5", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-search": "^0.13.0", "xterm-addon-web-links": "^0.9.0"}, "devDependencies": {"@tauri-apps/cli": "^2.5.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-resizable": "^3.0.8", "@vitejs/plugin-react": "^4.4.1", "typescript": "^5.8.3", "vite": "^6.3.3"}}