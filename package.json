{"name": "taskers", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.2", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-search": "^0.15.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "antd": "^5.25.4", "dayjs": "^1.11.13", "monaco-editor": "^0.52.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-monaco-editor": "^0.58.0", "react-resizable": "^3.0.5"}, "devDependencies": {"@tauri-apps/cli": "^2.5.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/react-resizable": "^3.0.8", "@vitejs/plugin-react": "^4.5.0", "typescript": "^5.8.3", "vite": "^6.3.5"}}