/* VS Code 风格的项目管理页面样式 */
.vs-code-layout {
  --vs-background: #f5f5f5;
  --vs-border: #e8e8e8;
  --vs-header-bg: #e8e8e8;
  --vs-hover: #e6f7ff;
  --vs-selected: #1890ff;
  --vs-text: #333;
  --vs-text-secondary: #666;
}

/* 暗色主题 */
.dark-theme .vs-code-layout {
  --vs-background: #252526;
  --vs-border: #1e1e1e;
  --vs-header-bg: #333333;
  --vs-hover: #2a2d2e;
  --vs-selected: #094771;
  --vs-text: #cccccc;
  --vs-text-secondary: #999999;
}

.vs-code-layout .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: var(--vs-header-bg);
  border-bottom: 1px solid var(--vs-border);
}

.vs-code-layout .section-title {
  font-weight: bold;
  font-size: 14px;
  color: var(--vs-text);
}

.vs-code-layout .section-content {
  overflow: auto;
  transition: all 0.3s ease-in-out;
}

.vs-code-layout .section-content.collapsed {
  max-height: 0;
  overflow: hidden;
}

/* 侧边栏样式 */
.vs-code-layout .ant-layout-sider {
  background-color: var(--vs-background);
  border-right: 1px solid var(--vs-border);
}

/* 项目列表样式 */
.vs-code-layout .project-item {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 4px;
  transition: all 0.2s;
}

.vs-code-layout .project-item:hover {
  background-color: var(--vs-hover);
}

.vs-code-layout .project-item.selected {
  background-color: var(--vs-hover);
  border-left: 3px solid var(--vs-selected);
}

/* 文件浏览器样式 */
.vs-code-layout .file-explorer-container {
  height: 100%;
}

/* 编辑器样式 */
.vs-code-layout .editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 终端样式 */
.vs-code-layout .terminal-container {
  height: 100%;
}

/* 折叠动画 */
.vs-code-layout .collapsible-section {
  transition: all 0.3s ease-in-out;
}

/* 折叠按钮样式 */
.explorer-toggle-btn {
  z-index: 100;
}

.vs-code-layout .ant-layout-sider.ant-layout-sider-collapsed {
  overflow: visible !important;
}

.vs-code-layout .ant-layout-sider.ant-layout-sider-collapsed .explorer-toggle-btn {
  position: relative;
  left: 0;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .vs-code-layout .ant-layout-sider {
    position: absolute;
    z-index: 10;
    height: 100%;
    left: 0;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  }

  .vs-code-layout .ant-layout-sider.ant-layout-sider-collapsed {
    left: 0;
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
    flex: 0 0 40px !important;
  }
}
