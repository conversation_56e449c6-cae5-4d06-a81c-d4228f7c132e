/* 树形文件列表样式 */
.tree-view-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
  height: 100%;
  overflow: auto;
}

/* 树节点样式 */
.tree-node {
  display: flex;
  align-items: center;
  padding: 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 树节点图标 */
.tree-node-icon {
  margin-right: 4px;
  display: flex;
  align-items: center;
}

/* 树节点名称 */
.tree-node-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 目录节点样式 */
.tree-directory {
  font-weight: 500;
}

/* 文件节点样式 */
.tree-file {
  font-weight: normal;
}

/* 自定义Ant Design Tree组件样式 */
.custom-tree.ant-tree {
  background: transparent;
}

.custom-tree .ant-tree-treenode {
  padding: 0 0 2px 0;
  width: 100%;
  position: relative;
}

.custom-tree .ant-tree-node-content-wrapper {
  padding: 0 4px;
  border-radius: 2px;
  transition: background-color 0.2s;
}

.custom-tree .ant-tree-node-content-wrapper:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.custom-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: rgba(24, 144, 255, 0.1);
}

/* 树节点连接线样式 */
.custom-tree .ant-tree-indent-unit {
  position: relative;
  width: 16px;
  height: 100%;
}

/* 垂直连接线 */
.custom-tree .ant-tree-indent-unit::before {
  content: '';
  position: absolute;
  top: 0;
  right: 8px;
  bottom: 0;
  border-right: 1px dotted #d9d9d9;
  height: 100%;
}

/* 最后一个节点的特殊处理 */
.custom-tree .ant-tree-treenode-leaf-last .ant-tree-indent-unit::before {
  height: 14px;
}

/* 水平连接线 */
.custom-tree .ant-tree-switcher::after {
  content: '';
  position: absolute;
  top: 12px;
  left: -8px;
  width: 8px;
  height: 1px;
  border-top: 1px dotted #d9d9d9;
}

/* 展开/折叠按钮样式 */
.custom-tree .ant-tree-switcher {
  position: relative;
  width: 16px;
  height: 24px;
  line-height: 24px;
  background: transparent;
  text-align: center;
}

/* 展开/折叠图标样式 */
.custom-tree .ant-tree-switcher-icon {
  font-size: 10px;
  color: #666;
}

/* 自定义树节点样式 */
.custom-tree .ant-tree-title {
  display: flex;
  align-items: center;
  position: relative;
}

/* 文件/文件夹前的连接线 */
.custom-tree .tree-node::before {
  content: '';
  position: absolute;
  left: -16px;
  top: 50%;
  width: 8px;
  height: 1px;
  border-top: 1px dotted #d9d9d9;
}

/* 文件类型特定样式 */
.tree-file-js .tree-node-name {
  color: #f7df1e;
}

.tree-file-html .tree-node-name {
  color: #e44d26;
}

.tree-file-css .tree-node-name {
  color: #1a72b8;
}

.tree-file-json .tree-node-name {
  color: #ffc107;
}

.tree-file-md .tree-node-name {
  color: #42a5f5;
}

.tree-file-py .tree-node-name {
  color: #3572a5;
}

.tree-file-rs .tree-node-name {
  color: #dea584;
}

.tree-file-go .tree-node-name {
  color: #00add8;
}

.tree-file-java .tree-node-name {
  color: #b07219;
}

.tree-file-cpp .tree-node-name {
  color: #f34b7d;
}

/* 树形结构连接线 */
.tree-line {
  position: absolute;
  left: 0;
  width: 1px;
  height: 100%;
  background-color: #e8e8e8;
}

/* 树形结构水平线 */
.tree-line-horizontal {
  position: absolute;
  left: 0;
  width: 8px;
  height: 1px;
  background-color: #e8e8e8;
}

/* 树形结构折叠/展开图标 */
.tree-expand-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #999;
}

.tree-expand-icon:hover {
  color: #333;
}
