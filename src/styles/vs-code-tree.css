/* VS Code 风格的树形结构样式 */
.vs-code-tree.ant-tree {
  background-color: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 13px;
}

/* 树节点 */
.vs-code-tree .ant-tree-treenode {
  padding: 0;
  position: relative;
  width: 100%;
  margin: 0;
  white-space: nowrap;
}

/* 树节点内容 */
.vs-code-tree .ant-tree-node-content-wrapper {
  padding: 0 2px;
  height: 20px;
  line-height: 20px;
  transition: background-color 0.2s;
  margin: 0;
}

/* 树节点悬停效果 */
.vs-code-tree .ant-tree-node-content-wrapper:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 选中的树节点 */
.vs-code-tree .ant-tree-node-selected {
  background-color: rgba(0, 120, 215, 0.1) !important;
}

/* 树节点缩进 */
.vs-code-tree .ant-tree-indent {
  position: relative;
}

/* 树节点缩进单元 */
.vs-code-tree .ant-tree-indent-unit {
  width: 12px;
  position: relative;
}

/* 垂直连接线 */
.vs-code-tree .ant-tree-indent-unit::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 6px;
  border-right: 1px dotted #d9d9d9;
  height: 100%;
}

/* 最后一个节点的垂直连接线 */
.vs-code-tree .ant-tree-treenode-leaf-last .ant-tree-indent-unit::before {
  height: 10px;
}

/* 树形结构连接线 */
.vs-code-tree .ant-tree-show-line .ant-tree-indent-unit::before {
  border-right: 1px dotted #d9d9d9;
}

/* 水平连接线 */
.vs-code-tree .ant-tree-switcher::after {
  content: '';
  position: absolute;
  top: 10px;
  left: -6px;
  width: 6px;
  height: 1px;
  border-top: 1px dotted #d9d9d9;
}

/* 展开/折叠按钮 */
.vs-code-tree .ant-tree-switcher {
  width: 16px;
  height: 20px;
  line-height: 20px;
  position: relative;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 水平连接线 */
.vs-code-tree .ant-tree-switcher::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 0;
  width: 6px;
  height: 1px;
  border-top: 1px dotted #d9d9d9;
}

/* 展开/折叠图标 */
.vs-code-tree .ant-tree-switcher-icon {
  font-size: 8px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 文件/文件夹图标 */
.vs-code-tree .ant-tree-iconEle {
  width: 16px;
  height: 20px;
  line-height: 20px;
  margin-right: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* 文件夹图标 */
.vs-code-tree .ant-tree-iconEle .anticon-folder,
.vs-code-tree .ant-tree-iconEle .anticon-folder-open {
  color: #dcb67a;
}

/* 文件图标 */
.vs-code-tree .ant-tree-iconEle .anticon-file {
  color: #6a9955;
}

/* 文件/文件夹名称 */
.vs-code-tree .ant-tree-title {
  display: inline-flex;
  align-items: center;
  height: 22px;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 复选框样式 */
.vs-code-tree .ant-tree-checkbox {
  margin-right: 4px;
}

/* 树形连接线 */
.vs-code-tree.ant-tree-show-line .ant-tree-indent-unit::before {
  border-right: 1px dotted #d9d9d9;
}

/* 自定义节点样式 */
.vs-code-tree .tree-node {
  display: flex;
  align-items: center;
  height: 22px;
  line-height: 22px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 节点图标 */
.vs-code-tree .tree-node-icon {
  margin-right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 节点名称 */
.vs-code-tree .tree-node-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 目录节点 */
.vs-code-tree .tree-directory .tree-node-name {
  font-weight: normal;
  color: #333;
}

/* 文件节点 */
.vs-code-tree .tree-file .tree-node-name {
  font-weight: normal;
  color: #333;
}

/* 文件浏览器搜索框 */
.file-explorer-search {
  margin-bottom: 8px;
}

.file-explorer-search .ant-input-affix-wrapper {
  border-radius: 4px;
  border-color: #d9d9d9;
}

.file-explorer-search .ant-input-affix-wrapper:hover,
.file-explorer-search .ant-input-affix-wrapper:focus,
.file-explorer-search .ant-input-affix-wrapper-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.file-explorer-search .ant-input {
  font-size: 13px;
}

.file-explorer-search .ant-input-prefix {
  margin-right: 8px;
}

.file-explorer-search .ant-input-suffix {
  margin-left: 8px;
}

/* 搜索结果高亮 */
.search-highlight {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 0 1px;
  border-radius: 2px;
}
