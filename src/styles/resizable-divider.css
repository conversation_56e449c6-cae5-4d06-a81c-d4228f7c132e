/* 可调整大小的分隔线样式 */
.resizable-divider {
  position: relative;
  background-color: #e8e8e8;
  transition: background-color 0.2s;
}

/* 水平分隔线 */
.resizable-divider.horizontal {
  width: 100%;
  height: 5px;
  cursor: row-resize;
}

/* 垂直分隔线 */
.resizable-divider.vertical {
  width: 5px;
  height: 100%;
  cursor: col-resize;
}

/* 悬停效果 */
.resizable-divider:hover,
.resizable-divider.resizing {
  background-color: #1890ff;
}

/* 拖动手柄 */
.resizable-handle {
  position: absolute;
  z-index: 100;
}

/* 水平拖动手柄 */
.resizable-handle.horizontal {
  left: 0;
  right: 0;
  height: 5px;
  cursor: row-resize;
}

/* 垂直拖动手柄 */
.resizable-handle.vertical {
  top: 0;
  bottom: 0;
  width: 5px;
  cursor: col-resize;
}

/* 拖动时的遮罩层，防止选中文本 */
.resize-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  cursor: col-resize;
}

.resize-overlay.row-resize {
  cursor: row-resize;
}

/* 分隔线中间的点线 */
.resizable-divider::after {
  content: '';
  position: absolute;
  background-color: #ccc;
}

/* 水平分隔线中间的点线 */
.resizable-divider.horizontal::after {
  left: 50%;
  top: 1px;
  width: 30px;
  height: 3px;
  margin-left: -15px;
  border-radius: 1.5px;
}

/* 垂直分隔线中间的点线 */
.resizable-divider.vertical::after {
  top: 50%;
  left: 1px;
  height: 30px;
  width: 3px;
  margin-top: -15px;
  border-radius: 1.5px;
}

/* VS Code 风格的分隔线 */
.vs-code-divider {
  background-color: #e8e8e8;
  position: relative;
  z-index: 10;
}

.vs-code-divider.horizontal {
  height: 5px;
  width: 100%;
  cursor: row-resize;
  margin: -2px 0;
}

.vs-code-divider.vertical {
  width: 5px;
  height: 100%;
  cursor: col-resize;
  margin: 0 -2px;
}

.vs-code-divider:hover,
.vs-code-divider.resizing {
  background-color: #1890ff;
}

/* 分隔线拖动手柄 */
.vs-code-divider-handle {
  position: absolute;
  background-color: transparent;
  z-index: 11;
}

.vs-code-divider-handle.horizontal {
  height: 9px;
  width: 100%;
  top: -2px;
  cursor: row-resize;
}

.vs-code-divider-handle.vertical {
  width: 9px;
  height: 100%;
  left: -2px;
  cursor: col-resize;
}

/* 分隔线中间的点 */
.vs-code-divider::before {
  content: '';
  position: absolute;
  background-color: #999;
  z-index: 12;
}

/* 水平分隔线中间的点 */
.vs-code-divider.horizontal::before {
  width: 30px;
  height: 1px;
  top: 2px;
  left: 50%;
  transform: translateX(-50%);
}

/* 垂直分隔线中间的点 */
.vs-code-divider.vertical::before {
  width: 1px;
  height: 30px;
  left: 2px;
  top: 50%;
  transform: translateY(-50%);
}

/* 暗色主题 */
.dark-theme .resizable-divider {
  background-color: #333;
}

.dark-theme .resizable-divider:hover,
.dark-theme .resizable-divider.resizing {
  background-color: #1890ff;
}

.dark-theme .resizable-divider::after {
  background-color: #555;
}

.dark-theme .vs-code-divider {
  background-color: #333;
}

.dark-theme .vs-code-divider:hover,
.dark-theme .vs-code-divider.resizing {
  background-color: #1890ff;
}
