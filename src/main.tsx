import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './styles.css';
import './styles/file-icons.css';
import './styles/project-management.css';
import 'antd/dist/reset.css';
import ErrorBoundary from './components/ErrorBoundary';

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  // 暂时移除 StrictMode 以隐藏 Ant Design 组件的 findDOMNode 警告
  // <React.StrictMode>
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  // </React.StrictMode>,
);