declare module '*.css' {
    const content: { [className: string]: string };
    export default content;
  }
 
   // Define ProjectStatus type matching the backend enum
   export type ProjectStatus = 'Active' | 'Inactive' | 'Completed';

   // Define TestCaseStatus type matching the backend enum
   export type TestCaseStatus = 'Not Started' | 'In Progress' | 'Passed' | 'Failed' | 'Blocked';

   // Define TestCasePriority type matching the backend enum
   export type TestCasePriority = 'High' | 'Medium' | 'Low';

   // Define chat related types
   export interface ChatMessage {
     id: string;
     role: 'user' | 'assistant';
     content: string;
     created_at: string;
     conversation_id: string;
   }

   export interface ChatConversation {
     id: string;
     title: string;
     model: string;
     provider: string;
     created_at: string;
   }

   export interface ApiConfig {
     provider: string;
     api_key?: string;
     base_url?: string;
     models?: string[];
   }
