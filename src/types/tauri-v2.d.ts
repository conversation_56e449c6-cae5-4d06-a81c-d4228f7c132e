// Tauri v2 API 类型声明

// Core API
declare module '@tauri-apps/api/core' {
  export function invoke<T>(cmd: string, args?: Record<string, unknown>): Promise<T>;
}

// Window API
declare module '@tauri-apps/api/window' {
  export class Window {
    constructor(label: string);
    center(): Promise<void>;
    onResized(handler: () => void | Promise<void>): Promise<() => void>;
  }
}

// Dialog API
declare module '@tauri-apps/api/dialog' {
  export function open(options?: {
    directory?: boolean;
    multiple?: boolean;
    title?: string;
    filters?: Array<{
      name: string;
      extensions: string[];
    }>;
  }): Promise<string | string[] | null>;
}

// Event API
declare module '@tauri-apps/api/event' {
  export type EventCallback<T> = (event: { payload: T }) => void;
  export type UnlistenFn = () => void;
  export function listen<T>(event: string, handler: EventCallback<T>): Promise<UnlistenFn>;
  export function once<T>(event: string): Promise<T>;
  export function emit(event: string, payload?: unknown): Promise<void>;
}
