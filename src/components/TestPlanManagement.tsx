import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Layout,
  Tag,
  Progress,
  DatePicker,
  Row,
  Col,
  Transfer,
} from 'antd';
import type { TransferProps } from 'antd';
import ModuleTree from './ModuleTree';
import { PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { TestCaseStatus, TestCasePriority } from '../types'; // Import types

interface TestCase {
  id: string;
  name: string;
  module_id: string;
  priority: TestCasePriority; // 使用枚举类型
  status: TestCaseStatus;    // 使用枚举类型
  is_automated: boolean;
}

interface TestPlan {
  id: string;
  name: string;
  description?: string;
  status: string;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  project_id: string;
  test_cases: string[];
  progress: number;
  executor?: string;
  module_id?: string;
}

const TestPlanManagement: React.FC = () => {
  const [testPlans, setTestPlans] = useState<TestPlan[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTestPlan, setEditingTestPlan] = useState<TestPlan | null>(null);
  const [form] = Form.useForm();
  const [selectedModuleId, setSelectedModuleId] = useState<string>('');
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [selectedTestCases, setSelectedTestCases] = useState<string[]>([]);
  const [isTestCaseModalVisible, setIsTestCaseModalVisible] = useState(false);
  const [currentTestPlan, setCurrentTestPlan] = useState<TestPlan | null>(null);

  const fetchTestPlans = async () => {
    try {
      const planList = await invoke('get_test_plans', { moduleId: selectedModuleId }) as TestPlan[];
      setTestPlans(planList);
    } catch (error) {
      message.error('获取测试计划失败：' + error);
    }
  };

  const fetchTestCases = async () => {
    try {
      const caseList = await invoke('get_test_cases', { moduleId: selectedModuleId }) as TestCase[];
      setTestCases(caseList);
    } catch (error) {
      message.error('获取测试用例失败：' + error);
    }
  };

  const handleModuleSelect = (moduleId: string) => {
    setSelectedModuleId(moduleId);
  };

  const handleTestCaseSelect = async (testPlan: TestPlan) => {
    setCurrentTestPlan(testPlan);
    setSelectedTestCases(testPlan.test_cases || []);
    await fetchTestCases();
    setIsTestCaseModalVisible(true);
  };

  const handleTestCaseModalOk = async () => {
    if (currentTestPlan) {
      try {
        await invoke('update_test_plan', {
          testPlan: {
            ...currentTestPlan,
            test_cases: selectedTestCases,
            updated_at: new Date().toISOString()
          }
        });
        message.success('更新测试用例关联成功');
        setIsTestCaseModalVisible(false);
        fetchTestPlans();
      } catch (error) {
        message.error('更新测试用例关联失败：' + error);
      }
    }
  };

  useEffect(() => {
    if (selectedModuleId) {
      fetchTestPlans();
      fetchTestCases();
    }
  }, [selectedModuleId]);

  const handleCreate = () => {
    setEditingTestPlan(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record: TestPlan) => {
    setEditingTestPlan(record);
    form.setFieldsValue({
      ...record,
      start_date: record.start_date ? new Date(record.start_date) : null,
      end_date: record.end_date ? new Date(record.end_date) : null,
    });
    setIsModalVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await invoke('delete_test_plan', { id });
      message.success('删除成功');
      fetchTestPlans();
    } catch (error) {
      message.error('删除失败：' + error);
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const formattedValues = {
        ...values,
        start_date: values.start_date?.toISOString(),
        end_date: values.end_date?.toISOString(),
      };

      if (editingTestPlan) {
        await invoke('update_test_plan', {
          testPlan: {
            ...editingTestPlan,
            ...formattedValues,
            updated_at: new Date().toISOString()
          }
        });
        message.success('更新成功');
      } else {
        await invoke('create_test_plan', {
          testPlan: {
            ...formattedValues,
            id: crypto.randomUUID(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            created_by: 'System',
            status: 'Not Started',
            progress: 0
          }
        });
        message.success('创建成功');
      }
      setIsModalVisible(false);
      fetchTestPlans();
    } catch (error) {
      message.error('操作失败：' + error);
    }
  };

  const columns: ColumnsType<TestPlan> = [
    {
      title: '计划名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = 
          status === 'Completed' ? 'green' :
          status === 'In Progress' ? 'blue' :
          status === 'Not Started' ? 'default' : 'red';
        return <Tag color={color}>{status}</Tag>;
      }
    },
    {
      title: '执行进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      )
    },
    {
      title: '开始日期',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '-',
    },
    {
      title: '结束日期',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '-',
    },
    {
      title: '执行人',
      dataIndex: 'executor',
      key: 'executor',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
          <Button type="link" onClick={() => handleTestCaseSelect(record)}>关联用例</Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>删除</Button>
        </Space>
      ),
    },
  ];

  return (
    <Layout style={{ background: '#fff', minHeight: 'calc(100vh - 112px)' }}>
      <Row>
        <Col span={4} style={{ borderRight: '1px solid #f0f0f0' }}>
          <ModuleTree onSelect={handleModuleSelect} />
        </Col>
        <Col span={20} style={{ padding: '24px' }}>
          <div style={{ marginBottom: '16px' }}>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleCreate}
            >
              新建测试计划
            </Button>
          </div>

          <Table
            columns={columns}
            dataSource={testPlans}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />

          <Modal
            title={editingTestPlan ? '编辑测试计划' : '新建测试计划'}
            open={isModalVisible}
            onOk={handleModalOk}
            onCancel={() => setIsModalVisible(false)}
            width={800}
          >
            <Form
              form={form}
              layout="vertical"
            >
              <Form.Item
                name="name"
                label="计划名称"
                rules={[{ required: true, message: '请输入计划名称' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="description"
                label="计划描述"
              >
                <Input.TextArea rows={3} />
              </Form.Item>

              <Form.Item
                name="status"
                label="状态"
                initialValue="Not Started"
              >
                <Select>
                  <Select.Option value="Not Started">未开始</Select.Option>
                  <Select.Option value="In Progress">进行中</Select.Option>
                  <Select.Option value="Completed">已完成</Select.Option>
                  <Select.Option value="Blocked">已阻塞</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="start_date"
                label="开始日期"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="end_date"
                label="结束日期"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="executor"
                label="执行人"
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="progress"
                label="执行进度"
                initialValue={0}
              >
                <Select>
                  <Select.Option value={0}>0%</Select.Option>
                  <Select.Option value={25}>25%</Select.Option>
                  <Select.Option value={50}>50%</Select.Option>
                  <Select.Option value={75}>75%</Select.Option>
                  <Select.Option value={100}>100%</Select.Option>
                </Select>
              </Form.Item>
            </Form>
          </Modal>

          <Modal
            title="关联测试用例"
            open={isTestCaseModalVisible}
            onOk={handleTestCaseModalOk}
            onCancel={() => setIsTestCaseModalVisible(false)}
            width={800}
          >
            <Transfer
              dataSource={testCases.map(item => ({
                key: item.id,
                title: item.name,
                description: `优先级: ${item.priority}, 状态: ${item.status}`
              }))}
              titles={['可选用例', '已选用例']}
              targetKeys={selectedTestCases}
              onChange={(targetKeys) => setSelectedTestCases(targetKeys.map(key => key.toString()))} // Simplified onChange signature
              render={item => item.title}
              listStyle={{
                width: 300,
                height: 400,
              }}
            />
          </Modal>
        </Col>
      </Row>
    </Layout>
  );
};

export default TestPlanManagement;