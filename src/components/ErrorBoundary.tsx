import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button, Result, Typography, Card } from 'antd';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  };

  public static getDerivedStateFromError(error: Error): State {
    // 更新状态使下一次渲染能够显示错误界面
    return { 
      hasError: true,
      error,
      errorInfo: null
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('Uncaught error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  private handleReset = () => {
    // 重置状态，尝试恢复应用
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  }

  private handleReload = () => {
    // 刷新页面
    window.location.reload();
  }

  public render() {
    if (this.state.hasError) {
      // 自定义错误展示界面
      return (
        <div style={{ height: '100vh', display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '20px' }}>
          <Result
            status="error"
            title="应用发生错误"
            subTitle="请尝试重置应用或刷新页面"
            extra={[
              <Button key="reset" onClick={this.handleReset}>
                重置应用
              </Button>,
              <Button key="reload" type="primary" onClick={this.handleReload}>
                刷新页面
              </Button>,
            ]}
          >
            {this.state.error && (
              <Card title="错误详情" style={{ marginTop: 20 }}>
                <Typography.Paragraph>
                  <strong>错误类型：</strong> {this.state.error.name}
                </Typography.Paragraph>
                <Typography.Paragraph>
                  <strong>错误信息：</strong> {this.state.error.message}
                </Typography.Paragraph>
                {this.state.error.stack && (
                  <Typography.Paragraph>
                    <details>
                      <summary>错误堆栈</summary>
                      <pre style={{ whiteSpace: 'pre-wrap', maxHeight: '200px', overflow: 'auto' }}>
                        {this.state.error.stack}
                      </pre>
                    </details>
                  </Typography.Paragraph>
                )}
              </Card>
            )}
          </Result>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
