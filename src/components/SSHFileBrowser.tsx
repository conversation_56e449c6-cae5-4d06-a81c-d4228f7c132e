import React, { useState, useEffect, useCallback } from 'react';
import { Tree, Input, Button, Space, message, Spin, Tooltip, Select } from 'antd';
import { FolderOutlined, FileOutlined, ReloadOutlined, UploadOutlined, DownloadOutlined } from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';
import type { DirectoryTreeProps } from 'antd/es/tree';

interface FileNode {
  title: string;
  key: string; // Use path as key
  isLeaf: boolean;
  children?: FileNode[];
  path: string; // Full path
}

interface SSHFileBrowserProps {
  agentId: string | null;
  isConnected: boolean;
  initialPath?: string;
}

const { DirectoryTree } = Tree;
const { Search } = Input;
const { Option } = Select;

const SSHFileBrowser: React.FC<SSHFileBrowserProps> = ({ agentId, isConnected, initialPath = '/' }) => {
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [currentPath, setCurrentPath] = useState<string>(initialPath);
  const [inputPath, setInputPath] = useState<string>(initialPath);
  const [loading, setLoading] = useState<boolean>(false);
  const [rootDirectories, setRootDirectories] = useState<string[]>(['/', '/home', '/tmp', '/usr', '/var']); // Default common directories
  const [selectedRootDir, setSelectedRootDir] = useState<string>(initialPath);

  const fetchFiles = useCallback(async (path: string) => {
    if (!agentId || !isConnected) {
      // message.warn('SSH未连接，无法浏览文件');
      setFileTree([]); // Clear tree if not connected
      return;
    }
    setLoading(true);
    setInputPath(path); // Update input path as well
    try {
      console.log(`[FileBrowser Debug] Fetching files for path: ${path}`);
      const files = await invoke<any[]>('list_remote_files', { agentId, path });
      console.log(`[FileBrowser Debug] Received files:`, files);

      const treeData = files.map((file): FileNode => ({
        title: file.name,
        key: file.path, // Use full path as key
        isLeaf: !file.is_directory,
        path: file.path,
        children: file.is_directory ? [] : undefined, // Initialize children for directories
      }));

      // If fetching root, replace the tree, otherwise update children (logic needed)
      // For simplicity now, always replace if path is root, otherwise assume it's an update
      if (path === selectedRootDir) {
        setFileTree(treeData);
      } else {
        // Basic update logic (needs improvement for deep updates)
        const updateNode = (nodes: FileNode[]): FileNode[] => {
            return nodes.map(node => {
                if (node.key === path && !node.isLeaf) {
                    return { ...node, children: treeData };
                }
                if (node.children) {
                    return { ...node, children: updateNode(node.children) };
                }
                return node;
            });
        };
        setFileTree(prevTree => updateNode(prevTree));
      }

      setCurrentPath(path);
      message.success(`加载目录 ${path} 成功`);
    } catch (error) {
      console.error(`[FileBrowser Debug] 获取文件列表失败 (${path}):`, error);
      message.error(`获取文件列表失败 (${path}): ${error}`);
      // Optionally clear tree or show error node
      // setFileTree([]);
    } finally {
      setLoading(false);
    }
  }, [agentId, isConnected, selectedRootDir]);

  // Fetch root directories on connect/agent change
  useEffect(() => {
    const fetchRoots = async () => {
      if (agentId && isConnected) {
        try {
          // Assuming a command exists to get common root dirs or user home
          // const roots = await invoke<string[]>('get_remote_root_dirs', { agentId });
          // setRootDirectories(roots);
          // setSelectedRootDir(roots[0] || '/');
          // For now, use defaults and fetch initial path
          setSelectedRootDir(initialPath);
          fetchFiles(initialPath);
        } catch (error) {
          console.error('[FileBrowser Debug] 获取根目录失败:', error);
          // Fallback to defaults
          setSelectedRootDir(initialPath);
          fetchFiles(initialPath);
        }
      }
    };
    fetchRoots();
  }, [agentId, isConnected, fetchFiles, initialPath]);

  const onSelect: DirectoryTreeProps['onSelect'] = (keys, info) => {
    console.log('Trigger Select', keys, info);
    // Handle file selection (e.g., open file, download)
    if (info.node.isLeaf) {
        message.info(`选择了文件: ${info.node.path}`);
        // Add download logic here
    }
  };

  const onExpand: DirectoryTreeProps['onExpand'] = async (keys, info) => {
    console.log('Trigger Expand', keys, info);
    if (!info.node.isLeaf && info.expanded) {
        // Only fetch if expanding and children haven't been loaded
        if (!info.node.children || info.node.children.length === 0) {
             await fetchFiles(info.node.path);
        }
    }
  };

  const handlePathChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputPath(e.target.value);
  };

  const handleGoToPath = () => {
    fetchFiles(inputPath);
  };

  const handleRefresh = () => {
    fetchFiles(currentPath);
  };

  const handleUpload = () => {
    if (!agentId || !isConnected) {
        message.warn('SSH未连接，无法上传文件');
        return;
    }
    message.info('上传功能待实现');
    // Add upload logic here, likely using tauri dialogs and invoke
  };

  const handleDownload = () => {
     if (!agentId || !isConnected) {
        message.warn('SSH未连接，无法下载文件');
        return;
    }
    message.info('下载功能待实现');
    // Add download logic here based on selected file
  };

  const handleRootDirChange = (value: string) => {
    setSelectedRootDir(value);
    fetchFiles(value);
  };

  return (
    <Spin spinning={loading} tip="加载文件中...">
      <div style={{ padding: '10px', height: '100%', display: 'flex', flexDirection: 'column' }}>
        <Space direction="vertical" style={{ marginBottom: '10px', width: '100%' }}>
            <Space.Compact style={{ width: '100%' }}>
                 <Select value={selectedRootDir} onChange={handleRootDirChange} style={{ width: '120px' }}>
                    {rootDirectories.map(dir => (
                        <Option key={dir} value={dir}>{dir}</Option>
                    ))}
                </Select>
                <Input
                    placeholder="输入路径"
                    value={inputPath}
                    onChange={handlePathChange}
                    onPressEnter={handleGoToPath}
                />
                <Button onClick={handleGoToPath}>跳转</Button>
                <Tooltip title="刷新当前目录">
                    <Button icon={<ReloadOutlined />} onClick={handleRefresh} />
                </Tooltip>
            </Space.Compact>
             <Space>
                <Button icon={<UploadOutlined />} onClick={handleUpload}>上传</Button>
                <Button icon={<DownloadOutlined />} onClick={handleDownload}>下载</Button>
            </Space>
        </Space>
        <div style={{ flexGrow: 1, overflow: 'auto', border: '1px solid #f0f0f0', borderRadius: '4px' }}>
          {fileTree.length > 0 ? (
            <DirectoryTree
              multiple
              defaultExpandAll={false} // Don't expand all by default
              onSelect={onSelect}
              onExpand={onExpand}
              treeData={fileTree}
              showIcon
              icon={({ isLeaf }) => (isLeaf ? <FileOutlined /> : <FolderOutlined />)}
              style={{ padding: '5px' }}
              loadData={async (node) => {
                  if (!node.isLeaf) {
                      await fetchFiles(node.path);
                  }
              }}
            />
          ) : (
            <div style={{ textAlign: 'center', padding: '20px', color: '#aaa' }}>
              {isConnected ? '目录为空或加载失败' : '未连接'}
            </div>
          )}
        </div>
      </div>
    </Spin>
  );
};

export default SSHFileBrowser;