import React, { useEffect, useState } from 'react';
import { invoke } from '@tauri-apps/api/core';

interface Task {
  id: string;
  name: string;
  description: string;
  task_type: 'Manual' | 'Automated' | 'Custom';
  status: 'Unbuilt' | 'Pending' | 'Running' | 'Completed' | 'Failed';
  progress: number;
  steps: { name: string; command: string; order: number }[];
  working_dir?: string;
  schedule: string;
  scheduled_at?: string;
  created_at: string;
  updated_at: string;
  agent_id?: string;
  parent_id?: string;
  is_group: boolean;
  execution_mode?: 'Serial' | 'Parallel';
  child_tasks?: string[];
  test_cases?: string[];
  loop_count?: number;
  lastRun?: string;
  scheduleTime?: string;
  custom_command?: string;
}

const TaskList: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([]);

  useEffect(() => {
    invoke<Task[]>('get_tasks').then(setTasks);
  }, []);

  return (
    <div>
      <h2>Tasks</h2>
      <ul>
        {tasks.map(task => (
          <li key={task.id}>{task.name}</li>
        ))}
      </ul>
    </div>
  );
};

export default TaskList;
