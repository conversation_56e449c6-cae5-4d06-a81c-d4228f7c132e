.ssh-terminal-modal .ant-modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  background-color: #1e1e1e;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #333;
}

.ssh-terminal-modal .ant-modal-body {
  flex: 1;
  padding: 0;
  overflow: hidden;
  background-color: #1e1e1e;
}

.ssh-terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.ssh-terminal-controls {
  display: flex;
  gap: 8px;
}

.xterm {
  height: 100% !important;
  width: 100% !important;
  padding: 8px;
  background-color: #1e1e1e;
  display: flex !important;
  flex-direction: column !important;
}

.xterm-viewport {
  overflow-y: auto !important;
  background-color: #1e1e1e !important;
  flex: 1 !important;
}

/* 确保终端占满整个容器 */
.xterm-screen {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  visibility: visible !important;
}

/* 自定义滚动条样式 */
.xterm-viewport::-webkit-scrollbar {
  width: 8px;
}

.xterm-viewport::-webkit-scrollbar-track {
  background: #000000;
}

.xterm-viewport::-webkit-scrollbar-thumb {
  background-color: #555;
  border-radius: 4px;
}

.xterm-viewport::-webkit-scrollbar-thumb:hover {
  background-color: #777;
}

/* 标签页关闭按钮样式 */
.tab-close-icon {
  margin-left: 8px;
  color: rgba(255, 255, 255, 0.45);
  font-size: 12px;
  transition: all 0.3s;
}

.tab-close-icon:hover {
  color: rgba(255, 255, 255, 0.85);
}

/* xterm终端容器样式 */
#xterm-container {
  width: 100%;
  height: calc(100% - 56px);
  background-color: #000000;
  padding: 5px;
  overflow: hidden;
  position: relative;
}

/* 标签页内容区域样式 */
.ant-tabs-tabpane {
  height: calc(100vh - 180px);
  overflow: hidden;
}

/* 终端容器样式 */
.terminal-container {
  height: 100%;
  width: 100%;
  background-color: #1e1e1e;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  position: relative;
}

/* 确保Modal内容区域占满可用空间 */
.ant-modal-body {
  padding: 0 !important;
  height: 100%;
}

.ant-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.ssh-terminal-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
  overflow: hidden;
  position: relative;
}

.ssh-terminal-output {
  flex: 1;
  overflow: auto;
  padding: 12px;
  background-color: #1e1e1e;
  color: #f0f0f0;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  height: calc(100% - 40px);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  display: block;
  position: relative;
  z-index: 1;
  min-height: 300px;
  visibility: visible !important;
  border-radius: 4px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  overflow-y: scroll;
  border: 1px solid #333;
  transition: all 0.3s ease;
}

/* 确保SSH终端Modal独立显示 */
.ssh-terminal-modal-wrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  pointer-events: auto;
}

.ssh-terminal-modal {
  position: relative;
  margin: 20px auto;
  padding: 0;
  max-width: 90%;
  max-height: 90vh;
}

.ssh-terminal-modal .ant-modal-content {
  height: 90vh;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
}

.ssh-terminal-output pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  color: inherit;
}

/* xterm高亮显示相关样式 */
.ssh-terminal-output .ansi-black-fg { color: #3f3f3f; }
.ssh-terminal-output .ansi-red-fg { color: #ff5555; }
.ssh-terminal-output .ansi-green-fg { color: #50fa7b; }
.ssh-terminal-output .ansi-yellow-fg { color: #f1fa8c; }
.ssh-terminal-output .ansi-blue-fg { color: #bd93f9; }
.ssh-terminal-output .ansi-magenta-fg { color: #ff79c6; }
.ssh-terminal-output .ansi-cyan-fg { color: #8be9fd; }
.ssh-terminal-output .ansi-white-fg { color: #f8f8f2; }
.ssh-terminal-output .ansi-bright-black-fg { color: #6272a4; }
.ssh-terminal-output .ansi-bright-red-fg { color: #ff6e6e; }
.ssh-terminal-output .ansi-bright-green-fg { color: #69ff94; }
.ssh-terminal-output .ansi-bright-yellow-fg { color: #ffffa5; }
.ssh-terminal-output .ansi-bright-blue-fg { color: #d6acff; }
.ssh-terminal-output .ansi-bright-magenta-fg { color: #ff92df; }
.ssh-terminal-output .ansi-bright-cyan-fg { color: #a4ffff; }
.ssh-terminal-output .ansi-bright-white-fg { color: #ffffff; }

/* 终端工具栏样式 */
.ssh-terminal-toolbar {
  padding: 8px;
  background-color: #1e1e1e;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #333;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
}

.ssh-terminal-toolbar .ant-btn-text {
  color: #ffffff !important;
  background-color: #333333 !important;
}

.ssh-terminal-toolbar .ant-btn-text:hover {
  background-color: #444444 !important;
}

/* 终端光标行样式 */
.terminal-cursor-line {
  display: flex;
  white-space: nowrap;
  line-height: 1.5;
  margin-top: 4px;
  width: 100%;
  position: relative;
  z-index: 5;
}

.terminal-prompt {
  color: #4CAF50;
  font-weight: bold;
  padding: 0 8px;
  user-select: none;
}

/* 命令输入框样式优化 */
.terminal-input-area {
  position: relative;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  outline: none;
  color: #f0f0f0;
  caret-color: #4CAF50;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  background-color: #2d2d2d;
  border: 1px solid #444;
  border-radius: 4px;
  padding: 6px 12px;
  margin: 8px;
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.3s;
  z-index: 10;
}

.terminal-input-area:focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  background-color: #ffffff;
}

/* 终端输出区域样式优化 */
.ssh-terminal-output {
  flex: 1;
  overflow: auto;
  padding: 12px;
  background-color: #1e1e1e;
  color: #f0f0f0;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  border-radius: 4px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  margin-bottom: 8px;
}

/* ANSI颜色支持 */
.ssh-terminal-output .ansi-black-fg { color: #3f3f3f; }
.ssh-terminal-output .ansi-red-fg { color: #ff5555; }
.ssh-terminal-output .ansi-green-fg { color: #50fa7b; }
.ssh-terminal-output .ansi-yellow-fg { color: #f1fa8c; }
.ssh-terminal-output .ansi-blue-fg { color: #bd93f9; }
.ssh-terminal-output .ansi-magenta-fg { color: #ff79c6; }
.ssh-terminal-output .ansi-cyan-fg { color: #8be9fd; }
.ssh-terminal-output .ansi-white-fg { color: #f8f8f2; }
.ssh-terminal-output .ansi-bright-black-fg { color: #6272a4; }
.ssh-terminal-output .ansi-bright-red-fg { color: #ff6e6e; }
.ssh-terminal-output .ansi-bright-green-fg { color: #69ff94; }
.ssh-terminal-output .ansi-bright-yellow-fg { color: #ffffa5; }
.ssh-terminal-output .ansi-bright-blue-fg { color: #d6acff; }
.ssh-terminal-output .ansi-bright-magenta-fg { color: #ff92df; }
.ssh-terminal-output .ansi-bright-cyan-fg { color: #a4ffff; }
.ssh-terminal-output .ansi-bright-white-fg { color: #ffffff; }

/* 命令历史记录样式 */
.command-history {
  max-height: 200px;
  overflow-y: auto;
  background-color: #252525;
  border: 1px solid #333;
  border-radius: 4px;
  margin-top: 4px;
}

.command-history-item {
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.command-history-item:hover {
  background-color: #333;
}

.command-history-item.active {
  background-color: #4CAF50;
  color: #fff;
}

.ssh-terminal-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(30, 30, 30, 0.8);
  z-index: 100;
}

.terminal-container {
  position: relative;
  height: 100%;
  width: 100%;
  background-color: #1e1e1e;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

.terminal-container .xterm {
  opacity: 1;
  transition: opacity 0.3s ease;
}

.terminal-container.loading .xterm {
  opacity: 0.5;
}

.terminal-container:focus-within {
  border: 1px solid #4CAF50;
  box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

/* 标签页样式 */
.ssh-terminal-content .ant-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

.ssh-terminal-content .ant-tabs-content {
  flex: 1;
  height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #1e1e1e;
}

.ssh-terminal-content .ant-tabs-tabpane {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 8px;
  position: relative;
}

.ssh-terminal-content .ant-tabs-nav {
  margin-bottom: 0;
  background-color: #252525;
  border-bottom: 1px solid #333;
  padding: 4px 8px 0;
  user-select: none;
}

.ssh-terminal-content .ant-tabs-tab {
  color: #ccc;
  background-color: #252525;
  border-color: #333;
  padding: 4px 16px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.ssh-terminal-content .ant-tabs-tab:hover {
  color: #fff;
  background-color: #2d2d2d;
}

.ssh-terminal-content .ant-tabs-tab-active {
  background-color: #1e1e1e;
  border-bottom-color: #1e1e1e;
}

.ssh-terminal-content .ant-tabs-tab-btn {
  color: #ccc;
  transition: color 0.3s ease;
}

.ssh-terminal-content .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #fff;
  font-weight: 500;
}

.ssh-terminal-content .ant-tabs-ink-bar {
  background-color: #4CAF50;
  height: 2px;
}

.ssh-terminal-file-path-input {
  padding: 8px;
  background-color: #f5f5f5;
}

.ssh-terminal-root-selector {
  padding: 8px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
}