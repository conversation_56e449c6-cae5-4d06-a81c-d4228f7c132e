import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Tag, Modal, message, Form, Input, Select, Tooltip, Upload, Collapse } from 'antd';
import { PlusOutlined, DeleteOutlined, CopyOutlined, EditOutlined, PlayCircleOutlined, CodeOutlined, UploadOutlined, HistoryOutlined } from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';
import './CompactLayout.css';
import SSHTerminal from './SSHTerminal';


interface Agent {
  id: string;
  name: string;
  ip: string;
  username: string;
  password: string;
  work_dir: string;
  status: 'Online' | 'Offline' | 'Busy';
  last_seen: string;
  ping_latency?: number;
  last_ping?: string;
  labels?: string;
  description?: string;
  max_jobs?: number;
}

const { Panel } = Collapse;

interface AgentFormData {
  name: string;
  ip: string;
  username: string;
  password: string;
  work_dir: string;
  labels?: string;
  description?: string;
  max_jobs?: number;
}

const ExecutorManagement: React.FC = () => {
  const [selectedFiles, setSelectedFiles] = useState<any[]>([]);
  const [targetPath, setTargetPath] = useState('');
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);
  const [currentAgent, setCurrentAgent] = useState<Agent | null>(null);
  const [sshOutput, setSshOutput] = useState<string>('');
  const [form] = Form.useForm();
  const [isLogModalVisible, setIsLogModalVisible] = useState(false);
  const [showInlineTerminal, setShowInlineTerminal] = useState(false);
  
  // 关闭内联SSH终端的处理函数在下方已定义，这里删除重复声明

  const [operationLogs, setOperationLogs] = useState<Array<{
    id: string;
    agent_id: string;
    operation_type: 'command' | 'file_transfer' | 'ssh' | 'other';
    description: string;
    status: 'success' | 'failed';
    created_at: string;
  }>>([]);

  // Agent接口已在文件顶部定义

  useEffect(() => {
    // 初始化时获取执行机列表和操作记录
    fetchAgents();
    fetchOperationLogs();

    // 启动自动ping检测和状态更新
    const interval = setInterval(async () => {
      try {
        await invoke('ping_agents');
        await fetchAgents();
      } catch (error) {
        console.error('Failed to ping agents or update status:', error);
      }
    }, 5000);

    // 每30秒更新一次操作记录
    const logsInterval = setInterval(fetchOperationLogs, 30000);

    return () => {
      clearInterval(interval);
      clearInterval(logsInterval);
    };
  }, []);

  const fetchOperationLogs = async () => {
    try {
      const logs = await invoke<typeof operationLogs>('get_operation_logs');
      setOperationLogs(logs);
    } catch (error) {
      console.error('Failed to fetch operation logs:', error);
      // 不显示错误消息给用户，避免频繁弹出
      // 设置空数组，避免界面崩溃
      setOperationLogs([]);
    }
  };

  // 移除SSH相关处理函数，通过独立的SSH组件处理这些功能

  const fetchAgents = async () => {
    setLoading(true);
    try {
      const fetchedAgents = await invoke<Agent[]>('get_agents');
      setAgents(fetchedAgents);
    } catch (error) {
      console.error('获取执行机列表失败:', error);
      message.error('获取执行机列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingAgent(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 设置Modal的属性
  const modalProps = {
    open: isModalVisible,
    onCancel: () => setIsModalVisible(false),
    title: editingAgent ? '编辑执行机' : '添加执行机',
    destroyOnClose: true,
    maskClosable: false,
    zIndex: 1000, // 设置较低的zIndex，确保不会遮挡SSH终端窗口
    width: '600px'
  };

  const handleEdit = (agent: Agent) => {
    setEditingAgent(agent);
    form.setFieldsValue(agent);
    setIsModalVisible(true);
  };

  const handleCopy = async (agent: Agent) => {
    const newAgent = {
      ...agent,
      id: crypto.randomUUID(),
      name: `${agent.name}_副本`,
      ip: agent.ip,
      username: agent.username,
      password: agent.password,
      work_dir: agent.work_dir,
      status: 'Offline',
      last_seen: new Date().toISOString(),
    };
    try {
      await invoke('create_agent', { agent: newAgent });
      message.success('执行机复制成功');
      fetchAgents();
    } catch (error) {
      console.error('Failed to copy agent:', error);
      message.error('执行机复制失败');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await invoke('delete_agent', { id });
      message.success('执行机删除成功');
      fetchAgents();
    } catch (error) {
      console.error('Failed to delete agent:', error);
      message.error('执行机删除失败');
    }
  };

  const handleBatchDelete = async () => {
    if (selectedAgents.length === 0) {
      message.warning('请选择要删除的执行机');
      return;
    }
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedAgents.length} 个执行机吗？`,
      onOk: async () => {
        try {
          await Promise.all(selectedAgents.map(id => invoke('delete_agent', { id })));
          message.success('批量删除成功');
          setSelectedAgents([]);
          fetchAgents();
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败');
        }
      },
    });
  };

  const handleBatchFileTransfer = async () => {
    if (selectedAgents.length === 0 || selectedFiles.length === 0 || !targetPath) {
      message.warning('请选择执行机、文件和目标路径');
      return;
    }

    // 显示加载中提示
    const loadingMessage = message.loading('正在传输文件...', 0);
    
    try {
      // 确保目标路径存在
      for (const agentId of selectedAgents) {
        const agent = agents.find(a => a.id === agentId);
        if (!agent) continue;
        
        try {
          // 先创建目标目录（如果不存在）
          await invoke('execute_ssh_command', {
            agentId,
            command: `mkdir -p "${targetPath}"`
          });
        } catch (err) {
          console.error(`创建目录失败: ${err}`);
          // 继续执行，因为目录可能已经存在
        }
      }
      
      // 传输文件
      await Promise.all(selectedAgents.map(async (agentId) => {
        const agent = agents.find(a => a.id === agentId);
        if (!agent) return;

        for (const file of selectedFiles) {
          if (!file.originFileObj || !file.originFileObj.path) {
            console.error('文件对象缺少path属性');
            continue;
          }
          
          await invoke('transfer_file', {
            agentId,
            sourcePath: file.originFileObj.path,
            targetPath: `${targetPath}/${file.name}`
          });
        }

        await invoke('add_operation_log', {
          agentId,
          operationType: 'file_transfer',
          description: `向执行机 ${agent.name} 传输了 ${selectedFiles.length} 个文件`,
          status: 'success'
        });
      }));

      loadingMessage(); // 关闭加载提示
      message.success('文件传输成功');
      setSelectedFiles([]);
      setTargetPath('');
      fetchOperationLogs();
    } catch (error) {
      loadingMessage(); // 关闭加载提示
      console.error('文件传输失败:', error);
      message.error(`文件传输失败: ${error}`);
      selectedAgents.forEach(async (agentId) => {
        const agent = agents.find(a => a.id === agentId);
        if (!agent) return;

        await invoke('add_operation_log', {
          agentId,
          operationType: 'file_transfer',
          description: `向执行机 ${agent.name} 传输文件失败: ${error}`,
          status: 'failed'
        });
      });
      fetchOperationLogs();
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      if (editingAgent) {
        await invoke('update_agent', { agent: { ...editingAgent, ...values } });
        message.success('执行机更新成功');
      } else {
        const newAgent = {
          id: crypto.randomUUID(),
          ...values,
          status: 'Offline',
          last_seen: new Date().toISOString(),
          created_at: new Date().toISOString(),
          ping_latency: null
        };
        await invoke('create_agent', { agent: newAgent });
        // 新增执行机后立即执行ping检测
        await invoke('ping_agents');
        message.success('执行机添加成功');
      }
      setIsModalVisible(false);
      fetchAgents();
    } catch (error) {
      console.error('Failed to save agent:', error);
      message.error('保存失败');
    }
  };

  const handleTestConnection = async () => {
    try {
      const values = await form.validateFields();
      message.loading('正在测试连接...', 0);
      // 创建一个完整的Agent对象，包含所有必要字段
      const testAgent = {
        id: editingAgent ? editingAgent.id : crypto.randomUUID(),
        ...values,
        status: 'Offline',
        last_seen: new Date().toISOString(),
        created_at: new Date().toISOString(),
        ping_latency: null
      };
      await invoke('test_agent_connection', { agent: testAgent });
      message.destroy();
      message.success('连接测试成功');
    } catch (error) {
      message.destroy();
      console.error('Failed to test connection:', error);
      message.error('连接测试失败');
    }
  };

  const handleSSH = (agent: Agent) => {
    // 如果当前正在编辑执行机，不触发SSH终端显示
    if (isModalVisible) {
      return;
    }
    setCurrentAgent(agent);
    setShowInlineTerminal(true);
  };
  
  const handleCloseInlineTerminal = () => {
    setShowInlineTerminal(false);
    setCurrentAgent(null);
  };

  const handleBatchExecute = async (command: string) => {
    if (selectedAgents.length === 0) {
      message.warning('请选择要执行命令的执行机');
      return;
    }

    try {
      await Promise.all(selectedAgents.map(async (id) => {
        const agent = agents.find(a => a.id === id);
        if (!agent) return;

        await invoke('execute_command', { agentId: id, command });
        // 记录命令执行
        await invoke('add_operation_log', {
          agentId: id,
          operationType: 'command',
          description: `在执行机 ${agent.name} 上执行命令: ${command}`,
          status: 'success'
        });
      }));
      message.success(`正在${selectedAgents.length}个执行机上执行命令`);
      fetchOperationLogs();
      fetchAgents();
    } catch (error) {
      console.error('Failed to execute command:', error);
      // 记录失败操作
      selectedAgents.forEach(async (id) => {
        const agent = agents.find(a => a.id === id);
        if (!agent) return;

        await invoke('add_operation_log', {
          agentId: id,
          operationType: 'command',
          description: `在执行机 ${agent.name} 上执行命令失败: ${command}`,
          status: 'failed'
        });
      });
      message.error('命令执行失败');
      fetchOperationLogs();
    }
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: Agent, b: Agent) => a.name.localeCompare(b.name),
      render: (text: string, record: Agent) => (
        <Tooltip title={record.description || '无描述'}>
          <span>{text}</span>
        </Tooltip>
      ),
      filters: Array.from(new Set(agents.map(a => a.name.charAt(0).toUpperCase())))
        .sort()
        .map(letter => ({ text: `${letter}开头`, value: letter })),
      onFilter: (value: boolean | React.Key, record: Agent) => record.name.charAt(0).toUpperCase() === value.toString(),
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip',
      filters: Array.from(new Set(agents.map(a => a.ip.split('.')[0] + '.' + a.ip.split('.')[1])))
        .map(subnet => ({ text: subnet + '.*', value: subnet })),
      onFilter: (value: boolean | React.Key, record: Agent) => record.ip.startsWith(value.toString()),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: Agent) => {
        // 直接使用后端返回的状态
        const color = record.status === 'Online' ? 'green' : 'red';
        const actualStatus = record.status;

        return (
          <Tooltip title={record.ping_latency ? `延迟: ${record.ping_latency}ms` : '未知延迟'}>
            <Tag color={color}>{actualStatus.toUpperCase()}</Tag>
          </Tooltip>
        );
      },
      filters: [
        { text: '在线', value: 'Online' },
        { text: '离线', value: 'Offline' },
        { text: '忙碌', value: 'Busy' },
      ],
      onFilter: (value: boolean | React.Key, record: Agent) => {
        // 直接使用后端返回的状态进行过滤
        return record.status === value.toString();
      },
    },
    {
      title: '延迟',
      dataIndex: 'ping_latency',
      key: 'ping_latency',
      render: (latency: number) => {
        if (!latency) return '-';
        const color = 
          latency < 100 ? 'green' : 
          latency < 300 ? 'orange' : 
          'red';
        return <Tag color={color}>{latency}ms</Tag>;
      },
      sorter: (a: Agent, b: Agent) => (a.ping_latency || 0) - (b.ping_latency || 0),
    },
    {
      title: '标签',
      dataIndex: 'labels',
      key: 'labels',
      render: (labels: string) => {
        if (!labels) return '-';
        return (
          <Space size={2} wrap>
            {labels.split(',').map(label => (
              <Tag key={label} color="blue" style={{ margin: 0, padding: '0 4px' }}>{label.trim()}</Tag>
            ))}
          </Space>
        );
      },
      filters: Array.from(new Set(agents.flatMap(a => a.labels ? a.labels.split(',').map(l => l.trim()) : [])))
        .map(label => ({ text: label, value: label })),
      onFilter: (value: boolean | React.Key, record: Agent) => {
        return record.labels ? record.labels.includes(value.toString()) : false;
      },
    },
    {
      title: '最大任务数',
      dataIndex: 'max_jobs',
      key: 'max_jobs',
      render: (max_jobs?: number) => max_jobs || '-',
      sorter: (a: Agent, b: Agent) => (a.max_jobs || 0) - (b.max_jobs || 0),
    },
    {
      title: '工作目录',
      dataIndex: 'work_dir',
      key: 'work_dir',
      filters: Array.from(new Set(agents.map(a => a.work_dir.split('/')[1] || '根目录')))
        .map(dir => ({ text: dir, value: dir === '根目录' ? '/' : `/${dir}` })),
      onFilter: (value: boolean | React.Key, record: Agent) => {
        if (value.toString() === '/') return record.work_dir.startsWith('/');
        return record.work_dir.startsWith(value.toString());
      },
    },
    {
      title: '最后在线',
      dataIndex: 'last_seen',
      key: 'last_seen',
      render: (last_seen: string) => {
        const date = new Date(last_seen);
        return isNaN(date.getTime()) ? '-' : date.toLocaleString('zh-CN');
      },
      sorter: (a: Agent, b: Agent) => {
        const dateA = new Date(a.last_seen).getTime();
        const dateB = new Date(b.last_seen).getTime();
        return isNaN(dateA) || isNaN(dateB) ? 0 : dateA - dateB;
      },
      filters: [
        { text: '今天', value: 'today' },
        { text: '昨天', value: 'yesterday' },
        { text: '本周', value: 'week' },
        { text: '更早', value: 'earlier' },
      ],
      onFilter: (value: boolean | React.Key, record: Agent) => {
        const lastSeen = new Date(record.last_seen).getTime();
        const now = new Date().getTime();
        const today = new Date().setHours(0, 0, 0, 0);
        const yesterday = today - 86400000; // 24小时的毫秒数
        const weekAgo = today - 6 * 86400000; // 一周前
        
        if (value.toString() === 'today') return lastSeen >= today;
        if (value.toString() === 'yesterday') return lastSeen >= yesterday && lastSeen < today;
        if (value.toString() === 'week') return lastSeen >= weekAgo && lastSeen < yesterday;
        if (value.toString() === 'earlier') return lastSeen < weekAgo;
        return true;
      },
    },
  ];

  return (
    <>
      <div>
        <Space style={{ marginBottom: 16 }} className="compact-actions">
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleAdd}
            size="small"
          >
            添加执行机
          </Button>
          <Button 
            icon={<EditOutlined />}
            onClick={() => {
              const selectedExecutors = agents.filter(agent => selectedAgents.includes(agent.id));
              if (selectedExecutors.length === 1) {
                handleEdit(selectedExecutors[0]);
              } else {
                message.warning('请选择一个执行机进行编辑');
              }
            }}
            disabled={selectedAgents.length !== 1}
            size="small"
          >
            编辑
          </Button>
          <Button 
            icon={<CopyOutlined />}
            onClick={() => {
              const selectedExecutors = agents.filter(agent => selectedAgents.includes(agent.id));
              selectedExecutors.forEach(handleCopy);
            }}
            disabled={selectedAgents.length === 0}
            size="small"
          >
            批量复制
          </Button>
          <Button 
            icon={<PlayCircleOutlined />} 
            onClick={() => {
              const selectedExecutors = agents.filter(agent => selectedAgents.includes(agent.id));
              // 使用延时依次触发SSH连接，避免事件冲突
              selectedExecutors.forEach((agent, index) => {
                setTimeout(() => {
                  handleSSH(agent);
                }, index * 500); // 每个连接间隔500ms
              });
              message.info(`正在为${selectedExecutors.length}个执行机打开SSH终端...`);
            }}
            disabled={selectedAgents.length === 0}
            size="small"
          >
            批量SSH
          </Button>
          <Button 
            icon={<DeleteOutlined />} 
            onClick={handleBatchDelete}
            danger
            disabled={selectedAgents.length === 0}
            size="small"
          >
            批量删除
          </Button>
          <Button onClick={fetchAgents} size="small">刷新</Button>
          <Button 
            icon={<HistoryOutlined />}
            onClick={() => setIsLogModalVisible(true)}
            size="small"
          >
            操作日志
          </Button>
        </Space>
        
        <Table
          className="compact-table"
          rowSelection={{
            selectedRowKeys: selectedAgents,
            onChange: (selectedRowKeys) => {
              setSelectedAgents(selectedRowKeys as string[]);
            },
          }}
          columns={columns}
          dataSource={agents}
          rowKey="id"
          loading={loading}
          pagination={{ 
            defaultPageSize: 20, 
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          size="small"
        />
      </div>
      
      {/* 添加/编辑执行机的Modal */}
      <Modal
        title={editingAgent ? '编辑执行机' : '添加执行机'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => setIsModalVisible(false)}
        styles={{ body: { maxHeight: '70vh', overflowY: 'auto' } }}
        width={600}
        zIndex={1100} // 设置更高的zIndex，确保显示在SSH终端窗口之上
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            work_dir: '/tmp',
            max_jobs: 5
          }}
        >
          <Form.Item
            name="name"
            label="执行机名称"
            rules={[{ required: true, message: '请输入执行机名称' }]}
          >
            <Input placeholder="请输入执行机名称" />
          </Form.Item>
          <Form.Item
            name="ip"
            label="IP地址"
            rules={[{ required: true, message: '请输入IP地址' }]}
          >
            <Input placeholder="请输入IP地址" />
          </Form.Item>
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
          <Form.Item
            name="password"
            label="密码"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>
          <Form.Item
            name="work_dir"
            label="工作目录"
            rules={[{ required: true, message: '请输入工作目录' }]}
          >
            <Input placeholder="请输入工作目录" />
          </Form.Item>
          <Form.Item
            name="labels"
            label="标签"
          >
            <Input placeholder="多个标签用逗号分隔，如: prod,web,java" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea placeholder="请输入执行机描述" />
          </Form.Item>
          <Form.Item
            name="max_jobs"
            label="最大任务数"
          >
            <Input type="number" placeholder="请输入最大任务数" />
          </Form.Item>
          <Button type="dashed" onClick={handleTestConnection}>测试连接</Button>
        </Form>
      </Modal>

      {/* SSH终端Modal */}
      <SSHTerminal
        open={showInlineTerminal}
        agent={currentAgent}
        onClose={() => {
          handleCloseInlineTerminal();
          setSshOutput('');
          if (currentAgent) {
            invoke('disconnect_ssh', { agentId: currentAgent.id }).catch(error => {
              console.error('断开SSH连接失败:', error);
            });
          }
        }}
      />

      {/* 操作日志Modal */}
      <Modal
        title="操作日志"
        open={isLogModalVisible}
        onCancel={() => setIsLogModalVisible(false)}
        styles={{ body: { maxHeight: '70vh', overflowY: 'auto' } }}
        width={800}
        footer={null}
      >
        <Table
          dataSource={operationLogs}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          size="small"
        >
          <Table.Column
            title="执行机"
            dataIndex="agent_id"
            key="agent_id"
            render={(agentId) => {
              const agent = agents.find(a => a.id === agentId);
              return agent ? agent.name : agentId;
            }}
          />
          <Table.Column
            title="操作类型"
            dataIndex="operation_type"
            key="operation_type"
            render={(type) => {
              const typeMap: Record<string, { text: string; color: string }> = {
                command: { text: '命令执行', color: 'blue' },
                file_transfer: { text: '文件传输', color: 'purple' },
                ssh: { text: 'SSH连接', color: 'green' },
                other: { text: '其他操作', color: 'default' }
              };
              const info = typeMap[type] || { text: type, color: 'default' };
              return <Tag color={info.color}>{info.text}</Tag>;
            }}
            filters={[
              { text: '命令执行', value: 'command' },
              { text: '文件传输', value: 'file_transfer' },
              { text: 'SSH连接', value: 'ssh' },
              { text: '其他操作', value: 'other' },
            ]}
            onFilter={(value, record) => record.operation_type === value}
          />
          <Table.Column
            title="描述"
            dataIndex="description"
            key="description"
          />
          <Table.Column
            title="状态"
            dataIndex="status"
            key="status"
            render={(status) => {
              const color = status === 'success' ? 'green' : 'red';
              const text = status === 'success' ? '成功' : '失败';
              return <Tag color={color}>{text}</Tag>;
            }}
            filters={[
              { text: '成功', value: 'success' },
              { text: '失败', value: 'failed' },
            ]}
            onFilter={(value, record) => record.status === value}
          />
          <Table.Column
            title="时间"
            dataIndex="created_at"
            key="created_at"
            render={(time) => new Date(time).toLocaleString('zh-CN')}
            sorter={(a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()}
            defaultSortOrder="descend"
          />
        </Table>
      </Modal>

      {/* 文件传输区域 */}
      <div style={{ marginTop: 16, padding: 16, border: '1px dashed #ccc', borderRadius: 4 }}>
        <h3>文件传输</h3>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Upload
            multiple
            fileList={selectedFiles}
            onChange={({ fileList }) => setSelectedFiles(fileList)}
            beforeUpload={() => false}
            maxCount={5}
          >
            <Button icon={<UploadOutlined />}>选择文件</Button>
          </Upload>
          <Input 
            placeholder="目标路径，如: /home/<USER>/files" 
            value={targetPath} 
            onChange={e => setTargetPath(e.target.value)} 
            style={{ width: '100%' }}
            addonBefore="目标路径"
          />
          <Button 
            type="primary" 
            onClick={handleBatchFileTransfer} 
            disabled={selectedAgents.length === 0 || selectedFiles.length === 0 || !targetPath}
          >
            传输文件到选中执行机
          </Button>
        </Space>
      </div>

      {/* 批量命令执行区域 */}
      <div style={{ marginTop: 16, padding: 16, border: '1px dashed #ccc', borderRadius: 4 }}>
        <h3>批量命令执行</h3>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Input.Search
            placeholder="输入要在选中执行机上执行的命令"
            enterButton="执行"
            onSearch={handleBatchExecute}
            disabled={selectedAgents.length === 0}
          />
          <div>
            <Button 
              style={{ marginRight: 8 }} 
              onClick={() => handleBatchExecute('ls -la')} 
              disabled={selectedAgents.length === 0}
            >
              查看文件列表
            </Button>
            <Button 
              style={{ marginRight: 8 }} 
              onClick={() => handleBatchExecute('df -h')} 
              disabled={selectedAgents.length === 0}
            >
              查看磁盘空间
            </Button>
            <Button 
              onClick={() => handleBatchExecute('free -h')} 
              disabled={selectedAgents.length === 0}
            >
              查看内存使用
            </Button>
          </div>
        </Space>
      </div>
      

      {/* SSH终端 */}
      {showInlineTerminal && currentAgent && (
        <SSHTerminal
          open={showInlineTerminal}
          agent={currentAgent}
          onClose={handleCloseInlineTerminal}
        />
      )}
    </>
  );
};

export default ExecutorManagement;