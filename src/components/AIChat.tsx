import React, { useState, useEffect, useRef, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { ChatMessage, ChatConversation, ApiConfig } from '../types';
import './AIChat.css';
import { Select, Button, Alert, Typography, Card, Spin, Tooltip, Space, Input, Layout, Menu, Modal, Form, Divider, Empty, message, notification } from 'antd';
import { SendOutlined, DeleteOutlined, CommentOutlined, PlusOutlined, MessageOutlined, EditOutlined, SettingOutlined } from '@ant-design/icons';

interface AIChatProps {
    currentConversation: ChatConversation | null;
    setCurrentConversation: (conversation: ChatConversation | null) => void;
    apiConfigs: ApiConfig[]; // Pass API configs down
    refreshConversations: () => void; // Function to refresh conversation list
}

const { Sider, Content } = Layout;

const AIChat: React.FC<AIChatProps> = ({ currentConversation, setCurrentConversation, apiConfigs, refreshConversations }) => {
    const [messages, setMessages] = useState<ChatMessage[]>([]);
    const [newMessage, setNewMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const [isSending, setIsSending] = useState(false);
    const [availableModels, setAvailableModels] = useState<string[]>([]);
    const [selectedModel, setSelectedModel] = useState<string>('');
    const [conversations, setConversations] = useState<ChatConversation[]>([]);
    const [siderCollapsed, setSiderCollapsed] = useState(false);
    const [isNewConversationModalVisible, setIsNewConversationModalVisible] = useState(false);
    // 移除不需要的状态，直接使用表单值
    const [providerModels, setProviderModels] = useState<string[]>([]);
    const [form] = Form.useForm();

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    const fetchMessages = useCallback(async () => {
        if (!currentConversation) return;
        setIsLoading(true);
        setError(null);
        try {
            // 添加超时处理
            const timeoutPromise = new Promise<ChatMessage[]>((_, reject) => {
                setTimeout(() => reject(new Error('获取消息超时，请检查网络连接')), 10000);
            });

            // 使用 Promise.race 来实现超时处理
            const fetchedMessages = await Promise.race([
                invoke<ChatMessage[]>('get_messages', { conversationId: currentConversation.id }), // Explicit type
                timeoutPromise
            ]) as ChatMessage[]; // Cast the result

            // 防止空值或非数组
            if (!fetchedMessages || !Array.isArray(fetchedMessages)) {
                throw new Error('返回的消息格式无效');
            }

            // Sort messages by creation time before setting state
            fetchedMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
            setMessages(fetchedMessages);
        } catch (err: any) {
            console.error('Failed to fetch messages:', err);
            let errorMessage = err.toString();

            // 处理常见错误类型
            if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
                errorMessage = '获取消息超时，请检查网络连接或稍后重试';
            } else if (errorMessage.includes('format') || errorMessage.includes('格式')) {
                errorMessage = '消息格式错误，请刷新页面重试';
            }

            setError(`获取消息失败: ${errorMessage}`);

            // 显示错误提示，但不阻止用户继续使用
            message.error(`获取消息失败: ${errorMessage}`);

            // 即使出错也保留现有消息，不要清空
            // 如果没有消息，设置一个空数组防止渲染错误
            if (messages.length === 0) {
                setMessages([]);
            }
        } finally {
            setIsLoading(false);
        }
    }, [currentConversation, messages.length]);

    // 获取所有对话列表
    const fetchConversations = useCallback(async () => {
        try {
            // 添加超时处理
            const timeoutPromise = new Promise<ChatConversation[]>((_, reject) => {
                setTimeout(() => reject(new Error('获取对话列表超时，请检查网络连接')), 10000);
            });

            // 使用 Promise.race 来实现超时处理
            const fetchedConversations = await Promise.race([
                invoke<ChatConversation[]>('get_conversations'), // Explicit type
                timeoutPromise
            ]) as ChatConversation[]; // Cast the result

            // 防止空值或非数组
            if (!fetchedConversations || !Array.isArray(fetchedConversations)) {
                throw new Error('返回的对话列表格式无效');
            }

            setConversations(fetchedConversations);

            // 如果当前选中的对话不在列表中，重置选中状态
            if (currentConversation && fetchedConversations.length > 0) {
                const stillExists = fetchedConversations.some(conv => conv.id === currentConversation.id);
                if (!stillExists) {
                    // 如果当前对话不存在了，选择第一个对话
                    setCurrentConversation(fetchedConversations[0]);
                    message.info('当前对话已不存在，已自动切换到其他对话');
                }
            }
        } catch (err: any) {
            console.error('Failed to fetch conversations:', err);
            let errorMessage = err.toString();

            // 处理常见错误类型
            if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
                errorMessage = '获取对话列表超时，请检查网络连接或稍后重试';
            } else if (errorMessage.includes('format') || errorMessage.includes('格式')) {
                errorMessage = '对话列表格式错误，请刷新页面重试';
            }

            setError(`获取对话列表失败: ${errorMessage}`);

            // 显示错误提示，但不阻止用户继续使用
            message.error(`获取对话列表失败: ${errorMessage}`);

            // 即使出错也保留现有对话列表，不要清空
            // 如果没有对话，设置一个空数组防止渲染错误
            if (conversations.length === 0) {
                setConversations([]);
            }
        }
    }, [currentConversation, conversations.length]);

    // 初始加载时获取对话列表
    useEffect(() => {
        fetchConversations();
    }, [fetchConversations]);

    // 监听模态框打开状态
    useEffect(() => {
        if (isNewConversationModalVisible) {
            // 模态框打开时重置模型列表
            setProviderModels([]);

            // 检查是否有可用的API配置
            if (apiConfigs.length === 0) {
                notification.warning({
                    message: '未配置API',
                    description: (
                        <div>
                            <p>您还没有配置AI服务提供商的API密钥。</p>
                            <p>请先在API配置页面添加至少一个提供商的API密钥。</p>
                        </div>
                    ),
                    duration: 5,
                });
            }
        }
    }, [isNewConversationModalVisible, apiConfigs.length]);

    // 监听 apiConfigs 变化
    useEffect(() => {
        if (apiConfigs.length > 0 && isNewConversationModalVisible) {
            // 如果有选择的服务提供商，更新模型列表
            const provider = form.getFieldValue('provider');
            if (provider) {
                const config = apiConfigs.find(c => c.provider === provider);
                if (config) {
                    setProviderModels(config.models || []);
                }
            }
        }
    }, [apiConfigs, isNewConversationModalVisible, form]);

    // 全局错误处理
    useEffect(() => {
        const handleError = (error: Error) => {
            console.error('Unhandled error in AIChat component:', error);
            notification.error({
                message: '应用错误',
                description: `发生意外错误: ${error.message}`,
                duration: 0,
            });
        };

        window.addEventListener('error', (event) => handleError(event.error));
        window.addEventListener('unhandledrejection', (event) => handleError(event.reason));

        return () => {
            window.removeEventListener('error', (event) => handleError(event.error));
            window.removeEventListener('unhandledrejection', (event) => handleError(event.reason));
        };
    }, []);

    // Effect to update available models and selected model when conversation changes
    useEffect(() => {
        if (currentConversation && apiConfigs.length > 0) {
            const config = apiConfigs.find(c => c.provider === currentConversation.provider);
            if (config) {
                setAvailableModels(config.models || []);
                setSelectedModel(currentConversation.model);
            } else {
                setAvailableModels([]);
                setSelectedModel('');

                // 如果没有找到对应的API配置，显示警告
                notification.warning({
                    message: '缺少API配置',
                    description: (
                        <div>
                            <p>未找到提供商 <strong>{currentConversation.provider}</strong> 的API配置。</p>
                            <p>请先在API配置页面添加该提供商的API密钥。</p>
                        </div>
                    ),
                    duration: 5,
                });
            }

            // 使用try-catch包裹fetchMessages，防止异常导致组件崩溃
            try {
                fetchMessages().catch(err => {
                    console.error('Failed to fetch messages:', err);
                    setError(`获取消息失败: ${err.toString()}`);
                    // 即使出错也不清空消息，保持现有状态
                });
            } catch (err: any) {
                console.error('Exception in fetchMessages:', err);
                setError(`获取消息异常: ${err.toString()}`);
            }
        } else {
            setMessages([]); // Clear messages if no conversation is selected
            setAvailableModels([]);
            setSelectedModel('');
        }
    }, [currentConversation, apiConfigs, fetchMessages]);

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const handleSendMessage = async () => {
        if (!newMessage.trim() || !currentConversation || isSending) return;

        setIsSending(true);
        setError(null);
        const userMessageContent = newMessage;
        setNewMessage(''); // Clear input immediately

        // Optimistically add user message
        const optimisticUserMessage: ChatMessage = {
            id: `temp-user-${Date.now()}`,
            role: 'user',
            content: userMessageContent,
            created_at: new Date().toISOString(), // Use ISO string for consistency
            conversation_id: currentConversation.id,
        };
        setMessages(prev => [...prev, optimisticUserMessage]);

        try {
            // 检查API配置
            const config = apiConfigs.find(c => c.provider === currentConversation.provider);
            if (!config) {
                throw new Error(`未找到提供商 ${currentConversation.provider} 的API配置，请先配置API`);
            }

            if (!config.api_key) {
                throw new Error(`提供商 ${currentConversation.provider} 的API密钥未设置，请先配置API密钥`);
            }

            if (!selectedModel) {
                throw new Error('未选择模型，请先选择模型');
            }

            // 添加超时处理
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('请求超时，请检查网络连接')), 60000); // 增加超时时间到60秒
            });

            // 显示加载提示
            const loadingMessage = message.loading('正在生成回复...', 0);

            try {
                // 使用 Promise.race 来实现超时处理
                const response: any = await Promise.race([
                    invoke('send_chat_message', {
                        request: {
                            conversation_id: currentConversation.id,
                            message: userMessageContent,
                            model: selectedModel, // Use the currently selected model
                            provider: currentConversation.provider,
                        },
                    }),
                    timeoutPromise
                ]);

                // 关闭加载提示
                loadingMessage();

                // Refetch messages to get the actual AI response and update IDs
                await fetchMessages();
            } catch (err) {
                // 关闭加载提示
                loadingMessage();
                throw err; // 将错误传递给外层catch块
            }
        } catch (err: any) {
            console.error('Failed to send message:', err);
            let errorMessage = err.toString();

            // 处理常见错误类型
            if (errorMessage.includes('API key')) {
                errorMessage = 'API密钥无效或未提供，请检查API配置';
            } else if (errorMessage.includes('model')) {
                errorMessage = '模型无效或不可用，请选择其他模型';
            } else if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
                errorMessage = '请求超时，请检查网络连接或稍后重试';
            } else if (errorMessage.includes('Unsupported provider')) {
                errorMessage = `不支持的提供商: ${currentConversation.provider}，请选择其他提供商`;
            } else if (errorMessage.includes('format')) {
                errorMessage = `API响应格式错误，请检查提供商和模型配置`;
            }

            setError(`发送消息失败: ${errorMessage}`);

            // Remove optimistic message on error
            setMessages(prev => prev.filter(msg => msg.id !== optimisticUserMessage.id));
            // Restore input field content on error
            setNewMessage(userMessageContent);

            // 显示错误提示
            notification.error({
                message: '发送消息失败',
                description: errorMessage,
                duration: 5,
                placement: 'topRight',
            });
        } finally {
            setIsSending(false);
        }
    };

    const handleModelChange = async (value: string) => {
        const newModel = value;
        if (!currentConversation || newModel === selectedModel) return;

        // 验证模型名称
        if (!newModel || newModel.trim() === '') {
            message.error('请选择有效的模型');
            return;
        }

        // 验证模型是否在可用模型列表中
        const config = apiConfigs.find(c => c.provider === currentConversation.provider);
        if (config && config.models && !config.models.includes(newModel)) {
            console.warn(`模型 ${newModel} 不在提供商 ${currentConversation.provider} 的可用模型列表中`);
            // 不阻止用户使用未列出的模型，但记录警告
        }

        setIsLoading(true); // Show loading indicator while updating
        setError(null);

        // 显示加载提示
        const loadingMessage = message.loading('正在更新模型...', 0);

        try {
            await invoke('update_conversation_model', {
                conversation_id: currentConversation.id, // 确保参数名称与后端一致
                new_model: newModel,
            });

            // 关闭加载提示
            loadingMessage();

            setSelectedModel(newModel);
            // 更新对话对象
            setCurrentConversation({ ...currentConversation, model: newModel });
            // 刷新对话列表
            refreshConversations();
            // 显示成功提示
            message.success(`模型已更新为 ${newModel}`);
        } catch (err: any) {
            // 关闭加载提示
            loadingMessage();

            console.error('Failed to update model:', err);
            let errorMessage = err.toString();

            // 处理常见错误类型
            if (errorMessage.includes('model')) {
                errorMessage = '模型无效或不可用，请选择其他模型';
            } else if (errorMessage.includes('database')) {
                errorMessage = '数据库操作失败，请重试';
            } else if (errorMessage.includes('conversation')) {
                errorMessage = '对话不存在或已被删除';
            }

            setError(`更新模型失败: ${errorMessage}`);
            // 显示错误提示
            notification.error({
                message: '更新模型失败',
                description: errorMessage,
                duration: 5,
            });
            // 保持原来的模型选择
        } finally {
            setIsLoading(false);
        }
    };

    const handleDeleteMessage = async (messageId: string) => {
        if (!currentConversation) return;

        // 添加确认对话框
        Modal.confirm({
            title: '删除消息',
            content: '确定要删除这条消息吗？',
            okText: '删除',
            cancelText: '取消',
            okButtonProps: { danger: true },
            onOk: async () => {
                try {
                    // 乐观地先从界面移除消息
                    setMessages(prev => prev.filter(msg => msg.id !== messageId));

                    await invoke('delete_message', { messageId });
                    message.success('消息已删除');
                } catch (err: any) {
                    console.error('Failed to delete message:', err);
                    let errorMessage = err.toString();
                    setError(`删除消息失败: ${errorMessage}`);
                    message.error(`删除消息失败: ${errorMessage}`);

                    // 如果删除失败，重新获取消息列表恢复正确状态
                    fetchMessages();
                }
            },
        });
    };

    const showNewConversationModal = () => {
        form.resetFields();
        setProviderModels([]);
        setIsNewConversationModalVisible(true);
    };

    // 处理服务提供商变化
    const handleProviderChange = (value: string) => {
        // 根据选择的服务提供商更新可用模型列表
        const config = apiConfigs.find(c => c.provider === value);
        if (config) {
            setProviderModels(config.models || []);
            // 重置模型选择
            form.setFieldValue('model', undefined);
        } else {
            setProviderModels([]);
        }
    };

    const handleNewConversationCancel = () => {
        setIsNewConversationModalVisible(false);
        // 重置表单和模型列表
        form.resetFields();
        setProviderModels([]);
    };

    const handleNewConversationCreate = async () => {
        let loadingMessage: any = null;
        try {
            // 验证表单字段
            const values = await form.validateFields();
            const { title, provider, model } = values;

            // 检查模型是否存在
            if (!model) {
                message.error('请选择模型');
                return;
            }

            // 检查API配置
            const config = apiConfigs.find(c => c.provider === provider);
            if (!config) {
                message.error(`未找到提供商 ${provider} 的API配置，请先配置API`);
                return;
            }

            if (!config.api_key) {
                message.error(`提供商 ${provider} 的API密钥未设置，请先配置API密钥`);
                return;
            }

            // 显示加载中状态
            loadingMessage = message.loading('正在创建对话...', 0);

            // 添加超时处理
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('请求超时，请检查网络连接')), 15000);
            });

            // 创建新对话
            const newConversation = await Promise.race([
                invoke('create_conversation', {
                    title,
                    provider,
                    model
                }),
                timeoutPromise
            ]);

            // 关闭加载提示
            if (loadingMessage) loadingMessage();

            // 验证返回的对话 ID
            if (!newConversation || typeof newConversation !== 'string') {
                throw new Error('创建对话失败，返回的对话 ID 无效');
            }

            // 关闭模态框并更新状态
            setIsNewConversationModalVisible(false);
            // 重置表单和模型列表
            form.resetFields();
            setProviderModels([]);

            try {
                // 更新对话列表
                await fetchConversations();

                // 在对话列表中查找新创建的对话
                const createdConversation = conversations.find(conv => conv.id === newConversation);
                if (createdConversation) {
                    setCurrentConversation(createdConversation);
                    message.success('新对话创建成功');
                } else {
                    // 如果找不到，再次尝试获取对话列表
                    setTimeout(async () => {
                        await fetchConversations();
                        const foundConversation = conversations.find(conv => conv.id === newConversation);
                        if (foundConversation) {
                            setCurrentConversation(foundConversation);
                        } else {
                            // 如果还是找不到，使用已知的信息创建一个临时对话对象
                            const tempConversation: ChatConversation = {
                                id: newConversation,
                                title,
                                provider,
                                model,
                                created_at: new Date().toISOString(),
                                updated_at: new Date().toISOString(),
                                topic: undefined // Explicitly set topic to undefined
                            };
                            setCurrentConversation(tempConversation);
                        }
                        message.success('新对话创建成功');
                    }, 500);
                }
            } catch (fetchError) {
                console.error('Failed to fetch conversations after creation:', fetchError);
                // 即使获取对话列表失败，也创建一个临时对话对象
                const tempConversation: ChatConversation = {
                    id: newConversation,
                    title,
                    provider,
                    model,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    topic: undefined // Explicitly set topic to undefined
                };
                setCurrentConversation(tempConversation);
                message.success('新对话创建成功，但获取对话列表失败');
            }
        } catch (err: any) {
            // 关闭加载提示
            if (loadingMessage) loadingMessage();

            if (err.errorFields) {
                // 表单验证错误，不需要额外处理，form.validateFields 会自动显示错误
                return;
            }

            console.error('Failed to create conversation:', err);
            let errorMessage = err.toString();

            // 处理常见错误类型
            if (errorMessage.includes('API key')) {
                errorMessage = 'API密钥无效或未提供，请检查API配置';
            } else if (errorMessage.includes('model')) {
                errorMessage = '模型无效或不可用，请选择其他模型';
            } else if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
                errorMessage = '请求超时，请检查网络连接或稍后重试';
            } else if (errorMessage.includes('provider')) {
                errorMessage = '提供商配置错误，请检查API配置';
            }

            setError(`创建对话失败: ${errorMessage}`);

            // 使用notification而不是message，以显示更详细的错误信息
            notification.error({
                message: '创建对话失败',
                description: errorMessage,
                duration: 5,
            });
        }
    };

    // 渲染对话列表菜单项
    const renderConversationMenuItems = () => {
        if (conversations.length === 0) {
            return [
                <Menu.Item key="empty" disabled style={{ textAlign: 'center' }}>
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="没有对话" style={{ margin: '20px 0' }} />
                </Menu.Item>
            ];
        }

        return conversations.map(conversation => (
            <Menu.Item
                key={conversation.id}
                icon={<MessageOutlined />}
                onClick={() => setCurrentConversation(conversation)}
            >
                {conversation.title}
            </Menu.Item>
        ));
    };

    // 渲染新对话模态框
    const renderNewConversationModal = () => (
        <Modal
            title="新建对话"
            open={isNewConversationModalVisible}
            onCancel={handleNewConversationCancel}
            onOk={handleNewConversationCreate}
            okText="创建"
            cancelText="取消"
        >
            <Form form={form} layout="vertical">
                <Form.Item
                    name="title"
                    label="对话标题"
                    rules={[{ required: true, message: '请输入对话标题' }]}
                >
                    <Input placeholder="输入对话标题" />
                </Form.Item>
                <Form.Item
                    name="provider"
                    label="服务提供商"
                    rules={[{ required: true, message: '请选择服务提供商' }]}
                >
                    <Select
                        placeholder="选择服务提供商"
                        onChange={handleProviderChange}
                    >
                        {apiConfigs.map(config => (
                            <Select.Option key={config.provider} value={config.provider}>
                                {config.provider}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item
                    name="model"
                    label="模型"
                    rules={[{ required: true, message: '请选择模型' }]}
                >
                    <Select
                        placeholder="选择模型"
                        disabled={providerModels.length === 0}
                    >
                        {providerModels.map(model => (
                            <Select.Option key={model} value={model}>
                                {model}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
    );

    // 如果没有选中对话，显示空白页面
    if (!currentConversation && conversations.length === 0) {
        return (
            <Layout style={{ height: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column', padding: '16px' }}>
                    <Typography.Title level={5} style={{ marginBottom: '16px', color: 'rgba(0, 0, 0, 0.45)' }}>
                        欢迎使用AI对话助手
                    </Typography.Title>
                    <Button
                        type="primary"
                        icon={<CommentOutlined />}
                        onClick={showNewConversationModal}
                    >
                        新建对话
                    </Button>
                    {renderNewConversationModal()}
                </div>
            </Layout>
        );
    }

    return (
        <Layout style={{ height: '100%' }}>
            {/* 对话列表侧边栏 */}
            <Sider
                width={250}
                theme="light"
                collapsible
                collapsed={siderCollapsed}
                onCollapse={setSiderCollapsed}
                className="conversation-sider"
            >
                <div className="conversation-sider-header">
                    <Typography.Title level={5} style={{ margin: '16px 0 8px 16px' }}>
                        对话列表
                    </Typography.Title>
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={showNewConversationModal}
                        style={{ margin: '0 16px 16px 16px', width: 'calc(100% - 32px)' }}
                    >
                        新建对话
                    </Button>
                </div>
                <Menu
                    mode="inline"
                    selectedKeys={currentConversation ? [currentConversation.id] : []}
                    style={{ borderRight: 0 }}
                >
                    {renderConversationMenuItems()}
                </Menu>
            </Sider>

            {/* 主内容区 */}
            <Layout className="chat-content-layout">
                <Content className="chat-content">
                    {currentConversation ? (
                        <div className="ai-chat-container">
                            <Card style={{ marginBottom: '16px' }}>
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: '8px' }}>
                                    <Typography.Text strong style={{ flexGrow: 1, marginRight: '16px' }}>
                                        当前会话: <strong>{currentConversation.title}</strong>
                                    </Typography.Text>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                        <Typography.Text type="secondary">模型:</Typography.Text>
                                        {availableModels.length > 0 ? (
                                            <Select
                                                value={selectedModel}
                                                onChange={handleModelChange}
                                                size="small"
                                                disabled={isLoading || isSending}
                                                style={{ minWidth: '180px' }}
                                                showSearch
                                                placeholder="选择模型"
                                                optionFilterProp="children"
                                                filterOption={(input, option) =>
                                                    (option?.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                                                }
                                                loading={isLoading}
                                            >
                                                {availableModels.map((model) => (
                                                    <Select.Option key={model} value={model}>{model}</Select.Option>
                                                ))}
                                            </Select>
                                        ) : (
                                            <Tooltip title="未找到可用模型，请先在API配置中添加模型">
                                                <Typography.Text type="secondary" style={{ cursor: 'help' }}>未找到可用模型</Typography.Text>
                                            </Tooltip>
                                        )}
                                    </div>
                                </div>
                            </Card>

                            {error && <Alert message={error} type="error" style={{ marginBottom: '16px' }} />}

                            <div className="messages-area">
                                {isLoading && messages.length === 0 && (
                                    <div style={{ display: 'flex', justifyContent: 'center', padding: '24px' }}>
                                        <Spin />
                                    </div>
                                )}
                                {messages.map((msg) => (
                                    <div key={msg.id} className={`message ${msg.role}`}>
                                        <Card size="small" style={{ display: 'inline-block', maxWidth: '80%' }}>
                                            <div style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>
                                                {msg.content}
                                            </div>
                                            <div style={{ marginTop: '8px', textAlign: 'right', color: 'rgba(0, 0, 0, 0.45)', fontSize: '12px' }}>
                                                {new Date(msg.created_at).toLocaleTimeString()}
                                                {msg.role === 'assistant' && (
                                                    <Tooltip title="删除此消息">
                                                        <Button
                                                            type="text"
                                                            size="small"
                                                            icon={<DeleteOutlined />}
                                                            onClick={() => handleDeleteMessage(msg.id)}
                                                            style={{ marginLeft: '8px', padding: '0' }}
                                                        />
                                                    </Tooltip>
                                                )}
                                            </div>
                                        </Card>
                                    </div>
                                ))}
                                {isSending && (
                                    <div className="message assistant">
                                        <Card size="small" style={{ display: 'inline-block', maxWidth: '80%', padding: '12px' }}>
                                            <Spin size="small" />
                                        </Card>
                                    </div>
                                )}
                                <div ref={messagesEndRef} />
                            </div>

                            <div className="input-area">
                                <Input.TextArea
                                    value={newMessage}
                                    onChange={(e) => setNewMessage(e.target.value)}
                                    placeholder="输入消息..."
                                    rows={3}
                                    disabled={isSending || isLoading}
                                    onPressEnter={(e) => {
                                        if (!e.shiftKey) {
                                            e.preventDefault(); // Prevent newline on Enter
                                            handleSendMessage();
                                        }
                                    }}
                                />
                                <Button
                                    type="primary"
                                    icon={<SendOutlined />}
                                    onClick={handleSendMessage}
                                    disabled={isSending || isLoading || !newMessage.trim()}
                                />
                            </div>
                        </div>
                    ) : (
                        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column', padding: '16px' }}>
                            <Typography.Title level={4} style={{ marginBottom: '16px', color: 'rgba(0, 0, 0, 0.45)' }}>
                                请选择或创建一个对话
                            </Typography.Title>
                            <Button
                                type="primary"
                                icon={<CommentOutlined />}
                                onClick={showNewConversationModal}
                            >
                                新建对话
                            </Button>
                        </div>
                    )}
                </Content>
            </Layout>

            {/* 新建对话模态框 */}
            {renderNewConversationModal()}
        </Layout>
    );
};

export default AIChat;