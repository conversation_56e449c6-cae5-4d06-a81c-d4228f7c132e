import React, { useState, useEffect } from 'react';
import { Form, Input, Select, DatePicker, Button, InputNumber, Space, Switch, Radio, Divider, Card, Tag, message } from 'antd';
import { PlusOutlined, MinusCircleOutlined, NodeIndexOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { invoke } from '@tauri-apps/api/core';
import type { TestCaseStatus, TestCasePriority } from '../types'; // Import types

const { Option } = Select;
const { TextArea } = Input;

interface Task {
  id: string;
  name: string;
  description: string;
  task_type: 'Manual' | 'Automated' | 'Custom';
  status: 'Unbuilt' | 'Pending' | 'Running' | 'Completed' | 'Failed';
  progress: number;
  steps: { name: string; command: string; order: number }[];
  working_dir?: string;
  schedule: string;
  scheduled_at?: string;
  created_at: string;
  updated_at: string;
  agent_id?: string;
  parent_id?: string;
  is_group: boolean;
  execution_mode?: 'Serial' | 'Parallel';
  child_tasks?: string[];
  test_cases?: string[];
  loop_count?: number;
  lastRun?: string;
  scheduleTime?: string;
  custom_command?: string;
}

interface TestCase {
  id: string;
  name: string;
  description?: string;
  priority?: TestCasePriority;
  status?: TestCaseStatus;
  module_id?: string;
  is_automated: boolean;
}

interface TaskFormProps {
  initialValues?: any;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

const TaskForm: React.FC<TaskFormProps> = ({ initialValues, onSubmit, onCancel }) => {
  const [form] = Form.useForm();
  const [isTaskGroup, setIsTaskGroup] = useState(false);
  const [executionMode, setExecutionMode] = useState<'Serial' | 'Parallel'>('Serial');
  const [availableTasks, setAvailableTasks] = useState<Task[]>([]);
  const [selectedChildTasks, setSelectedChildTasks] = useState<string[]>([]);
  const [availableAgents, setAvailableAgents] = useState<{id: string; name: string; status: string}[]>([]);
  const [availableTestCases, setAvailableTestCases] = useState<TestCase[]>([]);
  const [selectedTestCases, setSelectedTestCases] = useState<string[]>([]);
  const [taskType, setTaskType] = useState<'Manual' | 'Automated' | 'Custom'>('Manual');
  const [selectedAgent, setSelectedAgent] = useState<string>('');

  // 获取可用的任务列表（排除当前任务和任务组）
  useEffect(() => {
    const fetchAvailableTasks = async () => {
      try {
        const tasks = await invoke<Task[]>('get_tasks');
        // 过滤掉当前任务和已经是任务组的任务
        const filteredTasks = tasks.filter(task => 
          (!initialValues || task.id !== initialValues.id) && 
          !task.is_group && 
          !task.parent_id
        );
        setAvailableTasks(filteredTasks);
      } catch (error) {
        console.error('获取可用任务失败:', error);
      }
    };

    const fetchTestCases = async () => {
      try {
        const cases = await invoke<TestCase[]>('get_test_cases');
        setAvailableTestCases(cases);
      } catch (error) {
        console.error('获取测试用例失败:', error);
      }
    };
    
    // 获取可用的执行机列表
    const fetchAvailableAgents = async () => {
      try {
        const agents = await invoke<Array<{id: string; name: string; status: string}>>('get_agents');
        setAvailableAgents(agents.map(agent => ({
          id: agent.id,
          name: agent.name,
          status: agent.status
        })));
        // 如果当前选中的执行机离线，显示警告
        if (selectedAgent) {
          const agent = agents.find(a => a.id === selectedAgent);
          if (agent && agent.status !== 'Online') {
            message.warning(`当前选中的执行机 ${agent.name} 处于${agent.status === 'Offline' ? '离线' : '忙碌'}状态`);
          }
        }
      } catch (error) {
        console.error('获取执行机列表失败:', error);
      }
    };
    
    fetchAvailableTasks();
    fetchAvailableAgents();
    fetchTestCases();
  }, [initialValues]);

  useEffect(() => {
    if (initialValues) {
      setIsTaskGroup(initialValues.is_group || false);
      setExecutionMode(initialValues.execution_mode || 'Serial');
      setSelectedChildTasks(initialValues.child_tasks || []);
      setSelectedTestCases(initialValues.test_cases || []);
      setTaskType(initialValues.task_type || 'Manual');
      
      form.setFieldsValue({
        ...initialValues,
        nextRun: initialValues.nextRun ? dayjs(initialValues.nextRun) : null,
      });
    } else {
      // 设置新建任务的默认值
      form.setFieldsValue({
        loop_count: 1,
        steps: [{ name: '', command: '' }],
        is_group: false,
        execution_mode: 'Serial',
        child_tasks: [],
        test_cases: [],
        task_type: 'Manual',
        progress: 0,
      });
    }
  }, [initialValues, form]);

  const handleSubmit = (values: any) => {
    // 添加任务组相关字段
    const taskData = {
      ...values,
      is_group: isTaskGroup,
      execution_mode: isTaskGroup ? executionMode : undefined,
      child_tasks: isTaskGroup ? selectedChildTasks : undefined,
      test_cases: selectedTestCases,
      task_type: taskType,
      progress: 0,
    };
    onSubmit(taskData);
  };
  
  // 处理子任务选择变化
  const handleChildTaskChange = (taskIds: string[]) => {
    setSelectedChildTasks(taskIds);
    form.setFieldsValue({ child_tasks: taskIds });
  };

  // 处理测试用例选择变化
  const handleTestCaseChange = (caseIds: string[]) => {
    setSelectedTestCases(caseIds);
    // 根据选中的测试用例判断任务类型
    const selectedCases = availableTestCases.filter(c => caseIds.includes(c.id));
    const hasAutomatedCase = selectedCases.some(c => c.is_automated);
    const hasManualCase = selectedCases.some(c => !c.is_automated);
    
    if (hasAutomatedCase && !hasManualCase) {
      setTaskType('Automated');
    } else if (hasManualCase) {
      setTaskType('Manual');
    }
  };

  // 处理任务类型变化
  const handleTaskTypeChange = (value: 'Manual' | 'Automated' | 'Custom') => {
    setTaskType(value);
    if (value === 'Custom') {
      form.setFieldsValue({ steps: [] }); // 清空步骤列表
    }
  };

  return (
    <Form form={form} layout="vertical" onFinish={handleSubmit}>
      <Form.Item name="name" label="任务名称" rules={[{ required: true, message: '请输入任务名称' }]}>
        <Input />
      </Form.Item>

      <Form.Item label="测试用例" name="test_cases">
        <Select
          mode="multiple"
          placeholder="请选择测试用例"
          style={{ width: '100%' }}
          value={selectedTestCases}
          onChange={handleTestCaseChange}
          optionLabelProp="label"
        >
          {availableTestCases.map(testCase => (
            <Select.Option key={testCase.id} value={testCase.id} label={testCase.name}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>{testCase.name}</span>
                <Tag color={testCase.is_automated ? 'green' : 'orange'}>
                  {testCase.is_automated ? '自动化' : '手工'}
                </Tag>
              </div>
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      
      {/* 任务类型选择 */}
      <Form.Item label="任务类型">
        <Space>
          <Switch 
            checkedChildren="任务组" 
            unCheckedChildren="普通任务" 
            checked={isTaskGroup} 
            onChange={(checked) => setIsTaskGroup(checked)} 
          />
          {isTaskGroup && <Tag color="blue">任务组</Tag>}
        </Space>
      </Form.Item>
      
      {/* 任务组特有配置 */}
      {isTaskGroup ? (
        <Card title="任务组配置" size="small" style={{ marginBottom: 16 }}>
          <Form.Item label="执行模式" name="execution_mode">
            <Radio.Group 
              value={executionMode} 
              onChange={(e) => setExecutionMode(e.target.value)}
            >
              <Radio.Button value="Serial">串行执行</Radio.Button>
              <Radio.Button value="Parallel">并行执行</Radio.Button>
            </Radio.Group>
          </Form.Item>
          
          <Form.Item label="选择子任务" name="child_tasks">
            <Select
              mode="multiple"
              placeholder="请选择子任务"
              style={{ width: '100%' }}
              value={selectedChildTasks}
              onChange={handleChildTaskChange}
              optionLabelProp="label"
            >
              {availableTasks.map(task => (
                <Select.Option key={task.id} value={task.id} label={task.name}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>{task.name}</span>
                    <Tag color={task.status === 'Completed' ? 'green' : 'blue'}>{task.status}</Tag>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Card>
      ) : (
        <>
          <Form.Item name="task_type" label="任务类型" rules={[{ required: true, message: '请选择任务类型' }]}>
            <Select value={taskType} onChange={handleTaskTypeChange}>
              <Option value="Manual">手工任务</Option>
              <Option value="Automated">自动化任务</Option>
              <Option value="Custom">自定义任务</Option>
            </Select>
          </Form.Item>

          <Form.Item name="agent_id" label="执行机" rules={[{ required: true, message: '请选择执行机' }]}>
            <Select
              value={selectedAgent}
              onChange={(value) => {
                setSelectedAgent(value);
                form.setFieldsValue({ agent_id: value });
              }}
            >
              {availableAgents.map(agent => (
                <Option key={agent.id} value={agent.id}>
                  <Space>
                    {agent.name}
                    <Tag color={agent.status === 'Online' ? 'green' : agent.status === 'Busy' ? 'orange' : 'red'}>
                      {agent.status === 'Online' ? '在线' : agent.status === 'Busy' ? '忙碌' : '离线'}
                    </Tag>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          {taskType === 'Custom' && (
            <Form.Item
              name="custom_command"
              label="自定义命令"
              rules={[{ required: true, message: '请输入自定义命令' }]}
              help="支持shell命令、python脚本或shell脚本，例如: python test.py 或 bash test.sh"
            >
              <Input.TextArea rows={4} placeholder="请输入要执行的命令或脚本路径" />
            </Form.Item>
          )}
          
          <Form.List name="steps">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                    <Form.Item
                      {...restField}
                      name={[name, 'name']}
                      rules={[{ required: true, message: '请输入步骤名称' }]}
                    >
                      <Input placeholder="步骤名称" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'command']}
                      rules={[{ required: true, message: '请输入Shell命令' }]}
                    >
                      <TextArea rows={2} placeholder="Shell命令" />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                ))}
                <Form.Item>
                  <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                    添加步骤
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </>
      )}

      <Divider />
      
      <Form.Item name="nextRun" label="下次执行时间">
        <DatePicker showTime />
      </Form.Item>
      <Form.Item name="loop_count" label="循环次数" rules={[{ required: true, message: '请输入循环次数' }]}>
        <InputNumber min={1} />
      </Form.Item>
      <Form.Item name="description" label="任务描述">
        <TextArea rows={4} />
      </Form.Item>
      <Form.Item>
        <Button type="primary" htmlType="submit">
          提交
        </Button>
        <Button onClick={onCancel} style={{ marginLeft: 8 }}>
          取消
        </Button>
      </Form.Item>
    </Form>
  );
};

export default TaskForm;