import React, { useState, useEffect, useCallback } from 'react';
import {
  Table, Button, Modal, Form, Input, Space, Popconfirm,
  message, Card, Typography, Select, Tag, Divider
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  KeyOutlined, ApiOutlined, SaveOutlined
} from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';
import { ApiConfig } from '../types';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface APIConfigManagementProps {
  apiConfigs: ApiConfig[];
  refreshApiConfigs: () => Promise<void>;
}

const APIConfigManagement: React.FC<APIConfigManagementProps> = ({
  apiConfigs,
  refreshApiConfigs
}) => {
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<ApiConfig | null>(null);
  const [loading, setLoading] = useState(false);
  const [modelInput, setModelInput] = useState('');
  const [tempModels, setTempModels] = useState<string[]>([]);

  // 重置表单和临时状态
  const resetForm = () => {
    form.resetFields();
    setEditingConfig(null);
    setTempModels([]);
    setModelInput('');
  };

  // 显示添加/编辑模态框
  const showModal = (config?: ApiConfig) => {
    resetForm();
    if (config) {
      setEditingConfig(config);
      setTempModels(config.models || []);

      // 打印调试信息
      // 编辑现有配置

      // 处理字段名不一致的问题
      form.setFieldsValue({
        provider: config.provider,
        api_key: config.api_key,
        api_url: config.base_url || '', // 使用 base_url 填充 api_url 字段
        base_url: config.base_url || '', // 同时设置隐藏字段
      });
    } else {
      console.log('创建新配置');
    }
    setIsModalVisible(true);
  };

  // 处理模态框取消
  const handleCancel = () => {
    setIsModalVisible(false);
    resetForm();
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      // 验证表单字段
      const values = await form.validateFields();
      setLoading(true);

      // 验证API密钥不为空
      if (!values.api_key || values.api_key.trim() === '') {
        message.error('API密钥不能为空');
        return;
      }

      // 验证提供商名称不为空
      if (!values.provider || values.provider.trim() === '') {
        message.error('提供商名称不能为空');
        return;
      }

      // 如果没有指定API URL，根据提供商设置默认值
      if (!values.api_url || values.api_url.trim() === '') {
        // 根据提供商设置默认API URL
        const defaultUrls: Record<string, string> = {
          'openai': 'https://api.openai.com/v1/chat/completions',
          'anthropic': 'https://api.anthropic.com/v1/messages',
          'openrouter': 'https://openrouter.ai/api/v1/chat/completions',
          'gemini': 'https://generativelanguage.googleapis.com/v1beta/models',
          'google': 'https://generativelanguage.googleapis.com/v1beta/models',
        };

        const provider = values.provider.toLowerCase();
        if (defaultUrls[provider]) {
          values.api_url = defaultUrls[provider];
          console.log(`使用默认API URL: ${values.api_url}`);
        } else {
          // 如果没有默认URL，设置为空字符串
          values.api_url = '';
        }
      }

      // 验证模型列表
      if (tempModels.length === 0) {
        // 如果没有指定模型，根据提供商设置默认模型
        const defaultModels: Record<string, string[]> = {
          'openai': ['gpt-3.5-turbo', 'gpt-4'],
          'anthropic': ['claude-3-opus-20240229', 'claude-3-sonnet-20240229'],
          'openrouter': ['openai/gpt-3.5-turbo', 'anthropic/claude-3-opus-20240229'],
          'gemini': ['gemini-pro', 'gemini-1.5-pro'],
          'google': ['gemini-pro', 'gemini-1.5-pro'],
        };

        const provider = values.provider.toLowerCase();
        if (defaultModels[provider]) {
          setTempModels(defaultModels[provider]);
          console.log(`使用默认模型: ${defaultModels[provider].join(', ')}`);
        } else {
          // 如果没有默认模型，添加一个示例模型
          setTempModels(['default-model']);
        }
      }

      // 构建配置数据
      const configData = {
        ...values,
        // 确保 base_url 字段存在（后端使用 base_url 而不是 api_url）
        base_url: values.api_url,
        models: tempModels,
      };

      console.log('准备保存的API配置:', configData);

      if (editingConfig) {
        // 更新现有配置
        await invoke('update_api_config', {
          id: editingConfig.provider,
          config: configData,
        });
        message.success('API配置已更新');
      } else {
        // 创建新配置
        await invoke('save_api_config', {
          config: configData,
        });
        message.success('API配置已创建');
      }

      // 关闭模态框并刷新数据
      setIsModalVisible(false);
      resetForm();
      refreshApiConfigs();
    } catch (error: any) {
      if (!error.errorFields) {
        // 非表单验证错误
        console.error('Failed to save API config:', error);

        // 尝试提取更有用的错误信息
        let errorMessage = error.toString();
        if (errorMessage.includes('API key')) {
          errorMessage = 'API密钥格式无效';
        } else if (errorMessage.includes('provider')) {
          errorMessage = '提供商名称无效';
        } else if (errorMessage.includes('database')) {
          errorMessage = '数据库操作失败，请重试';
        }

        // 显示错误提示
        message.error(`保存失败: ${errorMessage}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // 处理删除配置
  const handleDelete = async (provider: string) => {
    try {
      setLoading(true);
      await invoke('delete_api_config', { provider });
      message.success('API配置已删除');
      refreshApiConfigs();
    } catch (error: any) {
      console.error('Failed to delete API config:', error);
      message.error(`删除失败: ${error.toString()}`);
    } finally {
      setLoading(false);
    }
  };

  // 添加模型到临时列表
  const addModel = () => {
    if (modelInput && !tempModels.includes(modelInput)) {
      setTempModels([...tempModels, modelInput]);
      setModelInput('');
    }
  };

  // 从临时列表移除模型
  const removeModel = (model: string) => {
    setTempModels(tempModels.filter(m => m !== model));
  };

  // 表格列定义
  const columns = [
    {
      title: '提供商',
      dataIndex: 'provider',
      key: 'provider',
    },
    {
      title: 'API Key',
      dataIndex: 'api_key',
      key: 'api_key',
      render: (text: string) => `${text.substring(0, 8)}...`,
    },
    {
      title: 'API URL',
      dataIndex: 'base_url',
      key: 'base_url',
      render: (text: string) => text || '-',
    },
    {
      title: '可用模型',
      dataIndex: 'models',
      key: 'models',
      render: (models: string[]) => (
        <div style={{ maxWidth: '300px', overflow: 'hidden' }}>
          {models && models.length > 0 ? (
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
              {models.map(model => (
                <Tag key={model} color="blue">{model}</Tag>
              ))}
            </div>
          ) : (
            <Text type="secondary">无可用模型</Text>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: ApiConfig) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个API配置吗？"
            description="删除后将无法恢复，相关的对话可能无法继续使用。"
            onConfirm={() => handleDelete(record.provider)}
            okText="删除"
            cancelText="取消"
            okButtonProps={{ danger: true }}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <Title level={4} style={{ margin: 0 }}>API配置管理</Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => showModal()}
          >
            添加API配置
          </Button>
        </div>

        <Paragraph>
          在这里管理您的AI服务提供商API配置。您可以添加多个提供商的API密钥，并为每个提供商配置可用的模型。
        </Paragraph>

        <Table
          dataSource={apiConfigs}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={false}
        />
      </Card>

      <Modal
        title={editingConfig ? "编辑API配置" : "添加API配置"}
        open={isModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={handleSubmit}
            icon={<SaveOutlined />}
          >
            保存
          </Button>,
        ]}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="provider"
            label="提供商名称"
            rules={[{ required: true, message: '请输入提供商名称' }]}
          >
            <Input
              prefix={<ApiOutlined />}
              placeholder="例如: OpenAI, Anthropic, 百度, 讯飞等"
            />
          </Form.Item>

          <Form.Item
            name="api_key"
            label="API密钥"
            rules={[{ required: true, message: '请输入API密钥' }]}
          >
            <Input.Password
              prefix={<KeyOutlined />}
              placeholder="输入API密钥"
            />
          </Form.Item>

          <Form.Item
            name="api_url"
            label="API URL (可选)"
          >
            <Input
              placeholder="例如: https://api.openai.com/v1/chat/completions"
            />
          </Form.Item>

          {/* 隐藏字段，用于保持与后端字段名一致 */}
          <Form.Item
            name="base_url"
            hidden
          >
            <Input />
          </Form.Item>

          <Divider orientation="left">可用模型</Divider>

          <div style={{ marginBottom: '16px' }}>
            <Input
              placeholder="输入模型名称，例如: gpt-4, claude-3-opus-20240229"
              value={modelInput}
              onChange={(e) => setModelInput(e.target.value)}
              onPressEnter={addModel}
              style={{ width: 'calc(100% - 90px)' }}
            />
            <Button
              type="primary"
              onClick={addModel}
              style={{ marginLeft: '8px' }}
            >
              添加
            </Button>
          </div>

          <div style={{ marginBottom: '16px' }}>
            {tempModels.length > 0 ? (
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                {tempModels.map(model => (
                  <Tag
                    key={model}
                    closable
                    onClose={() => removeModel(model)}
                    color="blue"
                  >
                    {model}
                  </Tag>
                ))}
              </div>
            ) : (
              <Text type="secondary">尚未添加任何模型</Text>
            )}
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default APIConfigManagement;
