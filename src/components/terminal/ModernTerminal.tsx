import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { SearchAddon } from '@xterm/addon-search';
import { WebLinksAddon } from '@xterm/addon-web-links';
import '@xterm/xterm/css/xterm.css';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { message, Dropdown, Button, Input, Tooltip } from 'antd';
import {
  PlusOutlined,
  CloseOutlined,
  MoreOutlined,
  SearchOutlined,
  SettingOutlined,
  SplitCellsOutlined,
  DeleteOutlined,
  CopyOutlined,
  ReloadOutlined,
  DownOutlined,
  UpOutlined,
  MinusOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons';
import './ModernTerminal.css';

interface TerminalSession {
  id: string;
  ptyId: number;
  terminal: Terminal;
  fitAddon: FitAddon;
  searchAddon: SearchAddon;
  webLinksAddon: WebLinksAddon;
  title: string;
  projectId: string;
  cwd: string;
  isActive: boolean;
}

interface ModernTerminalProps {
  projectId?: string;
  projectPath?: string;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

const ModernTerminal: React.FC<ModernTerminalProps> = ({ 
  projectId, 
  projectPath, 
  collapsed = false,
  onToggleCollapse 
}) => {
  const [sessions, setSessions] = useState<TerminalSession[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  const [searchVisible, setSearchVisible] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const terminalRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const sessionCounter = useRef(1);
  const resizeTimeouts = useRef<{ [key: string]: NodeJS.Timeout }>({});

  // VS Code 风格的终端主题
  const vsCodeTheme = {
    background: '#1e1e1e',
    foreground: '#cccccc',
    cursor: '#cccccc',
    cursorAccent: '#1e1e1e',
    selection: '#264f78',
    selectionForeground: '#ffffff',
    
    // ANSI 颜色 - VS Code 风格
    black: '#000000',
    red: '#cd3131',
    green: '#0dbc79',
    yellow: '#e5e510',
    blue: '#2472c8',
    magenta: '#bc3fbc',
    cyan: '#11a8cd',
    white: '#e5e5e5',
    
    // 明亮颜色
    brightBlack: '#666666',
    brightRed: '#f14c4c',
    brightGreen: '#23d18b',
    brightYellow: '#f5f543',
    brightBlue: '#3b8eea',
    brightMagenta: '#d670d6',
    brightCyan: '#29b8db',
    brightWhite: '#e5e5e5',
  };

  const createTerminalSession = useCallback(async (title?: string) => {
    if (!projectId) {
      message.error('无法创建终端：未选择项目。');
      return;
    }

    console.log('Creating terminal session for project:', projectId);
    console.log('Project path:', projectPath);

    try {
      console.log('Invoking create_local_pty...');
      const ptyId = await invoke<number>('create_local_pty', {
        projectId,
        initialCommand: null
      });
      console.log('PTY created with ID:', ptyId);

      const sessionId = `terminal-${Date.now()}-${sessionCounter.current++}`;
      const sessionTitle = title || `终端 ${sessionCounter.current - 1}`;
      console.log('Creating session:', sessionId, 'with title:', sessionTitle);

      const terminal = new Terminal({
        cursorBlink: true,
        rows: 24,
        cols: 80,
        convertEol: true,
        fontFamily: '"Cascadia Code", "JetBrains Mono", "Fira Code", "SF Mono", Menlo, Monaco, "Courier New", monospace',
        fontSize: 14,
        lineHeight: 1.2,
        fontWeight: 'normal',
        fontWeightBold: 'bold',
        allowTransparency: true,
        bellStyle: 'none',
        cursorStyle: 'block',
        cursorWidth: 1,
        drawBoldTextInBrightColors: true,
        fastScrollModifier: 'alt',
        fastScrollSensitivity: 5,
        macOptionIsMeta: true,
        rightClickSelectsWord: true,
        scrollback: 10000,
        scrollSensitivity: 1,
        smoothScrollDuration: 0,
        tabStopWidth: 4,
        theme: vsCodeTheme
      });

      const fitAddon = new FitAddon();
      const searchAddon = new SearchAddon();
      const webLinksAddon = new WebLinksAddon();

      terminal.loadAddon(fitAddon);
      terminal.loadAddon(searchAddon);
      terminal.loadAddon(webLinksAddon);

      const session: TerminalSession = {
        id: sessionId,
        ptyId,
        terminal,
        fitAddon,
        searchAddon,
        webLinksAddon,
        title: sessionTitle,
        projectId,
        cwd: projectPath || '',
        isActive: false
      };

      setSessions(prev => {
        const newSessions = prev.map(s => ({ ...s, isActive: false }));
        return [...newSessions, { ...session, isActive: true }];
      });
      setActiveSessionId(sessionId);

      // 延迟初始化终端
      setTimeout(() => {
        const container = terminalRefs.current[sessionId];
        if (container && terminal) {
          terminal.open(container);
          fitAddon.fit();

          // 处理输入
          terminal.onData(async (data) => {
            try {
              await invoke('write_to_pty', { ptyId, data });
            } catch (error) {
              console.error('Failed to write to PTY:', error);
              message.error(`写入终端失败: ${error}`);
            }
          });

          // 处理调整大小
          const handleResize = () => {
            if (resizeTimeouts.current[sessionId]) {
              clearTimeout(resizeTimeouts.current[sessionId]);
            }
            resizeTimeouts.current[sessionId] = setTimeout(() => {
              if (fitAddon && terminal) {
                fitAddon.fit();
                const { cols, rows } = terminal;
                invoke('resize_pty', { ptyId, cols, rows })
                  .catch(err => console.error('Failed to resize PTY:', err));
              }
            }, 250);
          };

          const resizeObserver = new ResizeObserver(handleResize);
          resizeObserver.observe(container);

          // 初始调整大小
          setTimeout(handleResize, 100);

          // 监听输出
          const outputEventName = `pty-output-${ptyId}`;
          listen<string>(outputEventName, (event) => {
            terminal.write(event.payload);
          });

          terminal.focus();
        }
      }, 100);

    } catch (error) {
      console.error('Failed to create terminal session:', error);
      console.error('Error details:', {
        projectId,
        projectPath,
        error: error instanceof Error ? error.message : String(error)
      });
      message.error(`创建终端失败: ${error instanceof Error ? error.message : String(error)}`);

      // 如果是 PTY 创建失败，显示更详细的错误信息
      if (String(error).includes('create_local_pty')) {
        message.error('终端后端服务可能未正常启动，请检查 Tauri 后端服务');
      }
    }
  }, [projectId, projectPath]);

  const closeTerminalSession = useCallback(async (sessionId: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (!session) return;

    try {
      await invoke('close_pty', { ptyId: session.ptyId });
      session.terminal.dispose();
      
      if (resizeTimeouts.current[sessionId]) {
        clearTimeout(resizeTimeouts.current[sessionId]);
        delete resizeTimeouts.current[sessionId];
      }

      setSessions(prev => prev.filter(s => s.id !== sessionId));
      
      if (activeSessionId === sessionId) {
        const remainingSessions = sessions.filter(s => s.id !== sessionId);
        if (remainingSessions.length > 0) {
          setActiveSessionId(remainingSessions[0].id);
          setSessions(prev => prev.map(s => ({
            ...s,
            isActive: s.id === remainingSessions[0].id
          })));
        } else {
          setActiveSessionId(null);
        }
      }

      delete terminalRefs.current[sessionId];
    } catch (error) {
      console.error('Failed to close terminal session:', error);
      message.error(`关闭终端失败: ${error}`);
    }
  }, [sessions, activeSessionId]);

  const switchToSession = useCallback((sessionId: string) => {
    setActiveSessionId(sessionId);
    setSessions(prev => prev.map(s => ({
      ...s,
      isActive: s.id === sessionId
    })));
    
    // 聚焦到对应的终端
    setTimeout(() => {
      const session = sessions.find(s => s.id === sessionId);
      if (session) {
        session.terminal.focus();
      }
    }, 50);
  }, [sessions]);

  // 搜索功能
  const handleSearch = useCallback((term: string) => {
    const activeSession = sessions.find(s => s.id === activeSessionId);
    if (activeSession && term) {
      activeSession.searchAddon.findNext(term);
    }
  }, [sessions, activeSessionId]);

  // 快捷键处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+Shift+` 新建终端
      if (e.ctrlKey && e.shiftKey && e.key === '`') {
        e.preventDefault();
        createTerminalSession();
      }
      // Ctrl+Shift+W 关闭当前终端
      else if (e.ctrlKey && e.shiftKey && e.key === 'W') {
        e.preventDefault();
        if (activeSessionId && sessions.length > 1) {
          closeTerminalSession(activeSessionId);
        }
      }
      // Ctrl+Shift+T 新建终端
      else if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        createTerminalSession();
      }
      // Ctrl+Tab 切换到下一个终端
      else if (e.ctrlKey && e.key === 'Tab' && sessions.length > 1) {
        e.preventDefault();
        const currentIndex = sessions.findIndex(s => s.id === activeSessionId);
        const nextIndex = (currentIndex + 1) % sessions.length;
        switchToSession(sessions[nextIndex].id);
      }
      // Ctrl+Shift+Tab 切换到上一个终端
      else if (e.ctrlKey && e.shiftKey && e.key === 'Tab' && sessions.length > 1) {
        e.preventDefault();
        const currentIndex = sessions.findIndex(s => s.id === activeSessionId);
        const prevIndex = currentIndex === 0 ? sessions.length - 1 : currentIndex - 1;
        switchToSession(sessions[prevIndex].id);
      }
      // Ctrl+Shift+F 搜索
      else if (e.ctrlKey && e.shiftKey && e.key === 'F') {
        e.preventDefault();
        setSearchVisible(true);
      }
      // F11 全屏切换
      else if (e.key === 'F11') {
        e.preventDefault();
        setIsFullscreen(!isFullscreen);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [createTerminalSession, closeTerminalSession, switchToSession, activeSessionId, sessions, isFullscreen]);

  // 创建初始终端会话
  useEffect(() => {
    if (projectId && sessions.length === 0) {
      createTerminalSession();
    }
  }, [projectId, createTerminalSession, sessions.length]);

  // 清理
  useEffect(() => {
    return () => {
      sessions.forEach(session => {
        session.terminal.dispose();
        if (resizeTimeouts.current[session.id]) {
          clearTimeout(resizeTimeouts.current[session.id]);
        }
        invoke('close_pty', { ptyId: session.ptyId }).catch(console.error);
      });
    };
  }, []);

  return (
    <div className={`modern-terminal ${collapsed ? 'collapsed' : ''} ${isFullscreen ? 'fullscreen' : ''}`}>
      {/* 终端标题栏 */}
      <div className="terminal-header">
        <div className="terminal-header-left">
          <div className="terminal-title">
            <span className="terminal-icon">⚡</span>
            <span>终端</span>
            {sessions.length > 0 && (
              <span className="session-count">({sessions.length})</span>
            )}
          </div>
        </div>
        
        <div className="terminal-header-right">
          {/* 搜索按钮 */}
          <Tooltip title="搜索">
            <Button
              type="text"
              size="small"
              icon={<SearchOutlined />}
              onClick={() => setSearchVisible(!searchVisible)}
              className="header-btn"
            />
          </Tooltip>
          
          {/* 新建终端下拉菜单 */}
          <Dropdown
            menu={{
              items: [
                {
                  key: 'new-terminal',
                  label: '新建终端',
                  icon: <PlusOutlined />,
                  onClick: () => createTerminalSession()
                },
                {
                  key: 'split-terminal',
                  label: '分割终端',
                  icon: <SplitCellsOutlined />,
                  onClick: () => createTerminalSession('分割终端'),
                  disabled: sessions.length === 0
                },
                { type: 'divider' },
                {
                  key: 'clear-all',
                  label: '清除所有终端',
                  icon: <DeleteOutlined />,
                  onClick: () => {
                    sessions.forEach(session => closeTerminalSession(session.id));
                  },
                  disabled: sessions.length === 0
                }
              ]
            }}
            trigger={['click']}
            placement="bottomRight"
          >
            <Tooltip title="终端选项">
              <Button
                type="text"
                size="small"
                icon={<PlusOutlined />}
                className="header-btn"
              />
            </Tooltip>
          </Dropdown>
          
          {/* 全屏切换 */}
          <Tooltip title={isFullscreen ? "退出全屏" : "全屏"}>
            <Button
              type="text"
              size="small"
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="header-btn"
            />
          </Tooltip>
          
          {/* 折叠按钮 */}
          {onToggleCollapse && (
            <Tooltip title={collapsed ? "展开终端" : "折叠终端"}>
              <Button
                type="text"
                size="small"
                icon={collapsed ? <UpOutlined /> : <DownOutlined />}
                onClick={onToggleCollapse}
                className="header-btn"
              />
            </Tooltip>
          )}
        </div>
      </div>

      {/* 搜索栏 */}
      {searchVisible && (
        <div className="terminal-search">
          <Input
            placeholder="搜索终端内容..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              handleSearch(e.target.value);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearch(searchTerm);
              } else if (e.key === 'Escape') {
                setSearchVisible(false);
                setSearchTerm('');
              }
            }}
            suffix={
              <Button
                type="text"
                size="small"
                icon={<CloseOutlined />}
                onClick={() => {
                  setSearchVisible(false);
                  setSearchTerm('');
                }}
              />
            }
            autoFocus
          />
        </div>
      )}

      {/* 终端标签栏 */}
      {sessions.length > 0 && (
        <div className="terminal-tabs">
          {sessions.map(session => (
            <Dropdown
              key={session.id}
              menu={{
                items: [
                  {
                    key: 'rename',
                    label: '重命名',
                    icon: <SettingOutlined />,
                    onClick: () => {
                      const newTitle = prompt('请输入新的终端名称:', session.title);
                      if (newTitle && newTitle.trim()) {
                        setSessions(prev => prev.map(s =>
                          s.id === session.id ? { ...s, title: newTitle.trim() } : s
                        ));
                      }
                    }
                  },
                  {
                    key: 'duplicate',
                    label: '复制终端',
                    icon: <CopyOutlined />,
                    onClick: () => createTerminalSession(`${session.title} - 副本`)
                  },
                  { type: 'divider' },
                  {
                    key: 'clear',
                    label: '清除终端',
                    icon: <ReloadOutlined />,
                    onClick: () => {
                      session.terminal.clear();
                      message.success('终端已清除');
                    }
                  },
                  { type: 'divider' },
                  {
                    key: 'close',
                    label: '关闭终端',
                    icon: <CloseOutlined />,
                    onClick: () => closeTerminalSession(session.id),
                    disabled: sessions.length === 1
                  },
                  {
                    key: 'close-others',
                    label: '关闭其他终端',
                    icon: <MinusOutlined />,
                    onClick: () => {
                      sessions.forEach(s => {
                        if (s.id !== session.id) {
                          closeTerminalSession(s.id);
                        }
                      });
                    },
                    disabled: sessions.length === 1
                  }
                ]
              }}
              trigger={['contextMenu']}
            >
              <div
                className={`terminal-tab ${session.isActive ? 'active' : ''}`}
                onClick={() => switchToSession(session.id)}
              >
                <span className="tab-title">{session.title}</span>
                {sessions.length > 1 && (
                  <Button
                    type="text"
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      closeTerminalSession(session.id);
                    }}
                    className="tab-close-btn"
                  />
                )}
              </div>
            </Dropdown>
          ))}
        </div>
      )}

      {/* 终端内容区域 */}
      <div className="terminal-content">
        {sessions.length === 0 ? (
          <div className="terminal-empty">
            <div className="empty-content">
              <span className="empty-icon">⚡</span>
              <div style={{ marginBottom: '16px' }}>终端初始化中...</div>
              <div style={{ marginBottom: '8px', color: '#858585', fontSize: '13px' }}>
                如果终端长时间未响应，请尝试手动创建
              </div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => createTerminalSession()}
                style={{
                  background: '#007acc',
                  borderColor: '#007acc'
                }}
              >
                手动创建终端
              </Button>
            </div>
          </div>
        ) : (
          sessions.map(session => (
            <div
              key={session.id}
              ref={(el) => terminalRefs.current[session.id] = el}
              className={`terminal-instance ${session.isActive ? 'active' : ''}`}
              style={{
                display: session.isActive ? 'block' : 'none',
                width: '100%',
                height: '100%'
              }}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default ModernTerminal;
