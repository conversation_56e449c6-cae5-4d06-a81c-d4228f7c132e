/* Modern Terminal - VS Code 风格样式 */

.modern-terminal {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1e1e1e;
  color: #cccccc;
  font-family: 'Cascadia Code', 'JetBrains Mono', 'Fira Code', 'SF Mono', Menlo, Monaco, 'Courier New', monospace;
  border: 1px solid #2d2d30;
  border-radius: 0;
  overflow: hidden;
  position: relative;
  transition: all 0.2s ease;
}

.modern-terminal.collapsed {
  height: 40px;
  min-height: 40px;
}

.modern-terminal.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  border: none;
  border-radius: 0;
}

/* 终端标题栏 */
.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 35px;
  background: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  padding: 0 12px;
  flex-shrink: 0;
  user-select: none;
}

.terminal-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.terminal-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 500;
  color: #cccccc;
}

.terminal-icon {
  font-size: 14px;
  color: #007acc;
}

.session-count {
  font-size: 11px;
  color: #858585;
  font-weight: normal;
}

.terminal-header-right {
  display: flex;
  align-items: center;
  gap: 2px;
}

.header-btn {
  width: 28px !important;
  height: 28px !important;
  border: none !important;
  background: transparent !important;
  color: #cccccc !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  transition: all 0.15s ease !important;
  padding: 0 !important;
}

.header-btn:hover {
  background: #3e3e42 !important;
  color: #ffffff !important;
}

.header-btn:active {
  background: #4e4e52 !important;
}

/* 搜索栏 */
.terminal-search {
  background: #252526;
  border-bottom: 1px solid #3e3e42;
  padding: 8px 12px;
  flex-shrink: 0;
}

.terminal-search .ant-input {
  background: #3c3c3c !important;
  border: 1px solid #5a5a5a !important;
  color: #cccccc !important;
  font-size: 13px !important;
  border-radius: 4px !important;
}

.terminal-search .ant-input:focus {
  border-color: #007acc !important;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2) !important;
}

.terminal-search .ant-input::placeholder {
  color: #858585 !important;
}

/* 终端标签栏 */
.terminal-tabs {
  display: flex;
  background: #252526;
  border-bottom: 1px solid #3e3e42;
  overflow-x: auto;
  flex-shrink: 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.terminal-tabs::-webkit-scrollbar {
  display: none;
}

.terminal-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  border-right: 1px solid #3e3e42;
  cursor: pointer;
  transition: all 0.15s ease;
  min-width: 120px;
  max-width: 200px;
  position: relative;
  user-select: none;
}

.terminal-tab:hover {
  background: #2a2a2a;
}

.terminal-tab.active {
  background: #1e1e1e;
  border-bottom: 2px solid #007acc;
}

.terminal-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #007acc;
}

.tab-title {
  flex: 1;
  font-size: 13px;
  color: #cccccc;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.terminal-tab.active .tab-title {
  color: #ffffff;
  font-weight: 500;
}

.tab-close-btn {
  width: 16px !important;
  height: 16px !important;
  min-width: 16px !important;
  border: none !important;
  background: transparent !important;
  color: #858585 !important;
  padding: 0 !important;
  border-radius: 2px !important;
  opacity: 0;
  transition: all 0.15s ease !important;
}

.terminal-tab:hover .tab-close-btn {
  opacity: 1;
}

.tab-close-btn:hover {
  background: #e74c3c !important;
  color: #ffffff !important;
}

/* 终端内容区域 */
.terminal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #1e1e1e;
}

.terminal-instance {
  width: 100%;
  height: 100%;
  padding: 8px;
  background: #1e1e1e;
}

.terminal-instance.active {
  display: block !important;
}

/* 空状态 */
.terminal-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #1e1e1e;
}

.empty-content {
  text-align: center;
  color: #858585;
}

.empty-icon {
  font-size: 32px;
  display: block;
  margin-bottom: 8px;
  color: #007acc;
}

/* XTerm 样式覆盖 */
.modern-terminal .xterm {
  padding: 0 !important;
  background: #1e1e1e !important;
}

.modern-terminal .xterm-viewport {
  background: #1e1e1e !important;
}

.modern-terminal .xterm-screen {
  background: #1e1e1e !important;
}

.modern-terminal .xterm-decoration-overview-ruler {
  background: #1e1e1e !important;
}

/* 滚动条样式 */
.modern-terminal .xterm .xterm-viewport::-webkit-scrollbar {
  width: 14px;
  background: #1e1e1e;
}

.modern-terminal .xterm .xterm-viewport::-webkit-scrollbar-thumb {
  background: #424242;
  border-radius: 7px;
  border: 3px solid #1e1e1e;
}

.modern-terminal .xterm .xterm-viewport::-webkit-scrollbar-thumb:hover {
  background: #4e4e4e;
}

.modern-terminal .xterm .xterm-viewport::-webkit-scrollbar-track {
  background: #1e1e1e;
}

/* 选择文本样式 */
.modern-terminal .xterm .xterm-selection div {
  background: #264f78 !important;
}

/* 光标样式 */
.modern-terminal .xterm .xterm-cursor-layer .xterm-cursor {
  background: #cccccc !important;
  color: #1e1e1e !important;
}

/* 链接样式 */
.modern-terminal .xterm .xterm-link {
  color: #4fc3f7 !important;
  text-decoration: underline !important;
}

.modern-terminal .xterm .xterm-link:hover {
  color: #29b6f6 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .terminal-header {
    padding: 0 8px;
  }
  
  .terminal-tab {
    min-width: 100px;
    padding: 6px 8px;
  }
  
  .tab-title {
    font-size: 12px;
  }
  
  .header-btn {
    width: 24px !important;
    height: 24px !important;
  }
}

/* 动画效果 */
.terminal-tab {
  animation: tabSlideIn 0.2s ease-out;
}

@keyframes tabSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.terminal-instance {
  animation: contentFadeIn 0.15s ease-out;
}

@keyframes contentFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 折叠状态下隐藏内容 */
.modern-terminal.collapsed .terminal-search,
.modern-terminal.collapsed .terminal-tabs,
.modern-terminal.collapsed .terminal-content {
  display: none;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .modern-terminal {
    border-color: #ffffff;
  }
  
  .terminal-header {
    background: #000000;
    border-bottom-color: #ffffff;
  }
  
  .terminal-tab.active {
    background: #000000;
    border-bottom-color: #ffffff;
  }
}
