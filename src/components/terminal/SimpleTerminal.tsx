import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import '@xterm/xterm/css/xterm.css';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { message, Button, Tooltip } from 'antd';
import {
  PlusOutlined,
  CloseOutlined,
  DownOutlined,
  UpOutlined,
  ReloadOutlined
} from '@ant-design/icons';

interface SimpleTerminalProps {
  projectId?: string;
  projectPath?: string;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

const SimpleTerminal: React.FC<SimpleTerminalProps> = ({ 
  projectId, 
  projectPath, 
  collapsed = false,
  onToggleCollapse 
}) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const [terminal, setTerminal] = useState<Terminal | null>(null);
  const [fitAddon, setFitAddon] = useState<FitAddon | null>(null);
  const [ptyId, setPtyId] = useState<number | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 创建终端
  const createTerminal = async () => {
    if (!projectId || isLoading) {
      return;
    }

    setIsLoading(true);
    console.log('Creating simple terminal for project:', projectId);

    try {
      // 创建 PTY
      console.log('Calling create_local_pty...');
      const newPtyId = await invoke<number>('create_local_pty', { 
        projectId, 
        initialCommand: null 
      });
      console.log('PTY created with ID:', newPtyId);
      setPtyId(newPtyId);

      // 创建终端实例
      const newTerminal = new Terminal({
        cursorBlink: true,
        rows: 24,
        cols: 80,
        convertEol: true,
        fontFamily: '"Cascadia Code", "JetBrains Mono", "Fira Code", monospace',
        fontSize: 14,
        lineHeight: 1.2,
        theme: {
          background: '#1e1e1e',
          foreground: '#cccccc',
          cursor: '#cccccc',
          selection: '#264f78',
          black: '#000000',
          red: '#cd3131',
          green: '#0dbc79',
          yellow: '#e5e510',
          blue: '#2472c8',
          magenta: '#bc3fbc',
          cyan: '#11a8cd',
          white: '#e5e5e5',
          brightBlack: '#666666',
          brightRed: '#f14c4c',
          brightGreen: '#23d18b',
          brightYellow: '#f5f543',
          brightBlue: '#3b8eea',
          brightMagenta: '#d670d6',
          brightCyan: '#29b8db',
          brightWhite: '#e5e5e5',
        }
      });

      const newFitAddon = new FitAddon();
      newTerminal.loadAddon(newFitAddon);

      setTerminal(newTerminal);
      setFitAddon(newFitAddon);

      // 延迟初始化
      setTimeout(() => {
        if (terminalRef.current && newTerminal) {
          newTerminal.open(terminalRef.current);
          newFitAddon.fit();

          // 处理输入
          newTerminal.onData(async (data) => {
            try {
              await invoke('write_to_pty', { ptyId: newPtyId, data });
            } catch (error) {
              console.error('Failed to write to PTY:', error);
            }
          });

          // 监听输出
          const outputEventName = `pty-output-${newPtyId}`;
          listen<string>(outputEventName, (event) => {
            newTerminal.write(event.payload);
          });

          // 处理调整大小
          const handleResize = () => {
            if (newFitAddon && newTerminal) {
              newFitAddon.fit();
              const { cols, rows } = newTerminal;
              invoke('resize_pty', { ptyId: newPtyId, cols, rows })
                .catch(err => console.error('Failed to resize PTY:', err));
            }
          };

          const resizeObserver = new ResizeObserver(handleResize);
          resizeObserver.observe(terminalRef.current);

          setTimeout(handleResize, 100);
          newTerminal.focus();
          setIsInitialized(true);
          message.success('终端初始化成功');
        }
      }, 100);

    } catch (error) {
      console.error('Failed to create terminal:', error);
      message.error(`创建终端失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 清除终端
  const clearTerminal = () => {
    if (terminal) {
      terminal.clear();
      message.success('终端已清除');
    }
  };

  // 重新创建终端
  const recreateTerminal = async () => {
    if (terminal) {
      terminal.dispose();
    }
    if (ptyId) {
      try {
        await invoke('close_pty', { ptyId });
      } catch (error) {
        console.error('Failed to close PTY:', error);
      }
    }
    setTerminal(null);
    setFitAddon(null);
    setPtyId(null);
    setIsInitialized(false);
    
    setTimeout(() => {
      createTerminal();
    }, 100);
  };

  // 初始化终端
  useEffect(() => {
    if (projectId && !isInitialized && !isLoading) {
      createTerminal();
    }
  }, [projectId]);

  // 清理
  useEffect(() => {
    return () => {
      if (terminal) {
        terminal.dispose();
      }
      if (ptyId) {
        invoke('close_pty', { ptyId }).catch(console.error);
      }
    };
  }, []);

  return (
    <div className={`simple-terminal ${collapsed ? 'collapsed' : ''}`} style={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      background: '#1e1e1e',
      border: '1px solid #2d2d30',
      overflow: 'hidden'
    }}>
      {/* 终端标题栏 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: '35px',
        background: '#2d2d30',
        borderBottom: '1px solid #3e3e42',
        padding: '0 12px',
        flexShrink: 0,
        userSelect: 'none'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          fontSize: '13px',
          fontWeight: 500,
          color: '#cccccc'
        }}>
          <span style={{ fontSize: '14px', color: '#007acc' }}>⚡</span>
          <span>终端</span>
          {isInitialized && <span style={{ fontSize: '11px', color: '#858585' }}>(已连接)</span>}
          {isLoading && <span style={{ fontSize: '11px', color: '#ffa500' }}>(连接中...)</span>}
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
          {/* 清除按钮 */}
          <Tooltip title="清除终端">
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              onClick={clearTerminal}
              disabled={!isInitialized}
              style={{
                width: '28px',
                height: '28px',
                border: 'none',
                background: 'transparent',
                color: '#cccccc',
                borderRadius: '4px'
              }}
            />
          </Tooltip>
          
          {/* 重新创建按钮 */}
          <Tooltip title="重新创建终端">
            <Button
              type="text"
              size="small"
              icon={<PlusOutlined />}
              onClick={recreateTerminal}
              disabled={isLoading}
              style={{
                width: '28px',
                height: '28px',
                border: 'none',
                background: 'transparent',
                color: '#cccccc',
                borderRadius: '4px'
              }}
            />
          </Tooltip>
          
          {/* 折叠按钮 */}
          {onToggleCollapse && (
            <Tooltip title={collapsed ? "展开终端" : "折叠终端"}>
              <Button
                type="text"
                size="small"
                icon={collapsed ? <UpOutlined /> : <DownOutlined />}
                onClick={onToggleCollapse}
                style={{
                  width: '28px',
                  height: '28px',
                  border: 'none',
                  background: 'transparent',
                  color: '#cccccc',
                  borderRadius: '4px'
                }}
              />
            </Tooltip>
          )}
        </div>
      </div>

      {/* 终端内容区域 */}
      <div style={{
        flex: 1,
        position: 'relative',
        overflow: 'hidden',
        background: '#1e1e1e',
        display: collapsed ? 'none' : 'block'
      }}>
        {!isInitialized && !isLoading ? (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            flexDirection: 'column',
            color: '#858585'
          }}>
            <span style={{ fontSize: '32px', marginBottom: '8px', color: '#007acc' }}>⚡</span>
            <div style={{ marginBottom: '16px' }}>点击创建终端</div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={createTerminal}
              disabled={!projectId}
              style={{
                background: '#007acc',
                borderColor: '#007acc'
              }}
            >
              创建终端
            </Button>
          </div>
        ) : isLoading ? (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            flexDirection: 'column',
            color: '#858585'
          }}>
            <span style={{ fontSize: '32px', marginBottom: '8px', color: '#ffa500' }}>⚡</span>
            <div>正在初始化终端...</div>
          </div>
        ) : (
          <div
            ref={terminalRef}
            style={{
              width: '100%',
              height: '100%',
              padding: '8px',
              background: '#1e1e1e'
            }}
          />
        )}
      </div>
    </div>
  );
};

export default SimpleTerminal;
