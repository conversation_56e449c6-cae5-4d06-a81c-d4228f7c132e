import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { SearchAddon } from 'xterm-addon-search';
import { WebLinksAddon } from 'xterm-addon-web-links';
import 'xterm/css/xterm.css';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { message, Tabs, Button, Tooltip } from 'antd';
import { PlusOutlined, CloseOutlined, CodeOutlined, SearchOutlined } from '@ant-design/icons';
import './XtermTerminal.css';

interface TerminalSession {
  id: number;
  ptyId: number;
  terminal: Terminal;
  fitAddon: FitAddon;
  searchAddon: SearchAddon;
  webLinksAddon: WebLinksAddon;
  title: string;
  projectId: string;
}

interface VSCodeTerminalProps {
  projectId?: string;
  projectPath?: string;
}

const VSCodeTerminal: React.FC<VSCodeTerminalProps> = ({ projectId, projectPath }) => {
  const [sessions, setSessions] = useState<TerminalSession[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<number | null>(null);
  const [searchVisible, setSearchVisible] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const terminalRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});
  const sessionCounter = useRef(1);
  const resizeTimeouts = useRef<{ [key: number]: NodeJS.Timeout }>({});

  const createTerminalSession = useCallback(async () => {
    if (!projectId) {
      message.error('无法创建终端：未选择项目。');
      return;
    }

    try {
      console.log('Creating new terminal session for project:', projectId);
      const ptyId = await invoke<number>('create_local_pty', { 
        projectId, 
        initialCommand: null 
      });
      
      const sessionId = sessionCounter.current++;
      const title = `Terminal ${sessionId}`;

      // Create xterm.js terminal with enhanced theme
      const terminal = new Terminal({
        cursorBlink: true,
        rows: 24,
        cols: 80,
        convertEol: true,
        fontFamily: '"Cascadia Code", "JetBrains Mono", "SF Mono", Menlo, Monaco, "Courier New", monospace',
        fontSize: 13,
        lineHeight: 1.2,
        fontWeight: 'normal',
        fontWeightBold: 'bold',
        allowTransparency: true,
        bellStyle: 'none',
        cursorStyle: 'block',
        cursorWidth: 1,
        drawBoldTextInBrightColors: true,
        fastScrollModifier: 'alt',
        fastScrollSensitivity: 5,
        macOptionIsMeta: true,
        rightClickSelectsWord: true,
        scrollback: 10000,
        scrollSensitivity: 1,
        smoothScrollDuration: 0,
        tabStopWidth: 4,
        theme: {
          background: '#1e1e1e',
          foreground: '#cccccc',
          cursor: '#cccccc',
          cursorAccent: '#1e1e1e',
          selection: '#264f78',
          selectionForeground: '#ffffff',

          // Standard colors (ANSI)
          black: '#000000',
          red: '#cd3131',
          green: '#0dbc79',
          yellow: '#e5e510',
          blue: '#2472c8',
          magenta: '#bc3fbc',
          cyan: '#11a8cd',
          white: '#e5e5e5',

          // Bright colors (ANSI bright)
          brightBlack: '#666666',
          brightRed: '#f14c4c',
          brightGreen: '#23d18b',
          brightYellow: '#f5f543',
          brightBlue: '#3b8eea',
          brightMagenta: '#d670d6',
          brightCyan: '#29b8db',
          brightWhite: '#e5e5e5',

          // Extended colors for better syntax highlighting
          extendedAnsi: [
            // 16-231: 216 color cube
            '#000000', '#800000', '#008000', '#808000', '#000080', '#800080', '#008080', '#c0c0c0',
            '#808080', '#ff0000', '#00ff00', '#ffff00', '#0000ff', '#ff00ff', '#00ffff', '#ffffff',
            // Additional colors for enhanced terminal output
            '#5f0000', '#870000', '#af0000', '#d70000', '#ff0000', '#ff5f00', '#ff8700', '#ffaf00',
            '#ffd700', '#ffff00', '#d7ff00', '#afff00', '#87ff00', '#5fff00', '#00ff00', '#00ff5f',
            '#00ff87', '#00ffaf', '#00ffd7', '#00ffff', '#00d7ff', '#00afff', '#0087ff', '#005fff',
            '#0000ff', '#5f00ff', '#8700ff', '#af00ff', '#d700ff', '#ff00ff', '#ff00d7', '#ff00af',
            '#ff0087', '#ff005f'
          ]
        }
      });

      const fitAddon = new FitAddon();
      const searchAddon = new SearchAddon();
      const webLinksAddon = new WebLinksAddon();

      terminal.loadAddon(fitAddon);
      terminal.loadAddon(searchAddon);
      terminal.loadAddon(webLinksAddon);

      const session: TerminalSession = {
        id: sessionId,
        ptyId,
        terminal,
        fitAddon,
        searchAddon,
        webLinksAddon,
        title,
        projectId
      };

      setSessions(prev => [...prev, session]);
      setActiveSessionId(sessionId);

      // Wait for the terminal container to be available
      setTimeout(() => {
        const container = terminalRefs.current[sessionId];
        if (container && terminal) {
          terminal.open(container);
          fitAddon.fit();

          // Handle input from xterm -> backend
          terminal.onData(async (data) => {
            try {
              await invoke('write_to_pty', { ptyId, data });
            } catch (error) {
              console.error('Failed to write to PTY:', error);
              message.error(`写入终端失败: ${error}`);
            }
          });

          // Handle resize
          const handleResize = () => {
            if (resizeTimeouts.current[sessionId]) {
              clearTimeout(resizeTimeouts.current[sessionId]);
            }
            resizeTimeouts.current[sessionId] = setTimeout(() => {
              if (fitAddon && terminal) {
                fitAddon.fit();
                const { cols, rows } = terminal;
                invoke('resize_pty', { ptyId, cols, rows })
                  .catch(err => console.error('Failed to resize PTY:', err));
              }
            }, 250);
          };

          const resizeObserver = new ResizeObserver(handleResize);
          resizeObserver.observe(container);

          // Initial resize
          setTimeout(handleResize, 100);

          // Listen for output from backend
          const outputEventName = `pty-output-${ptyId}`;
          listen<string>(outputEventName, (event) => {
            terminal.write(event.payload);
          });

          terminal.focus();
        }
      }, 100);

    } catch (error) {
      console.error('Failed to create terminal session:', error);
      message.error(`创建终端失败: ${error}`);
    }
  }, [projectId]);

  const closeTerminalSession = useCallback(async (sessionId: number) => {
    const session = sessions.find(s => s.id === sessionId);
    if (!session) return;

    try {
      // Close PTY session
      await invoke('close_pty', { ptyId: session.ptyId });
      
      // Dispose terminal
      session.terminal.dispose();
      
      // Clear resize timeout
      if (resizeTimeouts.current[sessionId]) {
        clearTimeout(resizeTimeouts.current[sessionId]);
        delete resizeTimeouts.current[sessionId];
      }

      // Remove from sessions
      setSessions(prev => prev.filter(s => s.id !== sessionId));
      
      // Update active session
      if (activeSessionId === sessionId) {
        const remainingSessions = sessions.filter(s => s.id !== sessionId);
        setActiveSessionId(remainingSessions.length > 0 ? remainingSessions[0].id : null);
      }

      delete terminalRefs.current[sessionId];
    } catch (error) {
      console.error('Failed to close terminal session:', error);
      message.error(`关闭终端失败: ${error}`);
    }
  }, [sessions, activeSessionId]);

  // Search functionality
  const handleSearch = useCallback((term: string) => {
    const activeSession = sessions.find(s => s.id === activeSessionId);
    if (activeSession && term) {
      activeSession.searchAddon.findNext(term);
    }
  }, [sessions, activeSessionId]);

  const handleSearchPrevious = useCallback(() => {
    const activeSession = sessions.find(s => s.id === activeSessionId);
    if (activeSession && searchTerm) {
      activeSession.searchAddon.findPrevious(searchTerm);
    }
  }, [sessions, activeSessionId, searchTerm]);

  const handleSearchNext = useCallback(() => {
    const activeSession = sessions.find(s => s.id === activeSessionId);
    if (activeSession && searchTerm) {
      activeSession.searchAddon.findNext(searchTerm);
    }
  }, [sessions, activeSessionId, searchTerm]);

  // Create initial terminal session when component mounts
  useEffect(() => {
    if (projectId && sessions.length === 0) {
      createTerminalSession();
    }
  }, [projectId, createTerminalSession, sessions.length]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      sessions.forEach(session => {
        session.terminal.dispose();
        if (resizeTimeouts.current[session.id]) {
          clearTimeout(resizeTimeouts.current[session.id]);
        }
        invoke('close_pty', { ptyId: session.ptyId }).catch(console.error);
      });
    };
  }, []);

  const tabItems = sessions.map(session => ({
    key: session.id.toString(),
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <CodeOutlined style={{ fontSize: '12px' }} />
        <span>{session.title}</span>
        {sessions.length > 1 && (
          <Button
            type="text"
            size="small"
            icon={<CloseOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              closeTerminalSession(session.id);
            }}
            style={{ 
              width: '16px', 
              height: '16px', 
              minWidth: '16px',
              padding: 0,
              marginLeft: '4px'
            }}
          />
        )}
      </div>
    ),
    children: (
      <div 
        ref={(el) => terminalRefs.current[session.id] = el}
        style={{ 
          height: '100%', 
          width: '100%', 
          backgroundColor: '#1e1e1e',
          padding: '4px'
        }} 
      />
    )
  }));

  const tabBarExtraContent = (
    <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
      <Tooltip title="搜索">
        <Button
          type="text"
          size="small"
          icon={<SearchOutlined />}
          onClick={() => setSearchVisible(!searchVisible)}
          style={{ marginRight: '4px' }}
        />
      </Tooltip>
      <Tooltip title="新建终端">
        <Button
          type="text"
          size="small"
          icon={<PlusOutlined />}
          onClick={createTerminalSession}
          style={{ marginRight: '8px' }}
        />
      </Tooltip>
    </div>
  );

  if (sessions.length === 0) {
    return (
      <div style={{ 
        height: '100%', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#1e1e1e',
        color: '#cccccc'
      }}>
        <div style={{ textAlign: 'center' }}>
          <CodeOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
          <div>正在初始化终端...</div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height: '100%', backgroundColor: '#1e1e1e', position: 'relative' }}>
      {/* 搜索栏 */}
      {searchVisible && (
        <div style={{
          position: 'absolute',
          top: '40px',
          right: '8px',
          zIndex: 1000,
          background: '#2d2d30',
          border: '1px solid #3e3e42',
          borderRadius: '4px',
          padding: '8px',
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
        }}>
          <input
            type="text"
            placeholder="搜索..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              handleSearch(e.target.value);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                if (e.shiftKey) {
                  handleSearchPrevious();
                } else {
                  handleSearchNext();
                }
              } else if (e.key === 'Escape') {
                setSearchVisible(false);
                setSearchTerm('');
              }
            }}
            style={{
              background: '#1e1e1e',
              border: '1px solid #3e3e42',
              borderRadius: '2px',
              color: '#cccccc',
              padding: '4px 8px',
              fontSize: '12px',
              width: '150px'
            }}
            autoFocus
          />
          <Button
            type="text"
            size="small"
            onClick={handleSearchPrevious}
            style={{ color: '#cccccc', minWidth: '24px', padding: '0 4px' }}
          >
            ↑
          </Button>
          <Button
            type="text"
            size="small"
            onClick={handleSearchNext}
            style={{ color: '#cccccc', minWidth: '24px', padding: '0 4px' }}
          >
            ↓
          </Button>
          <Button
            type="text"
            size="small"
            onClick={() => {
              setSearchVisible(false);
              setSearchTerm('');
            }}
            style={{ color: '#cccccc', minWidth: '24px', padding: '0 4px' }}
          >
            ×
          </Button>
        </div>
      )}

      <Tabs
        type="card"
        size="small"
        activeKey={activeSessionId?.toString()}
        onChange={(key) => setActiveSessionId(parseInt(key))}
        items={tabItems}
        tabBarExtraContent={tabBarExtraContent}
        style={{ height: '100%' }}
        tabBarStyle={{
          margin: 0,
          backgroundColor: '#2d2d30',
          borderBottom: '1px solid #3e3e42'
        }}
      />
    </div>
  );
};

export default VSCodeTerminal;
