.xterm-terminal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #1e1e1e;
  border-radius: 4px;
  overflow: hidden;
}

.xterm-terminal-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: #252526;
  border-bottom: 1px solid #333;
  height: 36px;
  flex-shrink: 0;
}

.xterm-terminal-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.xterm-terminal-tabs-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.xterm-terminal-tabs-header {
  display: flex;
  background-color: #252526;
  border-bottom: 1px solid #333;
  height: 36px;
  overflow-x: auto;
  flex-shrink: 0;
}

.xterm-terminal-tab {
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 36px;
  background-color: #2d2d2d;
  border-right: 1px solid #333;
  cursor: pointer;
  user-select: none;
  min-width: 120px;
  max-width: 200px;
}

.xterm-terminal-tab.active {
  background-color: #1e1e1e;
  border-bottom: 2px solid #0078d4;
}

.xterm-terminal-tab-content {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.xterm-terminal-tab-close {
  margin-left: 8px;
  font-size: 10px;
  color: #cccccc;
  opacity: 0.7;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 3px;
}

.xterm-terminal-tab:hover .xterm-terminal-tab-close {
  opacity: 1;
}

.xterm-terminal-tab-close:hover {
  color: #ff4d4f;
  background-color: rgba(255, 77, 79, 0.2);
  border-radius: 3px;
}

.xterm-terminal-tab-add {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: #252526;
  color: #cccccc;
  cursor: pointer;
  font-size: 12px;
  flex-shrink: 0;
}

.xterm-terminal-tab-add:hover {
  background-color: #3c3c3c;
  color: #ffffff;
}

.xterm-terminal-content-area {
  flex: 1;
  overflow: hidden;
  position: relative;
  background-color: #1e1e1e;
}

.xterm-instance-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 4px;
  box-sizing: border-box;
  display: none;
}

.xterm-instance-container.active {
  display: block;
}

/* 确保xterm.js的样式正确应用 */
.xterm {
  font-feature-settings: "liga" 0;
  position: relative;
  user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
}

.xterm.focus,
.xterm:focus {
  outline: none;
}

.xterm .xterm-helpers {
  position: absolute;
  top: 0;
  z-index: 5;
}

.xterm .xterm-helper-textarea {
  position: absolute;
  opacity: 0;
  left: -9999em;
  top: 0;
  width: 0;
  height: 0;
  z-index: -5;
  white-space: nowrap;
  overflow: hidden;
  resize: none;
}

.xterm .composition-view {
  background: #000;
  color: #FFF;
  display: none;
  position: absolute;
  white-space: nowrap;
  z-index: 1;
}

.xterm .composition-view.active {
  display: block;
}

.xterm .xterm-viewport {
  background-color: #000;
  overflow-y: scroll;
  cursor: default;
  position: absolute;
  right: 0;
  left: 0;
  top: 0;
  bottom: 0;
}

.xterm .xterm-screen {
  position: relative;
}

.xterm .xterm-screen canvas {
  position: absolute;
  left: 0;
  top: 0;
}

.xterm-cursor-layer {
  z-index: 3;
}

.xterm-cursor {
  background-color: #fff;
  border-left: 1px solid #fff;
  position: absolute;
}

.xterm-cursor.xterm-cursor-blink {
  animation: xterm-cursor-blink 1.2s infinite step-end;
}

@keyframes xterm-cursor-blink {
  0% {
    background-color: #fff;
    border-left: 1px solid #fff;
  }
  50% {
    background-color: transparent;
    border-left: 1px solid transparent;
  }
}

.xterm-cursor.xterm-cursor-block {
  width: 1ch;
  background-color: #fff;
  border-left: 0;
}

.xterm-cursor.xterm-cursor-underline {
  border-left: 0;
  border-bottom: 1px solid #fff;
}

.xterm-cursor.xterm-cursor-bar {
  border-left: 1px solid #fff;
}

.xterm-selection {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  opacity: 0.3;
  pointer-events: none;
}

.xterm-selection div {
  position: absolute;
  background-color: #fff;
}

.xterm-search-bar {
  background-color: #252526;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  border-top: 1px solid #333;
}

.xterm-search-input {
  flex: 1;
  background-color: #3c3c3c;
  border: none;
  color: #cccccc;
  padding: 4px 8px;
  border-radius: 2px;
  margin-right: 8px;
}

.xterm-search-button {
  background-color: #0078d4;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 2px;
  cursor: pointer;
}

.xterm-search-button:hover {
  background-color: #106ebe;
}

.xterm-search-button:disabled {
  background-color: #666;
  cursor: not-allowed;
}
