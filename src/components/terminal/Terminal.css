.terminal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consol<PERSON>', 'Courier New', monospace;
  border-radius: 4px;
  overflow: hidden;
}

.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: #252526;
  border-bottom: 1px solid #333;
}

.terminal-title {
  font-size: 12px;
  font-weight: bold;
  color: #cccccc;
}

.terminal-output {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
  font-size: 13px;
  line-height: 1.5;
}

.terminal-line {
  margin-bottom: 4px;
}

.terminal-input {
  padding: 8px;
  background-color: #252526;
  border-top: 1px solid #333;
}

.terminal-input .ant-input {
  background-color: #1e1e1e;
  color: #d4d4d4;
  border-color: #333;
}

.terminal-input .ant-input:focus,
.terminal-input .ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.terminal-input .ant-input::placeholder {
  color: #666;
}

/* 自定义滚动条 */
.terminal-output::-webkit-scrollbar {
  width: 8px;
}

.terminal-output::-webkit-scrollbar-track {
  background: #1e1e1e;
}

.terminal-output::-webkit-scrollbar-thumb {
  background-color: #555;
  border-radius: 4px;
}

.terminal-output::-webkit-scrollbar-thumb:hover {
  background-color: #777;
}
