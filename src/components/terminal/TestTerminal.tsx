import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import '@xterm/xterm/css/xterm.css';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { message, Button } from 'antd';

interface TestTerminalProps {
  projectId?: string;
  projectPath?: string;
}

const TestTerminal: React.FC<TestTerminalProps> = ({ projectId, projectPath }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const [terminal, setTerminal] = useState<Terminal | null>(null);
  const [ptyId, setPtyId] = useState<number | null>(null);
  const [status, setStatus] = useState<string>('未初始化');

  const createTerminal = async () => {
    if (!projectId) {
      setStatus('错误：未选择项目');
      return;
    }

    setStatus('正在创建终端...');
    console.log('=== 开始创建终端 ===');
    console.log('项目ID:', projectId);
    console.log('项目路径:', projectPath);

    try {
      // 步骤1：调用后端创建 PTY
      console.log('步骤1：调用 create_local_pty');
      const newPtyId = await invoke<number>('create_local_pty', { 
        projectId, 
        initialCommand: null 
      });
      console.log('PTY 创建成功，ID:', newPtyId);
      setPtyId(newPtyId);
      setStatus(`PTY 已创建，ID: ${newPtyId}`);

      // 步骤2：创建前端终端
      console.log('步骤2：创建前端终端实例');
      const newTerminal = new Terminal({
        cursorBlink: true,
        rows: 24,
        cols: 80,
        theme: {
          background: '#1e1e1e',
          foreground: '#cccccc',
        }
      });

      const fitAddon = new FitAddon();
      newTerminal.loadAddon(fitAddon);
      setTerminal(newTerminal);

      // 步骤3：挂载到 DOM
      console.log('步骤3：挂载终端到 DOM');
      if (terminalRef.current) {
        newTerminal.open(terminalRef.current);
        fitAddon.fit();
        setStatus('终端已挂载到 DOM');

        // 步骤4：设置输入处理
        console.log('步骤4：设置输入处理');
        newTerminal.onData(async (data) => {
          console.log('用户输入:', data);
          try {
            await invoke('write_to_pty', { ptyId: newPtyId, data });
            console.log('输入已发送到后端');
          } catch (error) {
            console.error('发送输入失败:', error);
            setStatus(`输入失败: ${error}`);
          }
        });

        // 步骤5：监听输出
        console.log('步骤5：监听输出事件');
        const outputEventName = `pty-output-${newPtyId}`;
        console.log('监听事件:', outputEventName);
        
        const unlisten = await listen<string>(outputEventName, (event) => {
          console.log('收到输出:', event.payload);
          newTerminal.write(event.payload);
        });

        // 步骤6：完成初始化
        console.log('步骤6：终端初始化完成');
        newTerminal.focus();
        setStatus('终端初始化完成');
        message.success('终端创建成功！');

        // 清理函数
        return () => {
          console.log('清理终端资源');
          unlisten();
          newTerminal.dispose();
          if (newPtyId) {
            invoke('close_pty', { ptyId: newPtyId }).catch(console.error);
          }
        };
      }

    } catch (error) {
      console.error('创建终端失败:', error);
      setStatus(`创建失败: ${error}`);
      message.error(`创建终端失败: ${error}`);
    }
  };

  const testBackend = async () => {
    if (!projectId) {
      message.error('请先选择项目');
      return;
    }

    setStatus('测试后端连接...');
    try {
      console.log('测试调用 create_local_pty...');
      const testPtyId = await invoke<number>('create_local_pty', { 
        projectId, 
        initialCommand: null 
      });
      console.log('后端测试成功，PTY ID:', testPtyId);
      setStatus(`后端测试成功，PTY ID: ${testPtyId}`);
      message.success('后端连接正常');
      
      // 立即关闭测试 PTY
      await invoke('close_pty', { ptyId: testPtyId });
      console.log('测试 PTY 已关闭');
    } catch (error) {
      console.error('后端测试失败:', error);
      setStatus(`后端测试失败: ${error}`);
      message.error(`后端测试失败: ${error}`);
    }
  };

  const clearTerminal = () => {
    if (terminal) {
      terminal.clear();
      setStatus('终端已清除');
    }
  };

  const resetTerminal = () => {
    if (terminal) {
      terminal.dispose();
      setTerminal(null);
    }
    if (ptyId) {
      invoke('close_pty', { ptyId }).catch(console.error);
      setPtyId(null);
    }
    setStatus('终端已重置');
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      background: '#1e1e1e',
      border: '1px solid #444',
      padding: '8px'
    }}>
      {/* 控制面板 */}
      <div style={{
        background: '#2d2d30',
        padding: '8px',
        marginBottom: '8px',
        borderRadius: '4px',
        color: '#cccccc',
        fontSize: '12px'
      }}>
        <div style={{ marginBottom: '8px' }}>
          <strong>状态:</strong> {status}
        </div>
        <div style={{ marginBottom: '8px' }}>
          <strong>项目ID:</strong> {projectId || '未选择'}
        </div>
        <div style={{ marginBottom: '8px' }}>
          <strong>PTY ID:</strong> {ptyId || '无'}
        </div>
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          <Button size="small" onClick={testBackend} disabled={!projectId}>
            测试后端
          </Button>
          <Button size="small" onClick={createTerminal} disabled={!projectId}>
            创建终端
          </Button>
          <Button size="small" onClick={clearTerminal} disabled={!terminal}>
            清除
          </Button>
          <Button size="small" onClick={resetTerminal}>
            重置
          </Button>
        </div>
      </div>

      {/* 终端区域 */}
      <div style={{
        flex: 1,
        background: '#1e1e1e',
        border: '1px solid #444',
        borderRadius: '4px',
        overflow: 'hidden'
      }}>
        {!terminal ? (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#888',
            flexDirection: 'column'
          }}>
            <div style={{ marginBottom: '16px' }}>终端未创建</div>
            <Button 
              type="primary" 
              onClick={createTerminal} 
              disabled={!projectId}
              style={{ background: '#007acc', borderColor: '#007acc' }}
            >
              创建终端
            </Button>
          </div>
        ) : (
          <div
            ref={terminalRef}
            style={{
              width: '100%',
              height: '100%',
              padding: '4px'
            }}
          />
        )}
      </div>
    </div>
  );
};

export default TestTerminal;
