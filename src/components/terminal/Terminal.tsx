import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button, Input, Space, Typography, Tooltip, message } from 'antd';
import type { InputRef } from 'antd';
import { ClearOutlined, SendOutlined, CloseOutlined, CodeOutlined, ReloadOutlined } from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';
import './Terminal.css';

const { Text } = Typography;

interface TerminalProps {
  projectId?: string;
}

const Terminal: React.FC<TerminalProps> = ({ projectId }) => {
  const [output, setOutput] = useState<string[]>([]);
  const [command, setCommand] = useState<string>('');
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [terminalId, setTerminalId] = useState<number | null>(null);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(-1);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);
  const outputEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<InputRef>(null);

  // 自动滚动到底部
  useEffect(() => {
    if (outputEndRef.current) {
      outputEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [output]);

  // 初始化终端
  useEffect(() => {
    initTerminal();
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
      if (terminalId !== null) {
        invoke('kill_process', { terminalId }).catch(console.error);
      }
    };
  }, []);

  // 初始化终端
  const initTerminal = async () => {
    try {
      // 创建一个新的终端进程
      const result = await invoke('launch_process', {
        command: navigator.platform.toLowerCase().includes('win') ? 'cmd' : 'bash',
        wait: false,
        maxWaitSeconds: 600
      }) as { terminalId: number };

      setTerminalId(result.terminalId);
      setOutput(prev => [...prev, `Terminal #${result.terminalId} started.`]);

      // 设置轮询间隔，定期读取终端输出
      const interval = setInterval(async () => {
        if (terminalId !== null) {
          try {
            const output = await invoke('read_process', {
              terminalId: result.terminalId,
              wait: false,
              maxWaitSeconds: 1
            }) as string;

            if (output && output.trim()) {
              setOutput(prev => [...prev, output]);
            }
          } catch (error) {
            console.error('Error reading terminal output:', error);
          }
        }
      }, 1000);

      setPollingInterval(interval);
    } catch (error) {
      message.error(`Error initializing terminal: ${error}`);
    }
  };

  // 清空终端
  const handleClear = () => {
    setOutput([]);
  };

  // 执行命令
  const handleExecute = async () => {
    if (!command.trim()) return;

    try {
      setIsRunning(true);
      setOutput(prev => [...prev, `$ ${command}`]);

      // 添加到命令历史
      setCommandHistory(prev => [...prev, command]);
      setHistoryIndex(-1);

      // 向终端发送命令
      if (terminalId !== null) {
        await invoke('write_process', {
          terminalId,
          inputText: command + '\n'
        });
      } else {
        // 如果终端不存在，重新初始化
        await initTerminal();
        message.warning('Terminal was restarted. Please try your command again.');
      }
    } catch (error) {
      setOutput(prev => [...prev, `Error: ${error}`]);
    } finally {
      setIsRunning(false);
      setCommand('');
    }
  };

  // 关闭终端
  const handleClose = async () => {
    if (terminalId !== null) {
      try {
        if (pollingInterval) {
          clearInterval(pollingInterval);
          setPollingInterval(null);
        }

        await invoke('kill_process', { terminalId });
        setTerminalId(null);
        setOutput(prev => [...prev, 'Terminal closed.']);
      } catch (error) {
        setOutput(prev => [...prev, `Error closing terminal: ${error}`]);
      }
    }
  };

  // 重启终端
  const handleRestart = async () => {
    if (terminalId !== null) {
      await handleClose();
    }
    await initTerminal();
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (commandHistory.length > 0) {
        const newIndex = historyIndex < commandHistory.length - 1 ? historyIndex + 1 : historyIndex;
        setHistoryIndex(newIndex);
        setCommand(commandHistory[commandHistory.length - 1 - newIndex]);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        setCommand(commandHistory[commandHistory.length - 1 - newIndex]);
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        setCommand('');
      }
    } else if (e.key === 'Tab') {
      e.preventDefault();
      // 在这里可以实现自动补全功能
    }
  };

  // 聚焦输入框
  const focusInput = useCallback(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  return (
    <div className="terminal-container" onClick={focusInput}>
      <div className="terminal-header">
        <div className="terminal-title">
          <CodeOutlined /> Terminal {terminalId !== null ? `#${terminalId}` : ''}
        </div>
        <Space>
          <Tooltip title="Clear terminal">
            <Button
              type="text"
              icon={<ClearOutlined />}
              onClick={handleClear}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Restart terminal">
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={handleRestart}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Close terminal">
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={handleClose}
              size="small"
              disabled={terminalId === null}
            />
          </Tooltip>
        </Space>
      </div>

      <div className="terminal-output">
        {output.map((line, index) => (
          <div key={index} className="terminal-line">
            <Text style={{ whiteSpace: 'pre-wrap', color: line.startsWith('$') ? '#1890ff' : '#d4d4d4' }}>
              {line}
            </Text>
          </div>
        ))}
        <div ref={outputEndRef} />
      </div>

      <div className="terminal-input">
        <Input
          ref={inputRef}
          value={command}
          onChange={e => setCommand(e.target.value)}
          onPressEnter={handleExecute}
          onKeyDown={handleKeyDown}
          placeholder="Enter command..."
          disabled={isRunning}
          prefix={<Text type="secondary">$</Text>}
          suffix={
            <Tooltip title="Execute">
              <Button
                type="text"
                icon={<SendOutlined />}
                onClick={handleExecute}
                disabled={isRunning || !command.trim()}
                size="small"
              />
            </Tooltip>
          }
          autoFocus
        />
      </div>
    </div>
  );
};

export default Terminal;
