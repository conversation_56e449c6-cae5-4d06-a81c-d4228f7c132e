import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { SearchAddon } from '@xterm/addon-search';
import { WebLinksAddon } from '@xterm/addon-web-links';
import '@xterm/xterm/css/xterm.css';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { message } from 'antd';

interface LocalTerminalProps {
  projectId: string | null; // Project ID to associate the terminal session
  initialCommand?: string; // Optional initial command to run
}

const LocalTerminal: React.FC<LocalTerminalProps> = ({ projectId, initialCommand }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const term = useRef<Terminal | null>(null);
  const fitAddon = useRef<FitAddon | null>(null);
  const searchAddon = useRef<SearchAddon | null>(null);
  const webLinksAddon = useRef<WebLinksAddon | null>(null);
  const [ptyId, setPtyId] = useState<number | null>(null);
  const resizeTimeout = useRef<NodeJS.Timeout | null>(null);

  const createPtySession = useCallback(async () => {
    if (!projectId) {
      message.error('无法创建本地终端：未选择项目。');
      return;
    }
    if (ptyId !== null) {
      console.log('PTY session already exists:', ptyId);
      return; // Avoid creating multiple sessions
    }

    try {
      console.log('Attempting to create PTY session for project:', projectId);
      const newPtyId = await invoke<number>('create_local_pty', { projectId, initialCommand });
      console.log('PTY session created with ID:', newPtyId);
      setPtyId(newPtyId);
      return newPtyId;
    } catch (error) {
      console.error('Failed to create PTY session:', error);
      message.error(`创建本地终端失败: ${error}`);
      return null;
    }
  }, [projectId, initialCommand, ptyId]);

  const initializeTerminal = useCallback((currentPtyId: number) => {
    if (terminalRef.current && !term.current) {
      console.log('Initializing xterm.js terminal for PTY ID:', currentPtyId);
      const xterm = new Terminal({
        cursorBlink: true,
        rows: 20, // Default rows
        cols: 80, // Default cols
        convertEol: true,
        fontFamily: '"Cascadia Code", "JetBrains Mono", "SF Mono", Menlo, Monaco, "Courier New", monospace',
        fontSize: 14,
        lineHeight: 1.2,
        fontWeight: 'normal',
        fontWeightBold: 'bold',
        allowTransparency: true,
        bellStyle: 'none',
        cursorStyle: 'block',
        cursorWidth: 1,
        drawBoldTextInBrightColors: true,
        scrollback: 10000,
        tabStopWidth: 4,
        theme: {
          background: '#1e1e1e',
          foreground: '#d4d4d4',
          cursor: '#d4d4d4',
          cursorAccent: '#1e1e1e',
          selection: '#264f78',
          selectionForeground: '#ffffff',

          // Standard colors (ANSI)
          black: '#000000',
          red: '#cd3131',
          green: '#0dbc79',
          yellow: '#e5e510',
          blue: '#2472c8',
          magenta: '#bc3fbc',
          cyan: '#11a8cd',
          white: '#e5e5e5',

          // Bright colors (ANSI bright)
          brightBlack: '#666666',
          brightRed: '#f14c4c',
          brightGreen: '#23d18b',
          brightYellow: '#f5f543',
          brightBlue: '#3b8eea',
          brightMagenta: '#d670d6',
          brightCyan: '#29b8db',
          brightWhite: '#e5e5e5',
        }
      });

      const newFitAddon = new FitAddon();
      const newSearchAddon = new SearchAddon();
      const newWebLinksAddon = new WebLinksAddon();

      xterm.loadAddon(newFitAddon);
      xterm.loadAddon(newSearchAddon);
      xterm.loadAddon(newWebLinksAddon);

      term.current = xterm;
      fitAddon.current = newFitAddon;
      searchAddon.current = newSearchAddon;
      webLinksAddon.current = newWebLinksAddon;

      xterm.open(terminalRef.current);
      newFitAddon.fit();

      // Handle input from xterm -> backend
      xterm.onData(async (data) => {
        try {
          // console.log('Sending data to PTY:', currentPtyId, data);
          await invoke('write_to_pty', { ptyId: currentPtyId, data });
        } catch (error) {
          console.error('Failed to write to PTY:', error);
          message.error(`写入终端失败: ${error}`);
          // Consider closing terminal or session on write failure
        }
      });

      // Handle resize
      const handleResize = () => {
        if (resizeTimeout.current) {
          clearTimeout(resizeTimeout.current);
        }
        resizeTimeout.current = setTimeout(() => {
          if (fitAddon.current && term.current) {
            fitAddon.current.fit();
            const { cols, rows } = term.current;
            console.log(`Resizing PTY ${currentPtyId} to:`, { cols, rows });
            invoke('resize_pty', { ptyId: currentPtyId, cols, rows })
              .catch(err => console.error('Failed to resize PTY:', err));
          }
        }, 250); // Debounce resize events
      };

      const resizeObserver = new ResizeObserver(handleResize);
      if (terminalRef.current) {
        resizeObserver.observe(terminalRef.current);
      }

      // Initial resize after a short delay
      setTimeout(handleResize, 100);

      console.log('xterm.js initialized and attached.');

      // Return cleanup function for resize observer
      return () => {
        if (terminalRef.current) {
          resizeObserver.unobserve(terminalRef.current);
        }
        if (resizeTimeout.current) {
          clearTimeout(resizeTimeout.current);
        }
      };
    }
    return () => {}; // No cleanup needed if terminal wasn't initialized
  }, []); // Dependencies managed inside

  // Effect to create PTY and initialize terminal
  useEffect(() => {
    let unlistenOutput: (() => void) | null = null;
    let cleanupResize: (() => void) | null = null;

    const setup = async () => {
      const currentPtyId = await createPtySession();
      if (currentPtyId !== null && term.current === null) {
        cleanupResize = initializeTerminal(currentPtyId);

        // Listen for output from backend -> xterm
        const outputEventName = `pty-output-${currentPtyId}`;
        console.log('Listening for event:', outputEventName);
        const unlisten = await listen<string>(outputEventName, (event) => {
          // console.log('Received PTY output:', event.payload);
          term.current?.write(event.payload);
        });
        unlistenOutput = unlisten;
        console.log('Listener attached for PTY output.');


      }
    };

    setup();

    // Cleanup function
    return () => {
      console.log('Cleaning up LocalTerminal component...');
      if (unlistenOutput) {
        console.log('Removing PTY output listener.');
        unlistenOutput();
      }
      if (cleanupResize) {
        console.log('Cleaning up resize observer.');
        cleanupResize();
      }
      if (term.current) {
        console.log('Disposing xterm.js terminal.');
        term.current.dispose();
        term.current = null;
        fitAddon.current = null;
      }
      if (ptyId !== null) {
        console.log('Closing PTY session:', ptyId);
        invoke('close_pty', { ptyId })
          .then(() => console.log('PTY session closed:', ptyId))
          .catch(err => console.error('Failed to close PTY session:', ptyId, err));
        setPtyId(null); // Reset ptyId state
      }
    };
  // Re-run setup if projectId changes, but be careful about cleanup
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId]); // Only re-run when projectId changes

  // Effect to handle initial command or focus
  useEffect(() => {
    if (term.current && ptyId !== null) {
      term.current.focus();
      // If an initial command was provided, it should be handled by the backend PTY creation
    }
  }, [ptyId]); // Run when ptyId is set

  return (
    <div ref={terminalRef} style={{ height: '100%', width: '100%', backgroundColor: '#1e1e1e' }} />
  );
};

export default LocalTerminal;