import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import '../../styles/tree-view.css';
import '../../styles/vs-code-tree.css';
import {
  Tree,
  Button,
  Space,
  Modal,
  Form,
  Input,
  message,
  Layout,
  Menu,
  Dropdown,
  Typography,
  Row,
  Col,
  Card,
  Divider,
  Spin,
  Empty,
  Breadcrumb,
  Tooltip,
  Checkbox,
} from 'antd';
import {
  FolderOutlined,
  FileOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DownOutlined,
  UpOutlined,
  RightOutlined,
  FileAddOutlined,
  FolderAddOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  CodeOutlined,
  SearchOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import type { DataNode, TreeProps } from 'antd/es/tree';
import { Project, ProjectFile } from '../../types';

const { Title } = Typography;
const { DirectoryTree } = Tree;

interface FileExplorerProps {
  project: Project | null;
  onSelectFile: (file: ProjectFile) => void;
  files?: ProjectFile[];
}

const FileExplorer: React.FC<FileExplorerProps> = ({ project, onSelectFile, files: propFiles }) => {
  const [files, setFiles] = useState<ProjectFile[]>([]);
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'file' | 'folder'>('file');
  const [currentNode, setCurrentNode] = useState<{ id: string; title: string } | null>(null);
  const [form] = Form.useForm();
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [isCreateTaskModalVisible, setIsCreateTaskModalVisible] = useState(false);
  const [taskForm] = Form.useForm();
  const [searchValue, setSearchValue] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [allExpanded, setAllExpanded] = useState(false);

  const fetchFiles = async () => {
    if (!project) return;

    try {
      setLoading(true);
      const projectFiles = await invoke('get_project_files', { projectId: project.id }) as ProjectFile[];
      setFiles(projectFiles);

      // 构建树形结构
      const tree = buildFileTree(projectFiles);
      setTreeData(tree);

      // 设置默认展开所有根节点
      const rootKeys = tree.map(node => node.key);
      setExpandedKeys(rootKeys);
    } catch (error) {
      message.error('获取文件列表失败：' + error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (project) {
      // 如果有传入的文件列表，则使用传入的文件列表
      if (propFiles && propFiles.length > 0) {
        setFiles(propFiles);
        const tree = buildFileTree(propFiles);
        setTreeData(tree);

        // 设置默认展开所有根节点
        const rootKeys = tree.map(node => node.key);
        setExpandedKeys(rootKeys);
      } else {
        fetchFiles();
      }
    } else {
      setFiles([]);
      setTreeData([]);
      setExpandedKeys([]);
    }
  }, [project, propFiles]);

  const buildFileTree = (files: ProjectFile[]): DataNode[] => {
    // 创建节点映射
    const nodeMap = new Map<string, DataNode>();

    // 首先添加所有节点到映射
    files.forEach(file => {
      // 获取文件图标
      let icon;
      let className = '';

      if (file.is_directory) {
        icon = <FolderOutlined style={{ color: '#dcb67a' }} />;
        className = 'tree-directory';
      } else {
        // 根据文件扩展名设置不同的图标和类名
        const extension = file.name.split('.').pop()?.toLowerCase() || '';
        className = 'tree-file';

        switch (extension) {
          case 'js':
          case 'jsx':
            icon = <FileOutlined style={{ color: '#f1e05a' }} />;
            className += ' tree-file-js';
            break;
          case 'ts':
          case 'tsx':
            icon = <FileOutlined style={{ color: '#3178c6' }} />;
            className += ' tree-file-ts';
            break;
          case 'html':
          case 'htm':
            icon = <FileOutlined style={{ color: '#e34c26' }} />;
            className += ' tree-file-html';
            break;
          case 'css':
          case 'scss':
          case 'less':
            icon = <FileOutlined style={{ color: '#563d7c' }} />;
            className += ' tree-file-css';
            break;
          case 'json':
            icon = <FileOutlined style={{ color: '#f1e05a' }} />;
            className += ' tree-file-json';
            break;
          case 'md':
            icon = <FileOutlined style={{ color: '#083fa1' }} />;
            className += ' tree-file-md';
            break;
          case 'py':
            icon = <FileOutlined style={{ color: '#3572A5' }} />;
            className += ' tree-file-py';
            break;
          case 'rs':
            icon = <FileOutlined style={{ color: '#dea584' }} />;
            className += ' tree-file-rs';
            break;
          case 'go':
            icon = <FileOutlined style={{ color: '#00ADD8' }} />;
            className += ' tree-file-go';
            break;
          case 'java':
            icon = <FileOutlined style={{ color: '#b07219' }} />;
            className += ' tree-file-java';
            break;
          case 'c':
          case 'cpp':
          case 'h':
          case 'hpp':
            icon = <FileOutlined style={{ color: '#f34b7d' }} />;
            className += ' tree-file-cpp';
            break;
          case 'sh':
            icon = <FileOutlined style={{ color: '#89e051' }} />;
            className += ' tree-file-sh';
            break;
          default:
            icon = <FileOutlined style={{ color: '#6a9955' }} />;
            className += ' tree-file-default';
        }
      }

      // 创建自定义标题组件，显示树形结构
      const customTitle = (
        <div className={`tree-node ${className}`}>
          <span className="tree-node-icon">{icon}</span>
          <span className="tree-node-name">{file.name}</span>
        </div>
      );

      nodeMap.set(file.id, {
        key: file.id,
        title: customTitle,
        isLeaf: !file.is_directory,
        // 移除系统图标，只使用自定义图标
        children: file.is_directory ? [] : undefined,
        className: className, // 添加类名用于样式
      });
    });

    // 构建树结构
    const rootNodes: DataNode[] = [];

    // 按路径长度排序，确保父节点先处理
    const sortedFiles = [...files].sort((a, b) => {
      const aDepth = a.path.split('/').length;
      const bDepth = b.path.split('/').length;
      return aDepth - bDepth;
    });

    sortedFiles.forEach(file => {
      const node = nodeMap.get(file.id);
      if (!node) return;

      const pathParts = file.path.split('/');

      if (pathParts.length === 1 || (pathParts.length === 2 && pathParts[0] === file.name)) {
        // 这是根节点
        rootNodes.push(node);
      } else {
        // 找到父节点
        const parentPath = pathParts.slice(0, -1).join('/');
        const parent = files.find(f => f.path === parentPath);

        if (parent && nodeMap.has(parent.id)) {
          const parentNode = nodeMap.get(parent.id);
          if (parentNode && parentNode.children) {
            parentNode.children.push(node);
          }
        }
      }
    });

    // 对每个节点的子节点进行排序：先文件夹，后文件，按名称字母顺序排序
    const sortNodes = (nodes: DataNode[]) => {
      nodes.sort((a, b) => {
        const aIsLeaf = a.isLeaf === true;
        const bIsLeaf = b.isLeaf === true;

        if (aIsLeaf === bIsLeaf) {
          return String(a.title).localeCompare(String(b.title));
        }

        return aIsLeaf ? 1 : -1;
      });

      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          sortNodes(node.children);
        }
      });
    };

    sortNodes(rootNodes);

    return rootNodes;
  };

  const handleCreateFile = (parentId: string, isDirectory: boolean) => {
    setModalType(isDirectory ? 'folder' : 'file');
    setCurrentNode({ id: parentId, title: '' });
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleRename = (nodeId: string, nodeName: string) => {
    setCurrentNode({ id: nodeId, title: nodeName });
    form.setFieldsValue({ name: nodeName });
    setIsRenameModalVisible(true);
  };

  const handleDelete = async (nodeId: string) => {
    try {
      await invoke('delete_file_or_dir', { fileId: nodeId });
      message.success('删除成功');
      fetchFiles();
    } catch (error) {
      message.error('删除失败：' + error);
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      if (currentNode) {
        await invoke('create_file_or_dir', {
          projectId: project!.id,
          parentId: currentNode.id,
          name: values.name,
          isDirectory: modalType === 'folder',
        });
        message.success(`创建${modalType === 'folder' ? '文件夹' : '文件'}成功`);
        setIsModalVisible(false);
        fetchFiles();
      }
    } catch (error) {
      message.error('创建失败：' + error);
    }
  };

  const handleRenameOk = async () => {
    try {
      const values = await form.validateFields();

      if (currentNode) {
        await invoke('rename_file', {
          fileId: currentNode.id,
          newName: values.name,
        });
        message.success('重命名成功');
        setIsRenameModalVisible(false);
        fetchFiles();
      }
    } catch (error) {
      message.error('重命名失败：' + error);
    }
  };

  const handleSelect: TreeProps['onSelect'] = async (selectedKeys, info) => {
    if (selectedKeys.length > 0) {
      setSelectedKeys(selectedKeys);
      const fileId = selectedKeys[0].toString();
      const file = files.find(f => f.id === fileId);

      if (file && !file.is_directory) {
        onSelectFile(file);
      }
    }
  };

  const handleCheck = (checked: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
    if (Array.isArray(checked)) {
      setCheckedKeys(checked);
    } else {
      setCheckedKeys(checked.checked);
    }
  };

  const handleCreateTask = () => {
    if (checkedKeys.length === 0) {
      message.warning('请先选择文件');
      return;
    }

    // 过滤出Python文件
    const selectedFiles = files.filter(file =>
      checkedKeys.includes(file.id) &&
      !file.is_directory &&
      file.name.toLowerCase().endsWith('.py')
    );

    if (selectedFiles.length === 0) {
      message.warning('请选择至少一个Python文件');
      return;
    }

    taskForm.setFieldsValue({
      name: `Task_${new Date().toISOString().slice(0, 10)}`,
      description: `基于${selectedFiles.map(f => f.name).join(', ')}的任务`,
      files: selectedFiles.map(f => f.path).join('\n')
    });

    setIsCreateTaskModalVisible(true);
  };

  // 搜索框内容变化处理
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    if (!value) {
      // 如果搜索框为空，重置展开状态
      setExpandedKeys([]);
      setAutoExpandParent(false);
      return;
    }

    // 查找匹配的节点和它们的父节点
    const expandedKeysSet = new Set<React.Key>();

    // 递归查找匹配的节点
    const searchNodes = (nodes: DataNode[], parentKeys: React.Key[] = []) => {
      nodes.forEach(node => {
        const nodeTitle = typeof node.title === 'string'
          ? node.title
          : node.className?.includes('tree-file') || node.className?.includes('tree-directory')
            ? node.key.toString()
            : '';

        // 如果节点标题包含搜索关键字，添加该节点和所有父节点到展开列表
        if (nodeTitle.toLowerCase().includes(value.toLowerCase())) {
          parentKeys.forEach(key => expandedKeysSet.add(key));
          expandedKeysSet.add(node.key);
        }

        // 如果有子节点，递归搜索
        if (node.children && node.children.length > 0) {
          searchNodes(node.children, [...parentKeys, node.key]);
        }
      });
    };

    searchNodes(treeData);

    // 更新展开的节点
    setExpandedKeys(Array.from(expandedKeysSet));
    setAutoExpandParent(true);
  };

  // 处理展开/折叠
  const handleExpand = (keys: React.Key[]) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
    setAllExpanded(false);
  };

  // 一键展开所有文件
  const expandAll = () => {
    // 递归获取所有节点的key
    const getAllKeys = (nodes: DataNode[]): React.Key[] => {
      let keys: React.Key[] = [];
      nodes.forEach(node => {
        keys.push(node.key);
        if (node.children) {
          keys = [...keys, ...getAllKeys(node.children)];
        }
      });
      return keys;
    };

    const allKeys = getAllKeys(treeData);
    setExpandedKeys(allKeys);
    setAutoExpandParent(true);
    setAllExpanded(true);
  };

  // 一键折叠所有文件
  const collapseAll = () => {
    setExpandedKeys([]);
    setAutoExpandParent(false);
    setAllExpanded(false);
  };

  // 过滤树数据，高亮搜索关键字
  const getFilteredTreeData = (data: DataNode[]): DataNode[] => {
    if (!searchValue) return data;

    // 递归处理节点
    const processNode = (node: DataNode): DataNode | null => {
      // 获取节点标题
      const nodeTitle = typeof node.title === 'string'
        ? node.title
        : node.className?.includes('tree-file') || node.className?.includes('tree-directory')
          ? node.key.toString()
          : '';

      // 检查节点是否匹配搜索关键字
      const index = nodeTitle.toLowerCase().indexOf(searchValue.toLowerCase());

      // 处理子节点
      const children = node.children
        ? node.children
            .map(child => processNode(child))
            .filter(child => child !== null) as DataNode[]
        : undefined;

      // 如果节点匹配或者有匹配的子节点，返回节点
      if (index > -1 || (children && children.length > 0)) {
        // 创建节点的副本
        const newNode = { ...node };

        // 如果有子节点，更新子节点
        if (children) {
          newNode.children = children;
        }

        return newNode;
      }

      // 如果节点不匹配且没有匹配的子节点，返回null
      return null;
    };

    // 处理所有根节点
    return data
      .map(node => processNode(node))
      .filter(node => node !== null) as DataNode[];
  };

  const handleRightClick = ({ event, node }: any) => {
    event.preventDefault();
    const nodeId = node.key as string;
    const file = files.find(f => f.id === nodeId);

    if (!file) return;

    const menu = (
      <Menu>
        {file.is_directory && (
          <>
            <Menu.Item
              key="create-file"
              icon={<FileAddOutlined />}
              onClick={() => handleCreateFile(nodeId, false)}
            >
              新建文件
            </Menu.Item>
            <Menu.Item
              key="create-folder"
              icon={<FolderAddOutlined />}
              onClick={() => handleCreateFile(nodeId, true)}
            >
              新建文件夹
            </Menu.Item>
            <Menu.Divider />
          </>
        )}
        <Menu.Item
          key="rename"
          icon={<EditOutlined />}
          onClick={() => handleRename(nodeId, file.name)}
        >
          重命名
        </Menu.Item>
        <Menu.Item
          key="delete"
          icon={<DeleteOutlined />}
          danger
          onClick={() => handleDelete(nodeId)}
        >
          删除
        </Menu.Item>
      </Menu>
    );

    // 显示右键菜单
    const overlay = document.createElement('div');
    overlay.style.position = 'absolute';
    overlay.style.left = `${event.clientX}px`;
    overlay.style.top = `${event.clientY}px`;
    overlay.style.zIndex = '1000';
    document.body.appendChild(overlay);

    const root = document.createDocumentFragment().appendChild(overlay);
    const onClose = () => {
      document.body.removeChild(overlay);
    };

    // 使用ReactDOM渲染菜单
    // 注意：这里使用了一个简化的方法，实际上应该使用ReactDOM.render
    // 但由于我们在这个环境中无法直接使用ReactDOM，所以这里只是示意
    // 在实际项目中，应该使用ReactDOM.render或React 18的createRoot
    setTimeout(() => {
      onClose();
    }, 100);
  };

  return (
    <div className="file-explorer-container">
      {/* VS Code 风格的文件浏览器 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
        <div style={{ fontWeight: 'bold', fontSize: '14px' }}>文件浏览器</div>
        <div>
          <Space size="small">
            <Tooltip title={allExpanded ? "折叠所有" : "展开所有"}>
              <Button
                type="text"
                icon={allExpanded ? <UpOutlined /> : <DownOutlined />}
                onClick={allExpanded ? collapseAll : expandAll}
                size="small"
              />
            </Tooltip>
            <Tooltip title="刷新">
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={fetchFiles}
                size="small"
              />
            </Tooltip>
            {project && (
              <>
                <Tooltip title="新建文件">
                  <Button
                    type="text"
                    icon={<FileAddOutlined />}
                    size="small"
                    onClick={() => {
                      // 找到项目根目录
                      const rootDir = files.find(f => f.is_directory && !f.path.includes('/'));
                      if (rootDir) {
                        handleCreateFile(rootDir.id, false);
                      }
                    }}
                  />
                </Tooltip>
                <Tooltip title="新建文件夹">
                  <Button
                    type="text"
                    icon={<FolderAddOutlined />}
                    size="small"
                    onClick={() => {
                      // 找到项目根目录
                      const rootDir = files.find(f => f.is_directory && !f.path.includes('/'));
                      if (rootDir) {
                        handleCreateFile(rootDir.id, true);
                      }
                    }}
                  />
                </Tooltip>
                <Tooltip title="创建任务">
                  <Button
                    type="text"
                    icon={<CodeOutlined />}
                    size="small"
                    onClick={handleCreateTask}
                    disabled={checkedKeys.length === 0}
                  />
                </Tooltip>
              </>
            )}
          </Space>
        </div>
      </div>

      {/* 搜索框 */}
      <div className="file-explorer-search" style={{ marginBottom: '8px', position: 'relative' }}>
        <Input
          placeholder="搜索文件和文件夹..."
          value={searchValue}
          onChange={handleSearchChange}
          prefix={<SearchOutlined style={{ color: '#999' }} />}
          suffix={
            searchValue ? (
              <CloseCircleOutlined
                style={{ color: '#999', cursor: 'pointer' }}
                onClick={() => {
                  setSearchValue('');
                  setExpandedKeys([]);
                  setAutoExpandParent(false);
                }}
              />
            ) : null
          }
          style={{ borderRadius: '4px' }}
        />
      </div>

      {/* 选中文件提示 */}
      {checkedKeys.length > 0 && (
        <div style={{ marginBottom: '8px', fontSize: '12px', color: '#1890ff' }}>
          已选择 {checkedKeys.length} 个文件
          <Button
            type="link"
            size="small"
            onClick={() => setCheckedKeys([])}
            style={{ padding: '0 4px' }}
          >
            清除
          </Button>
        </div>
      )}

      {loading ? (
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Spin tip="加载中..." />
        </div>
      ) : project ? (
        treeData.length > 0 ? (
          <div className="tree-view-container">
            <DirectoryTree
              showIcon={false} // 不显示系统图标，只使用自定义图标
              expandedKeys={expandedKeys.length > 0 ? expandedKeys : (searchValue ? [] : ['defaultExpandAll'])}
              autoExpandParent={autoExpandParent}
              onExpand={handleExpand}
              onSelect={handleSelect}
              onRightClick={handleRightClick}
              treeData={searchValue ? getFilteredTreeData(treeData) : treeData}
              selectedKeys={selectedKeys}
              checkable
              checkedKeys={checkedKeys}
              onCheck={handleCheck}
              className="custom-tree vs-code-tree"
              style={{ background: 'transparent' }}
              showLine={{ showLeafIcon: false }}
              switcherIcon={({ expanded }) =>
                expanded ?
                <DownOutlined style={{ fontSize: '8px', color: '#666' }} /> :
                <RightOutlined style={{ fontSize: '8px', color: '#666' }} />
              }
              blockNode
            />
            {searchValue && getFilteredTreeData(treeData).length === 0 && (
              <div style={{ padding: '20px 0', textAlign: 'center', color: '#999' }}>
                没有找到匹配的文件或文件夹
              </div>
            )}
          </div>
        ) : (
          <Empty description="暂无文件" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )
      ) : (
        <Empty description="请先选择一个项目" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}

      <Modal
        title={`新建${modalType === 'folder' ? '文件夹' : '文件'}`}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => setIsModalVisible(false)}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={`${modalType === 'folder' ? '文件夹' : '文件'}名称`}
            rules={[{ required: true, message: `请输入${modalType === 'folder' ? '文件夹' : '文件'}名称` }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="重命名"
        open={isRenameModalVisible}
        onOk={handleRenameOk}
        onCancel={() => setIsRenameModalVisible(false)}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建任务模态框 */}
      <Modal
        title="创建任务"
        open={isCreateTaskModalVisible}
        onOk={async () => {
          try {
            const values = await taskForm.validateFields();
            // 这里调用创建任务的API
            // 示例: await invoke('create_task', { ...values, projectId: project?.id });
            message.success('任务创建成功');
            setIsCreateTaskModalVisible(false);
            setCheckedKeys([]);
          } catch (error) {
            message.error('创建任务失败：' + error);
          }
        }}
        onCancel={() => setIsCreateTaskModalVisible(false)}
      >
        <Form
          form={taskForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="任务描述"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="files"
            label="选中文件"
          >
            <Input.TextArea rows={4} readOnly />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FileExplorer;
