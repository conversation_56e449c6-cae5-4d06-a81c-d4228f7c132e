import React, { useState, useEffect, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
  message,
  Spin,
  Empty,
} from 'antd';
import { ProjectFile } from '../../types';
import * as monaco from 'monaco-editor';
import './FileEditor.css';



interface FileEditorProps {
  file: ProjectFile | null;
}

const FileEditor: React.FC<FileEditorProps> = ({ file }) => {
  const [content, setContent] = useState<string>('');
  const [originalContent, setOriginalContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [language, setLanguage] = useState<string>('plaintext');
  const fontSize = 14; // 固定字体大小
  const editorRef = useRef<HTMLDivElement>(null);
  const monacoEditorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);

  const fetchFileContent = async () => {
    if (!file || file.is_directory) return;

    try {
      setLoading(true);
      const fileContent = await invoke('read_file', { fileId: file.id }) as string;
      setContent(fileContent);
      setOriginalContent(fileContent);

      // 根据文件扩展名设置语言
      const extension = file.name.split('.').pop()?.toLowerCase() || '';
      setLanguage(getLanguageByExtension(extension));
    } catch (error) {
      message.error('获取文件内容失败：' + error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (file) {
      fetchFileContent();
    } else {
      setContent('');
      setOriginalContent('');
    }
  }, [file]);

  // 初始化 Monaco Editor
  useEffect(() => {
    if (!editorRef.current) return;

    // 延迟初始化以确保容器已完全渲染
    const initTimer = setTimeout(() => {
      if (!editorRef.current) return;

      // 定义 Dracula 主题
    monaco.editor.defineTheme('dracula', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6272A4', fontStyle: 'italic' },
        { token: 'keyword', foreground: 'FF79C6' },
        { token: 'string', foreground: 'F1FA8C' },
        { token: 'number', foreground: 'BD93F9' },
        { token: 'function', foreground: '50FA7B' },
        { token: 'variable', foreground: 'F8F8F2' },
        { token: 'operator', foreground: 'FF79C6' },
        { token: 'delimiter', foreground: 'F8F8F2' },
        { token: 'type', foreground: '8BE9FD' },
        { token: 'class', foreground: '8BE9FD' },
        { token: 'constant', foreground: 'BD93F9' },
        { token: 'regexp', foreground: 'F1FA8C' },
      ],
      colors: {
        'editor.background': '#282A36',
        'editor.foreground': '#F8F8F2',
        'editor.lineHighlightBackground': '#44475A',
        'editor.selectionBackground': '#44475A',
        'editor.inactiveSelectionBackground': '#44475A75',
        'editorCursor.foreground': '#F8F8F2',
        'editorLineNumber.foreground': '#6272A4',
        'editorLineNumber.activeForeground': '#F8F8F2',
        'editor.selectionHighlightBackground': '#44475A50',
        'editor.wordHighlightBackground': '#44475A75',
        'editor.wordHighlightStrongBackground': '#44475A',
        'editorBracketMatch.background': '#44475A',
        'editorBracketMatch.border': '#50FA7B',
        'editorGutter.background': '#282A36',
        'editorGutter.modifiedBackground': '#8BE9FD',
        'editorGutter.addedBackground': '#50FA7B',
        'editorGutter.deletedBackground': '#FF5555',
        'editorError.foreground': '#FF5555',
        'editorWarning.foreground': '#FFB86C',
        'editorInfo.foreground': '#8BE9FD',
        'editorHint.foreground': '#6272A4',
        'editorIndentGuide.background': '#44475A',
        'editorIndentGuide.activeBackground': '#6272A4',
        'editorWhitespace.foreground': '#44475A',
        'editorRuler.foreground': '#44475A',
        'scrollbar.shadow': '#00000000',
        'scrollbarSlider.background': '#44475A80',
        'scrollbarSlider.hoverBackground': '#44475AA0',
        'scrollbarSlider.activeBackground': '#44475AC0',
        'minimap.background': '#282A36',
        'minimapSlider.background': '#44475A80',
        'minimapSlider.hoverBackground': '#44475AA0',
        'minimapSlider.activeBackground': '#44475AC0',
        'editorOverviewRuler.background': '#282A36',
        'editorOverviewRuler.border': '#00000000',
        'editorOverviewRuler.findMatchForeground': '#F1FA8C',
        'editorOverviewRuler.rangeHighlightForeground': '#44475A',
        'editorOverviewRuler.selectionHighlightForeground': '#44475A',
        'editorOverviewRuler.wordHighlightForeground': '#44475A',
        'editorOverviewRuler.wordHighlightStrongForeground': '#44475A',
        'editorOverviewRuler.modifiedForeground': '#8BE9FD',
        'editorOverviewRuler.addedForeground': '#50FA7B',
        'editorOverviewRuler.deletedForeground': '#FF5555',
        'editorOverviewRuler.errorForeground': '#FF5555',
        'editorOverviewRuler.warningForeground': '#FFB86C',
        'editorOverviewRuler.infoForeground': '#8BE9FD',
        'editorSuggestWidget.background': '#282A36',
        'editorSuggestWidget.border': '#44475A',
        'editorSuggestWidget.foreground': '#F8F8F2',
        'editorSuggestWidget.selectedBackground': '#44475A',
        'editorHoverWidget.background': '#282A36',
        'editorHoverWidget.border': '#44475A',
        'editorHoverWidget.foreground': '#F8F8F2',
        'peekView.border': '#44475A',
        'peekViewEditor.background': '#282A36',
        'peekViewEditorGutter.background': '#282A36',
        'peekViewResult.background': '#282A36',
        'peekViewTitle.background': '#44475A',
        'peekViewTitleDescription.foreground': '#6272A4',
        'peekViewTitleLabel.foreground': '#F8F8F2',
      }
    });

    // 创建编辑器
    const editor = monaco.editor.create(editorRef.current, {
      value: content,
      language: language,
      theme: 'dracula',
      fontSize: fontSize,
      automaticLayout: true,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      lineNumbers: 'on',
      folding: true,
      renderLineHighlight: 'all',
      selectOnLineNumbers: true,
      roundedSelection: false,
      readOnly: false,
      cursorStyle: 'line',
      cursorBlinking: 'blink',
      cursorSmoothCaretAnimation: 'on',
      smoothScrolling: true,
      mouseWheelZoom: true,
      multiCursorModifier: 'alt',
      accessibilitySupport: 'auto',
      copyWithSyntaxHighlighting: true,
      emptySelectionClipboard: true,
      dragAndDrop: true,
      links: true,
      hover: { enabled: true, delay: 300 },
      parameterHints: { enabled: true },
      suggestOnTriggerCharacters: true,
      acceptSuggestionOnEnter: 'on',
      tabCompletion: 'on',
      snippetSuggestions: 'inline',
      formatOnPaste: true,
      formatOnType: true,
      autoIndent: 'full',
      autoClosingBrackets: 'always',
      autoClosingQuotes: 'always',
      autoSurround: 'languageDefined',
      bracketPairColorization: { enabled: true },
      guides: {
        bracketPairs: true,
        indentation: true,
        bracketPairsHorizontal: true,
        highlightActiveIndentation: true
      },
      renderWhitespace: 'selection',
      renderControlCharacters: true,
      rulers: [],
      colorDecorators: true,
      codeActionsOnSaveTimeout: 1000,
      quickSuggestions: {
        other: 'on',
        comments: 'on',
        strings: 'on'
      },
      quickSuggestionsDelay: 100,
      contextmenu: true,
      find: {
        seedSearchStringFromSelection: 'always',
        autoFindInSelection: 'multiline'
      },
      gotoLocation: {
        multipleReferences: 'goto',
        multipleDefinitions: 'goto',
        multipleDeclarations: 'goto',
        multipleImplementations: 'goto',
        multipleTypeDefinitions: 'goto'
      },
      lightbulb: { enabled: 'on' },
      codeLens: true,
      inlineSuggest: { enabled: true },
      unicodeHighlight: {
        ambiguousCharacters: true,
        invisibleCharacters: true
      },
      stickyScroll: { enabled: true },
      padding: { top: 0, bottom: 0 },
      fixedOverflowWidgets: true,
      overviewRulerBorder: false,
      hideCursorInOverviewRuler: true,
      overviewRulerLanes: 3,
      renderValidationDecorations: 'on',
      scrollbar: {
        vertical: 'auto',
        horizontal: 'auto',
        useShadows: false,
        verticalHasArrows: false,
        horizontalHasArrows: false,
        verticalScrollbarSize: 14,
        horizontalScrollbarSize: 14,
        arrowSize: 11
      }
    });

    monacoEditorRef.current = editor;

    // 监听内容变化
    editor.onDidChangeModelContent(() => {
      const newContent = editor.getValue();
      setContent(newContent);
    });

    // 添加快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });

    // 添加折叠所有代码的快捷键 (Ctrl+Shift+[)
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.BracketLeft, () => {
      editor.trigger('fold', 'editor.foldAll');
    });

    // 添加展开所有代码的快捷键 (Ctrl+Shift+])
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.BracketRight, () => {
      editor.trigger('unfold', 'editor.unfoldAll');
    });

    // 自定义右键菜单
    editor.addAction({
      id: 'fold-all-code',
      label: '🔽 折叠所有代码',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.1,
      run: () => {
        editor.trigger('fold', 'editor.foldAll');
        message.success('已折叠所有代码');
      }
    });

    editor.addAction({
      id: 'unfold-all-code',
      label: '🔼 展开所有代码',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.2,
      run: () => {
        editor.trigger('unfold', 'editor.unfoldAll');
        message.success('已展开所有代码');
      }
    });

    // 添加折叠当前级别的功能
    editor.addAction({
      id: 'fold-level-1',
      label: '📁 折叠到级别 1',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.3,
      run: () => {
        editor.trigger('fold', 'editor.foldLevel1');
        message.success('已折叠到级别 1');
      }
    });

    editor.addAction({
      id: 'fold-level-2',
      label: '📁 折叠到级别 2',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.4,
      run: () => {
        editor.trigger('fold', 'editor.foldLevel2');
        message.success('已折叠到级别 2');
      }
    });

    editor.addAction({
      id: 'fold-level-3',
      label: '📁 折叠到级别 3',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.5,
      run: () => {
        editor.trigger('fold', 'editor.foldLevel3');
        message.success('已折叠到级别 3');
      }
    });

    // 添加分隔符
    editor.addAction({
      id: 'separator-1',
      label: '─────────────────',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.6,
      run: () => {}
    });

    // 添加格式化代码功能
    editor.addAction({
      id: 'format-document',
      label: '✨ 格式化文档',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.7,
      keybindings: [monaco.KeyMod.Shift | monaco.KeyMod.Alt | monaco.KeyCode.KeyF],
      run: () => {
        editor.trigger('format', 'editor.action.formatDocument');
        message.success('文档格式化完成');
      }
    });

    // 添加保存功能到右键菜单
    editor.addAction({
      id: 'save-file',
      label: '💾 保存文件',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.8,
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS],
      run: () => {
        handleSave();
      }
    });

    // 添加第二个分隔符
    editor.addAction({
      id: 'separator-2',
      label: '─────────────────',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.9,
      run: () => {}
    });

    // 添加选择所有内容功能
    editor.addAction({
      id: 'select-all',
      label: '📋 选择全部',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 2.0,
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyA],
      run: () => {
        editor.trigger('select', 'editor.action.selectAll');
        message.success('已选择全部内容');
      }
    });

    // 添加复制所有内容功能
    editor.addAction({
      id: 'copy-all',
      label: '📄 复制全部内容',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 2.1,
      run: () => {
        const content = editor.getValue();
        navigator.clipboard.writeText(content).then(() => {
          message.success('已复制全部内容到剪贴板');
        }).catch(() => {
          message.error('复制失败');
        });
      }
    });

    // 添加查找和替换功能
    editor.addAction({
      id: 'find-replace',
      label: '🔍 查找和替换',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 2.2,
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyH],
      run: () => {
        editor.trigger('find', 'editor.action.startFindReplaceAction');
      }
    });

    // 添加转到行功能
    editor.addAction({
      id: 'goto-line',
      label: '🎯 转到行',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 2.3,
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyG],
      run: () => {
        editor.trigger('goto', 'editor.action.gotoLine');
      }
    });

    // 添加第三个分隔符
    editor.addAction({
      id: 'separator-3',
      label: '─────────────────',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 2.4,
      run: () => {}
    });

    // 添加切换注释功能
    editor.addAction({
      id: 'toggle-comment',
      label: '💬 切换行注释',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 2.5,
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.Slash],
      run: () => {
        editor.trigger('comment', 'editor.action.commentLine');
        message.success('已切换行注释');
      }
    });

    // 添加切换块注释功能
    editor.addAction({
      id: 'toggle-block-comment',
      label: '💭 切换块注释',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 2.6,
      keybindings: [monaco.KeyMod.Shift | monaco.KeyMod.Alt | monaco.KeyCode.KeyA],
      run: () => {
        editor.trigger('comment', 'editor.action.blockComment');
        message.success('已切换块注释');
      }
    });

    // 添加代码缩进功能
    editor.addAction({
      id: 'indent-lines',
      label: '➡️ 增加缩进',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 2.7,
      keybindings: [monaco.KeyCode.Tab],
      run: () => {
        editor.trigger('indent', 'editor.action.indentLines');
      }
    });

    // 添加代码反缩进功能
    editor.addAction({
      id: 'outdent-lines',
      label: '⬅️ 减少缩进',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 2.8,
      keybindings: [monaco.KeyMod.Shift | monaco.KeyCode.Tab],
      run: () => {
        editor.trigger('outdent', 'editor.action.outdentLines');
      }
    });

    console.log('Monaco Editor initialized with language:', language);
    }, 50); // 50ms 延迟

    // 清理函数
    return () => {
      if (initTimer) {
        clearTimeout(initTimer);
      }
      if (monacoEditorRef.current) {
        monacoEditorRef.current.dispose();
        monacoEditorRef.current = null;
      }
    };
  }, []);

  // 监听语言变化，重新设置编辑器语言
  useEffect(() => {
    if (monacoEditorRef.current) {
      const model = monacoEditorRef.current.getModel();
      if (model) {
        monaco.editor.setModelLanguage(model, language);
        console.log('Language updated to:', language);
      }
    }
  }, [language]);

  // 监听内容变化，更新编辑器
  useEffect(() => {
    if (monacoEditorRef.current && monacoEditorRef.current.getValue() !== content) {
      monacoEditorRef.current.setValue(content);
    }
  }, [content]);

  const getLanguageByExtension = (extension: string): string => {
    const languageMap: Record<string, string> = {
      // JavaScript/TypeScript
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'mjs': 'javascript',
      'cjs': 'javascript',

      // Web Technologies
      'html': 'html',
      'htm': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'svg': 'xml',

      // Markup & Documentation
      'md': 'markdown',
      'markdown': 'markdown',
      'rst': 'restructuredtext',
      'tex': 'latex',

      // Programming Languages
      'py': 'python',
      'pyw': 'python',
      'rs': 'rust',
      'go': 'go',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'cc': 'cpp',
      'cxx': 'cpp',
      'h': 'c',
      'hpp': 'cpp',
      'hxx': 'cpp',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'clj': 'clojure',
      'fs': 'fsharp',
      'vb': 'vb',
      'dart': 'dart',
      'r': 'r',
      'lua': 'lua',
      'perl': 'perl',
      'pl': 'perl',

      // Shell & Scripts
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',
      'bat': 'bat',
      'cmd': 'bat',
      'ps1': 'powershell',

      // Database
      'sql': 'sql',
      'mysql': 'mysql',
      'pgsql': 'pgsql',

      // Configuration
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'conf': 'ini',
      'cfg': 'ini',
      'properties': 'properties',
      'env': 'shell',

      // Docker & DevOps
      'dockerfile': 'dockerfile',
      'dockerignore': 'ignore',
      'gitignore': 'ignore',
      'gitattributes': 'ignore',

      // Others
      'txt': 'plaintext',
      'log': 'log',
      'csv': 'csv',
      'tsv': 'csv',
    };

    const detectedLanguage = languageMap[extension] || 'plaintext';
    console.log(`File extension: ${extension}, Detected language: ${detectedLanguage}`);
    return detectedLanguage;
  };

  const handleSave = async () => {
    if (!file || file.is_directory) return;

    try {
      setSaving(true);
      await invoke('write_file', { fileId: file.id, content });
      setOriginalContent(content);
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败：' + error);
    } finally {
      setSaving(false);
    }
  };



  return (
    <div style={{ height: '100%', width: '100%', background: '#282A36' }}>
      {loading ? (
        <div style={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: '#282A36',
          color: '#F8F8F2'
        }}>
          <Spin tip="加载中..." />
        </div>
      ) : file ? (
        file.is_directory ? (
          <div style={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: '#282A36',
            color: '#F8F8F2'
          }}>
            <Empty description="无法编辑文件夹" />
          </div>
        ) : (
          <div
            ref={editorRef}
            className="monaco-editor-container"
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              outline: 'none',
              overflow: 'hidden',
              position: 'relative',
              backgroundColor: '#282A36'
            }}
          />
        )
      ) : (
        <div style={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: '#282A36',
          color: '#F8F8F2'
        }}>
          <Empty description="请先选择一个文件" />
        </div>
      )}
    </div>
  );
};

export default FileEditor;
