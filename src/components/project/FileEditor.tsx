import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
  Button,
  Space,
  message,
  Layout,
  Row,
  Col,
  Card,
  Divider,
  Spin,
  Empty,
  Breadcrumb,
  Tooltip,
  Select,
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  FileOutlined,
  CopyOutlined,
  UndoOutlined,
  RedoOutlined,
} from '@ant-design/icons';
import { ProjectFile } from '../../types';
import MonacoEditor from 'react-monaco-editor';

// 移除 Typography 导入
const { Option } = Select;

interface FileEditorProps {
  file: ProjectFile | null;
}

const FileEditor: React.FC<FileEditorProps> = ({ file }) => {
  const [content, setContent] = useState<string>('');
  const [originalContent, setOriginalContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [language, setLanguage] = useState<string>('plaintext');
  const [theme, setTheme] = useState<string>('vs');
  const [fontSize, setFontSize] = useState<number>(14);

  const fetchFileContent = async () => {
    if (!file || file.is_directory) return;

    try {
      setLoading(true);
      const fileContent = await invoke('read_file', { fileId: file.id }) as string;
      setContent(fileContent);
      setOriginalContent(fileContent);

      // 根据文件扩展名设置语言
      const extension = file.name.split('.').pop()?.toLowerCase() || '';
      setLanguage(getLanguageByExtension(extension));
    } catch (error) {
      message.error('获取文件内容失败：' + error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (file) {
      fetchFileContent();
    } else {
      setContent('');
      setOriginalContent('');
    }
  }, [file]);

  const getLanguageByExtension = (extension: string): string => {
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'html': 'html',
      'css': 'css',
      'json': 'json',
      'md': 'markdown',
      'py': 'python',
      'rs': 'rust',
      'go': 'go',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'h': 'cpp',
      'hpp': 'cpp',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'sql': 'sql',
      'sh': 'shell',
      'bat': 'bat',
      'ps1': 'powershell',
      'yaml': 'yaml',
      'yml': 'yaml',
      'xml': 'xml',
      'toml': 'toml',
      'ini': 'ini',
      'conf': 'ini',
      'txt': 'plaintext',
    };

    return languageMap[extension] || 'plaintext';
  };

  const handleSave = async () => {
    if (!file || file.is_directory) return;

    try {
      setSaving(true);
      await invoke('write_file', { fileId: file.id, content });
      setOriginalContent(content);
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败：' + error);
    } finally {
      setSaving(false);
    }
  };

  const handleReload = async () => {
    if (!file || file.is_directory) return;

    // 如果内容已修改，提示用户
    if (content !== originalContent) {
      const confirmed = window.confirm('文件已修改，重新加载将丢失未保存的更改。确定要继续吗？');
      if (!confirmed) return;
    }

    fetchFileContent();
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(content)
      .then(() => message.success('已复制到剪贴板'))
      .catch(() => message.error('复制失败'));
  };

  const handleReset = () => {
    if (content !== originalContent) {
      const confirmed = window.confirm('确定要放弃所有更改吗？');
      if (confirmed) {
        setContent(originalContent);
      }
    }
  };

  const editorDidMount = (editor: any, monaco: any) => {
    editor.focus();

    // 添加编辑器快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });

    // 设置自动保存
    let autoSaveTimer: NodeJS.Timeout | null = null;
    editor.onDidChangeModelContent(() => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }

      autoSaveTimer = setTimeout(() => {
        if (content !== originalContent) {
          handleSave();
        }
      }, 5000); // 5秒后自动保存
    });

    // 设置编辑器选项
    monaco.editor.defineTheme('customDarkTheme', {
      base: 'vs-dark',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editor.lineHighlightBackground': '#2a2a2a',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41',
        'editorCursor.foreground': '#d4d4d4',
      }
    });

    // 根据当前主题设置编辑器主题
    if (theme === 'vs-dark') {
      monaco.editor.setTheme('customDarkTheme');
    }
  };

  const handleEditorChange = (value: string) => {
    setContent(value);
  };

  return (
    <Layout style={{ background: '#fff', padding: '24px', minHeight: 'calc(100vh - 112px)' }}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card>
            <Row justify="space-between" align="middle">
              <Col>
                <h4 style={{ margin: '0 0 8px 0', fontWeight: 'bold' }}>文件编辑器</h4>
                {file && (
                  <Breadcrumb>
                    <Breadcrumb.Item>文件</Breadcrumb.Item>
                    <Breadcrumb.Item>{file.path}</Breadcrumb.Item>
                  </Breadcrumb>
                )}
              </Col>
              <Col>
                <Space>
                  <Select
                    value={language}
                    style={{ width: 120 }}
                    onChange={setLanguage}
                  >
                    <Option value="plaintext">纯文本</Option>
                    <Option value="javascript">JavaScript</Option>
                    <Option value="typescript">TypeScript</Option>
                    <Option value="html">HTML</Option>
                    <Option value="css">CSS</Option>
                    <Option value="json">JSON</Option>
                    <Option value="markdown">Markdown</Option>
                    <Option value="python">Python</Option>
                    <Option value="rust">Rust</Option>
                    <Option value="go">Go</Option>
                    <Option value="java">Java</Option>
                    <Option value="c">C</Option>
                    <Option value="cpp">C++</Option>
                  </Select>
                  <Select
                    value={theme}
                    style={{ width: 120 }}
                    onChange={setTheme}
                  >
                    <Option value="vs">浅色</Option>
                    <Option value="vs-dark">深色</Option>
                    <Option value="hc-black">高对比度</Option>
                  </Select>
                  <Select
                    value={fontSize}
                    style={{ width: 80 }}
                    onChange={setFontSize}
                  >
                    {[12, 14, 16, 18, 20, 22, 24].map(size => (
                      <Option key={size} value={size}>{size}px</Option>
                    ))}
                  </Select>
                  {file && !file.is_directory && (
                    <>
                      <Tooltip title="保存">
                        <Button
                          icon={<SaveOutlined />}
                          onClick={handleSave}
                          loading={saving}
                          disabled={content === originalContent}
                        />
                      </Tooltip>
                      <Tooltip title="重新加载">
                        <Button
                          icon={<ReloadOutlined />}
                          onClick={handleReload}
                        />
                      </Tooltip>
                      <Tooltip title="复制内容">
                        <Button
                          icon={<CopyOutlined />}
                          onClick={handleCopy}
                        />
                      </Tooltip>
                      <Tooltip title="重置更改">
                        <Button
                          icon={<UndoOutlined />}
                          onClick={handleReset}
                          disabled={content === originalContent}
                        />
                      </Tooltip>
                    </>
                  )}
                </Space>
              </Col>
            </Row>
            <Divider />

            {loading ? (
              <div style={{ textAlign: 'center', padding: '50px 0' }}>
                <Spin tip="加载中..." />
              </div>
            ) : file ? (
              file.is_directory ? (
                <Empty description="无法编辑文件夹" />
              ) : (
                <div style={{ border: '1px solid #d9d9d9', borderRadius: '2px' }}>
                  <MonacoEditor
                    width="100%"
                    height="600"
                    language={language}
                    theme={theme}
                    value={content}
                    options={{
                      selectOnLineNumbers: true,
                      roundedSelection: false,
                      readOnly: false,
                      cursorStyle: 'line',
                      automaticLayout: true,
                      fontSize: fontSize,
                      minimap: { enabled: true },
                      scrollBeyondLastLine: false,
                      wordWrap: 'on',
                      lineNumbers: 'on',
                      folding: true,
                      foldingStrategy: 'auto',
                      showFoldingControls: 'always',
                      renderLineHighlight: 'all',
                      parameterHints: { enabled: true },
                      suggestOnTriggerCharacters: true,
                      acceptSuggestionOnEnter: 'on',
                      tabCompletion: 'on',
                      // wordBasedSuggestions: "on", // 移除不兼容的配置
                      snippetSuggestions: 'inline',
                      formatOnPaste: true,
                      formatOnType: true,
                      autoIndent: 'full',
                      autoClosingBrackets: 'always',
                      autoClosingQuotes: 'always',
                      autoSurround: 'languageDefined',
                      bracketPairColorization: { enabled: true },
                      guides: { bracketPairs: true, indentation: true },
                      renderWhitespace: 'selection',
                      renderControlCharacters: true,
                      rulers: [],
                      colorDecorators: true,
                      // lightbulb: "on", // 移除不兼容的配置
                      codeActionsOnSaveTimeout: 1000,
                      // codeActions: { extractFunction: true, extractConstant: true }, // 移除不兼容的配置
                      quickSuggestions: { other: true, comments: true, strings: true },
                      quickSuggestionsDelay: 100,
                      contextmenu: true,
                      mouseWheelZoom: true,
                      multiCursorModifier: 'alt',
                      accessibilitySupport: 'auto',
                      copyWithSyntaxHighlighting: true,
                      emptySelectionClipboard: true,
                      dragAndDrop: true,
                      links: true,
                      hover: { enabled: true, delay: 300 },
                      // find: { seedSearchStringFromSelection: "always", autoFindInSelection: 'multiline' }, // 移除不兼容的配置
                    }}
                    onChange={handleEditorChange}
                    editorDidMount={editorDidMount}
                  />
                </div>
              )
            ) : (
              <Empty description="请先选择一个文件" />
            )}
          </Card>
        </Col>
      </Row>
    </Layout>
  );
};

export default FileEditor;
