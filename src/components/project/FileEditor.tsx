import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
  Button,
  Space,
  message,
  Layout,
  Row,
  Col,
  Card,
  Divider,
  Spin,
  Empty,
  Breadcrumb,
  Tooltip,
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  FileOutlined,
  CopyOutlined,
  UndoOutlined,
  RedoOutlined,
} from '@ant-design/icons';
import { ProjectFile } from '../../types';
import MonacoEditor from 'react-monaco-editor';



interface FileEditorProps {
  file: ProjectFile | null;
}

const FileEditor: React.FC<FileEditorProps> = ({ file }) => {
  const [content, setContent] = useState<string>('');
  const [originalContent, setOriginalContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [language, setLanguage] = useState<string>('plaintext');
  const fontSize = 14; // 固定字体大小

  const fetchFileContent = async () => {
    if (!file || file.is_directory) return;

    try {
      setLoading(true);
      const fileContent = await invoke('read_file', { fileId: file.id }) as string;
      setContent(fileContent);
      setOriginalContent(fileContent);

      // 根据文件扩展名设置语言
      const extension = file.name.split('.').pop()?.toLowerCase() || '';
      setLanguage(getLanguageByExtension(extension));
    } catch (error) {
      message.error('获取文件内容失败：' + error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (file) {
      fetchFileContent();
    } else {
      setContent('');
      setOriginalContent('');
    }
  }, [file]);

  const getLanguageByExtension = (extension: string): string => {
    const languageMap: Record<string, string> = {
      // JavaScript/TypeScript
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'mjs': 'javascript',
      'cjs': 'javascript',

      // Web Technologies
      'html': 'html',
      'htm': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'svg': 'xml',

      // Markup & Documentation
      'md': 'markdown',
      'markdown': 'markdown',
      'rst': 'restructuredtext',
      'tex': 'latex',

      // Programming Languages
      'py': 'python',
      'pyw': 'python',
      'rs': 'rust',
      'go': 'go',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'cc': 'cpp',
      'cxx': 'cpp',
      'h': 'c',
      'hpp': 'cpp',
      'hxx': 'cpp',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'clj': 'clojure',
      'fs': 'fsharp',
      'vb': 'vb',
      'dart': 'dart',
      'r': 'r',
      'lua': 'lua',
      'perl': 'perl',
      'pl': 'perl',

      // Shell & Scripts
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',
      'bat': 'bat',
      'cmd': 'bat',
      'ps1': 'powershell',

      // Database
      'sql': 'sql',
      'mysql': 'mysql',
      'pgsql': 'pgsql',

      // Configuration
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'conf': 'ini',
      'cfg': 'ini',
      'properties': 'properties',
      'env': 'shell',

      // Docker & DevOps
      'dockerfile': 'dockerfile',
      'dockerignore': 'ignore',
      'gitignore': 'ignore',
      'gitattributes': 'ignore',

      // Others
      'txt': 'plaintext',
      'log': 'log',
      'csv': 'csv',
      'tsv': 'csv',
    };

    return languageMap[extension] || 'plaintext';
  };

  const handleSave = async () => {
    if (!file || file.is_directory) return;

    try {
      setSaving(true);
      await invoke('write_file', { fileId: file.id, content });
      setOriginalContent(content);
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败：' + error);
    } finally {
      setSaving(false);
    }
  };

  const handleReload = async () => {
    if (!file || file.is_directory) return;

    // 如果内容已修改，提示用户
    if (content !== originalContent) {
      const confirmed = window.confirm('文件已修改，重新加载将丢失未保存的更改。确定要继续吗？');
      if (!confirmed) return;
    }

    fetchFileContent();
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(content)
      .then(() => message.success('已复制到剪贴板'))
      .catch(() => message.error('复制失败'));
  };

  const handleReset = () => {
    if (content !== originalContent) {
      const confirmed = window.confirm('确定要放弃所有更改吗？');
      if (confirmed) {
        setContent(originalContent);
      }
    }
  };

  const editorDidMount = (editor: any, monaco: any) => {
    editor.focus();

    // 添加编辑器快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });

    // 设置自动保存
    let autoSaveTimer: NodeJS.Timeout | null = null;
    editor.onDidChangeModelContent(() => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }

      autoSaveTimer = setTimeout(() => {
        if (content !== originalContent) {
          handleSave();
        }
      }, 5000); // 5秒后自动保存
    });

    // 定义 Dracula 主题
    monaco.editor.defineTheme('dracula', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6272A4', fontStyle: 'italic' },
        { token: 'keyword', foreground: 'FF79C6' },
        { token: 'string', foreground: 'F1FA8C' },
        { token: 'number', foreground: 'BD93F9' },
        { token: 'regexp', foreground: 'F1FA8C' },
        { token: 'type', foreground: '8BE9FD' },
        { token: 'class', foreground: '8BE9FD' },
        { token: 'function', foreground: '50FA7B' },
        { token: 'variable', foreground: 'F8F8F2' },
        { token: 'constant', foreground: 'BD93F9' },
        { token: 'operator', foreground: 'FF79C6' },
        { token: 'delimiter', foreground: 'F8F8F2' },
        { token: 'tag', foreground: 'FF79C6' },
        { token: 'attribute.name', foreground: '50FA7B' },
        { token: 'attribute.value', foreground: 'F1FA8C' },
        { token: 'key', foreground: '8BE9FD' },
        { token: 'value', foreground: 'F1FA8C' },
        { token: 'identifier', foreground: 'F8F8F2' },
      ],
      colors: {
        'editor.background': '#282A36',
        'editor.foreground': '#F8F8F2',
        'editor.lineHighlightBackground': '#44475A',
        'editor.selectionBackground': '#44475A',
        'editor.inactiveSelectionBackground': '#44475A75',
        'editorCursor.foreground': '#F8F8F2',
        'editorLineNumber.foreground': '#6272A4',
        'editorLineNumber.activeForeground': '#F8F8F2',
        'editor.selectionHighlightBackground': '#44475A50',
        'editor.wordHighlightBackground': '#44475A75',
        'editor.wordHighlightStrongBackground': '#44475A',
        'editorBracketMatch.background': '#44475A',
        'editorBracketMatch.border': '#50FA7B',
        'editorGutter.background': '#282A36',
        'editorGutter.modifiedBackground': '#8BE9FD',
        'editorGutter.addedBackground': '#50FA7B',
        'editorGutter.deletedBackground': '#FF5555',
        'editorError.foreground': '#FF5555',
        'editorWarning.foreground': '#FFB86C',
        'editorInfo.foreground': '#8BE9FD',
        'editorHint.foreground': '#6272A4',
        'editorIndentGuide.background': '#44475A',
        'editorIndentGuide.activeBackground': '#6272A4',
        'editorWhitespace.foreground': '#44475A',
      }
    });

    // 定义自定义主题
    monaco.editor.defineTheme('vsCodeDark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'regexp', foreground: 'D16969' },
        { token: 'type', foreground: '4EC9B0' },
        { token: 'class', foreground: '4EC9B0' },
        { token: 'function', foreground: 'DCDCAA' },
        { token: 'variable', foreground: '9CDCFE' },
        { token: 'constant', foreground: '4FC1FF' },
        { token: 'operator', foreground: 'D4D4D4' },
        { token: 'delimiter', foreground: 'D4D4D4' },
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editor.lineHighlightBackground': '#2a2a2a',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41',
        'editorCursor.foreground': '#d4d4d4',
        'editorLineNumber.foreground': '#858585',
        'editorLineNumber.activeForeground': '#c6c6c6',
        'editor.selectionHighlightBackground': '#add6ff26',
        'editor.wordHighlightBackground': '#575757b8',
        'editor.wordHighlightStrongBackground': '#004972b8',
        'editorBracketMatch.background': '#0064001a',
        'editorBracketMatch.border': '#888888',
        'editorGutter.background': '#1e1e1e',
        'editorGutter.modifiedBackground': '#1b81a8',
        'editorGutter.addedBackground': '#487e02',
        'editorGutter.deletedBackground': '#f85149',
        'editorError.foreground': '#f85149',
        'editorWarning.foreground': '#ff8c00',
        'editorInfo.foreground': '#3794ff',
        'editorHint.foreground': '#eeeeeeb3',
      }
    });

    monaco.editor.defineTheme('vsCodeLight', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '008000', fontStyle: 'italic' },
        { token: 'keyword', foreground: '0000FF' },
        { token: 'string', foreground: 'A31515' },
        { token: 'number', foreground: '098658' },
        { token: 'regexp', foreground: '811F3F' },
        { token: 'type', foreground: '267F99' },
        { token: 'class', foreground: '267F99' },
        { token: 'function', foreground: '795E26' },
        { token: 'variable', foreground: '001080' },
        { token: 'constant', foreground: '0070C1' },
        { token: 'operator', foreground: '000000' },
        { token: 'delimiter', foreground: '000000' },
      ],
      colors: {
        'editor.background': '#ffffff',
        'editor.foreground': '#000000',
        'editor.lineHighlightBackground': '#f0f0f0',
        'editor.selectionBackground': '#add6ff',
        'editor.inactiveSelectionBackground': '#e5ebf1',
        'editorCursor.foreground': '#000000',
        'editorLineNumber.foreground': '#237893',
        'editorLineNumber.activeForeground': '#0b216f',
        'editor.selectionHighlightBackground': '#add6ff80',
        'editor.wordHighlightBackground': '#57575740',
        'editor.wordHighlightStrongBackground': '#0064004d',
        'editorBracketMatch.background': '#0064001a',
        'editorBracketMatch.border': '#b9b9b9',
        'editorGutter.background': '#ffffff',
        'editorGutter.modifiedBackground': '#1b81a8',
        'editorGutter.addedBackground': '#487e02',
        'editorGutter.deletedBackground': '#f85149',
        'editorError.foreground': '#e51400',
        'editorWarning.foreground': '#bf8803',
        'editorInfo.foreground': '#1a85ff',
        'editorHint.foreground': '#6c6c6c',
      }
    });

    // 设置编辑器主题为 Dracula
    monaco.editor.setTheme('dracula');

    // 确保语法高亮正常工作
    monaco.editor.setModelLanguage(editor.getModel(), language);
  };

  const handleEditorChange = (value: string) => {
    setContent(value);
  };

  return (
    <Layout style={{ background: '#282A36', padding: '24px', minHeight: 'calc(100vh - 112px)' }}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card style={{ background: '#282A36', border: '1px solid #44475A' }}>
            <Row justify="space-between" align="middle">
              <Col>
                <h4 style={{ margin: '0 0 8px 0', fontWeight: 'bold', color: '#F8F8F2' }}>文件编辑器</h4>
                {file && (
                  <Breadcrumb style={{ color: '#6272A4' }}>
                    <Breadcrumb.Item style={{ color: '#6272A4' }}>文件</Breadcrumb.Item>
                    <Breadcrumb.Item style={{ color: '#F8F8F2' }}>{file.path}</Breadcrumb.Item>
                  </Breadcrumb>
                )}
              </Col>
              <Col>
                {file && !file.is_directory && (
                  <Space>
                    <Tooltip title="保存 (Ctrl+S)">
                      <Button
                        icon={<SaveOutlined />}
                        onClick={handleSave}
                        loading={saving}
                        disabled={content === originalContent}
                      />
                    </Tooltip>
                    <Tooltip title="重新加载">
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={handleReload}
                      />
                    </Tooltip>
                    <Tooltip title="复制内容">
                      <Button
                        icon={<CopyOutlined />}
                        onClick={handleCopy}
                      />
                    </Tooltip>
                    <Tooltip title="重置更改">
                      <Button
                        icon={<UndoOutlined />}
                        onClick={handleReset}
                        disabled={content === originalContent}
                      />
                    </Tooltip>
                  </Space>
                )}
              </Col>
            </Row>
            <Divider />

            {loading ? (
              <div style={{ textAlign: 'center', padding: '50px 0' }}>
                <Spin tip="加载中..." />
              </div>
            ) : file ? (
              file.is_directory ? (
                <Empty description="无法编辑文件夹" />
              ) : (
                <div style={{ border: '1px solid #44475A', borderRadius: '4px', overflow: 'hidden' }}>
                  <MonacoEditor
                    width="100%"
                    height="600"
                    language={language}
                    theme="dracula"
                    value={content}
                    options={{
                      selectOnLineNumbers: true,
                      roundedSelection: false,
                      readOnly: false,
                      cursorStyle: 'line',
                      automaticLayout: true,
                      fontSize: fontSize,
                      minimap: { enabled: true },
                      scrollBeyondLastLine: false,
                      wordWrap: 'on',
                      lineNumbers: 'on',
                      folding: true,
                      foldingStrategy: 'auto',
                      showFoldingControls: 'always',
                      renderLineHighlight: 'all',
                      parameterHints: { enabled: true },
                      suggestOnTriggerCharacters: true,
                      acceptSuggestionOnEnter: 'on',
                      tabCompletion: 'on',
                      // wordBasedSuggestions: "on", // 移除不兼容的配置
                      snippetSuggestions: 'inline',
                      formatOnPaste: true,
                      formatOnType: true,
                      autoIndent: 'full',
                      autoClosingBrackets: 'always',
                      autoClosingQuotes: 'always',
                      autoSurround: 'languageDefined',
                      bracketPairColorization: { enabled: true },
                      guides: { bracketPairs: true, indentation: true },
                      renderWhitespace: 'selection',
                      renderControlCharacters: true,
                      rulers: [],
                      colorDecorators: true,
                      // lightbulb: "on", // 移除不兼容的配置
                      codeActionsOnSaveTimeout: 1000,
                      // codeActions: { extractFunction: true, extractConstant: true }, // 移除不兼容的配置
                      quickSuggestions: { other: true, comments: true, strings: true },
                      quickSuggestionsDelay: 100,
                      contextmenu: true,
                      mouseWheelZoom: true,
                      multiCursorModifier: 'alt',
                      accessibilitySupport: 'auto',
                      copyWithSyntaxHighlighting: true,
                      emptySelectionClipboard: true,
                      dragAndDrop: true,
                      links: true,
                      hover: { enabled: true, delay: 300 },
                      // find: { seedSearchStringFromSelection: "always", autoFindInSelection: 'multiline' }, // 移除不兼容的配置
                    }}
                    onChange={handleEditorChange}
                    editorDidMount={editorDidMount}
                  />
                </div>
              )
            ) : (
              <Empty description="请先选择一个文件" />
            )}
          </Card>
        </Col>
      </Row>
    </Layout>
  );
};

export default FileEditor;
