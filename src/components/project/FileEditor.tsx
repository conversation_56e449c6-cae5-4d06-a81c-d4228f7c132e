import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
  message,
  Spin,
  Empty,
} from 'antd';
import { ProjectFile } from '../../types';
import MonacoEditor from 'react-monaco-editor';



interface FileEditorProps {
  file: ProjectFile | null;
}

const FileEditor: React.FC<FileEditorProps> = ({ file }) => {
  const [content, setContent] = useState<string>('');
  const [originalContent, setOriginalContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [language, setLanguage] = useState<string>('plaintext');
  const fontSize = 14; // 固定字体大小

  const fetchFileContent = async () => {
    if (!file || file.is_directory) return;

    try {
      setLoading(true);
      const fileContent = await invoke('read_file', { fileId: file.id }) as string;
      setContent(fileContent);
      setOriginalContent(fileContent);

      // 根据文件扩展名设置语言
      const extension = file.name.split('.').pop()?.toLowerCase() || '';
      setLanguage(getLanguageByExtension(extension));
    } catch (error) {
      message.error('获取文件内容失败：' + error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (file) {
      fetchFileContent();
    } else {
      setContent('');
      setOriginalContent('');
    }
  }, [file]);

  const getLanguageByExtension = (extension: string): string => {
    const languageMap: Record<string, string> = {
      // JavaScript/TypeScript
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'mjs': 'javascript',
      'cjs': 'javascript',

      // Web Technologies
      'html': 'html',
      'htm': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'svg': 'xml',

      // Markup & Documentation
      'md': 'markdown',
      'markdown': 'markdown',
      'rst': 'restructuredtext',
      'tex': 'latex',

      // Programming Languages
      'py': 'python',
      'pyw': 'python',
      'rs': 'rust',
      'go': 'go',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'cc': 'cpp',
      'cxx': 'cpp',
      'h': 'c',
      'hpp': 'cpp',
      'hxx': 'cpp',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'clj': 'clojure',
      'fs': 'fsharp',
      'vb': 'vb',
      'dart': 'dart',
      'r': 'r',
      'lua': 'lua',
      'perl': 'perl',
      'pl': 'perl',

      // Shell & Scripts
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',
      'bat': 'bat',
      'cmd': 'bat',
      'ps1': 'powershell',

      // Database
      'sql': 'sql',
      'mysql': 'mysql',
      'pgsql': 'pgsql',

      // Configuration
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'conf': 'ini',
      'cfg': 'ini',
      'properties': 'properties',
      'env': 'shell',

      // Docker & DevOps
      'dockerfile': 'dockerfile',
      'dockerignore': 'ignore',
      'gitignore': 'ignore',
      'gitattributes': 'ignore',

      // Others
      'txt': 'plaintext',
      'log': 'log',
      'csv': 'csv',
      'tsv': 'csv',
    };

    const detectedLanguage = languageMap[extension] || 'plaintext';
    console.log(`File extension: ${extension}, Detected language: ${detectedLanguage}`);
    return detectedLanguage;
  };

  const handleSave = async () => {
    if (!file || file.is_directory) return;

    try {
      setSaving(true);
      await invoke('write_file', { fileId: file.id, content });
      setOriginalContent(content);
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败：' + error);
    } finally {
      setSaving(false);
    }
  };

  const editorDidMount = (editor: any, monaco: any) => {
    editor.focus();

    // 添加编辑器快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });

    // 设置自动保存
    let autoSaveTimer: NodeJS.Timeout | null = null;
    editor.onDidChangeModelContent(() => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }

      autoSaveTimer = setTimeout(() => {
        if (content !== originalContent) {
          handleSave();
        }
      }, 5000); // 5秒后自动保存
    });

    // 定义 Dracula 主题
    monaco.editor.defineTheme('dracula', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        // 注释
        { token: 'comment', foreground: '6272A4', fontStyle: 'italic' },
        { token: 'comment.line', foreground: '6272A4', fontStyle: 'italic' },
        { token: 'comment.block', foreground: '6272A4', fontStyle: 'italic' },

        // 关键字
        { token: 'keyword', foreground: 'FF79C6' },
        { token: 'keyword.control', foreground: 'FF79C6' },
        { token: 'keyword.operator', foreground: 'FF79C6' },
        { token: 'keyword.other', foreground: 'FF79C6' },

        // 字符串
        { token: 'string', foreground: 'F1FA8C' },
        { token: 'string.quoted', foreground: 'F1FA8C' },
        { token: 'string.template', foreground: 'F1FA8C' },

        // 数字
        { token: 'number', foreground: 'BD93F9' },
        { token: 'constant.numeric', foreground: 'BD93F9' },

        // 正则表达式
        { token: 'regexp', foreground: 'F1FA8C' },

        // 类型和类
        { token: 'type', foreground: '8BE9FD' },
        { token: 'class', foreground: '8BE9FD' },
        { token: 'entity.name.class', foreground: '8BE9FD' },
        { token: 'entity.name.type', foreground: '8BE9FD' },

        // 函数
        { token: 'function', foreground: '50FA7B' },
        { token: 'entity.name.function', foreground: '50FA7B' },
        { token: 'support.function', foreground: '50FA7B' },

        // 变量和标识符
        { token: 'variable', foreground: 'F8F8F2' },
        { token: 'identifier', foreground: 'F8F8F2' },
        { token: 'variable.parameter', foreground: 'FFB86C' },

        // 常量
        { token: 'constant', foreground: 'BD93F9' },
        { token: 'constant.language', foreground: 'BD93F9' },
        { token: 'constant.character', foreground: 'BD93F9' },

        // 操作符
        { token: 'operator', foreground: 'FF79C6' },
        { token: 'delimiter', foreground: 'F8F8F2' },

        // HTML/XML 标签
        { token: 'tag', foreground: 'FF79C6' },
        { token: 'metatag', foreground: 'FF79C6' },
        { token: 'attribute.name', foreground: '50FA7B' },
        { token: 'attribute.value', foreground: 'F1FA8C' },

        // JSON
        { token: 'key', foreground: '8BE9FD' },
        { token: 'value', foreground: 'F1FA8C' },

        // CSS
        { token: 'property', foreground: '8BE9FD' },
        { token: 'property.value', foreground: 'F1FA8C' },

        // 其他
        { token: 'punctuation', foreground: 'F8F8F2' },
        { token: 'meta', foreground: '6272A4' },
      ],
      colors: {
        'editor.background': '#282A36',
        'editor.foreground': '#F8F8F2',
        'editor.lineHighlightBackground': '#44475A',
        'editor.selectionBackground': '#44475A',
        'editor.inactiveSelectionBackground': '#44475A75',
        'editorCursor.foreground': '#F8F8F2',
        'editorLineNumber.foreground': '#6272A4',
        'editorLineNumber.activeForeground': '#F8F8F2',
        'editor.selectionHighlightBackground': '#44475A50',
        'editor.wordHighlightBackground': '#44475A75',
        'editor.wordHighlightStrongBackground': '#44475A',
        'editorBracketMatch.background': '#44475A',
        'editorBracketMatch.border': '#50FA7B',
        'editorGutter.background': '#282A36',
        'editorGutter.modifiedBackground': '#8BE9FD',
        'editorGutter.addedBackground': '#50FA7B',
        'editorGutter.deletedBackground': '#FF5555',
        'editorError.foreground': '#FF5555',
        'editorWarning.foreground': '#FFB86C',
        'editorInfo.foreground': '#8BE9FD',
        'editorHint.foreground': '#6272A4',
        'editorIndentGuide.background': '#44475A',
        'editorIndentGuide.activeBackground': '#6272A4',
        'editorWhitespace.foreground': '#44475A',
      }
    });

    // 定义自定义主题
    monaco.editor.defineTheme('vsCodeDark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'regexp', foreground: 'D16969' },
        { token: 'type', foreground: '4EC9B0' },
        { token: 'class', foreground: '4EC9B0' },
        { token: 'function', foreground: 'DCDCAA' },
        { token: 'variable', foreground: '9CDCFE' },
        { token: 'constant', foreground: '4FC1FF' },
        { token: 'operator', foreground: 'D4D4D4' },
        { token: 'delimiter', foreground: 'D4D4D4' },
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editor.lineHighlightBackground': '#2a2a2a',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41',
        'editorCursor.foreground': '#d4d4d4',
        'editorLineNumber.foreground': '#858585',
        'editorLineNumber.activeForeground': '#c6c6c6',
        'editor.selectionHighlightBackground': '#add6ff26',
        'editor.wordHighlightBackground': '#575757b8',
        'editor.wordHighlightStrongBackground': '#004972b8',
        'editorBracketMatch.background': '#0064001a',
        'editorBracketMatch.border': '#888888',
        'editorGutter.background': '#1e1e1e',
        'editorGutter.modifiedBackground': '#1b81a8',
        'editorGutter.addedBackground': '#487e02',
        'editorGutter.deletedBackground': '#f85149',
        'editorError.foreground': '#f85149',
        'editorWarning.foreground': '#ff8c00',
        'editorInfo.foreground': '#3794ff',
        'editorHint.foreground': '#eeeeeeb3',
      }
    });

    monaco.editor.defineTheme('vsCodeLight', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '008000', fontStyle: 'italic' },
        { token: 'keyword', foreground: '0000FF' },
        { token: 'string', foreground: 'A31515' },
        { token: 'number', foreground: '098658' },
        { token: 'regexp', foreground: '811F3F' },
        { token: 'type', foreground: '267F99' },
        { token: 'class', foreground: '267F99' },
        { token: 'function', foreground: '795E26' },
        { token: 'variable', foreground: '001080' },
        { token: 'constant', foreground: '0070C1' },
        { token: 'operator', foreground: '000000' },
        { token: 'delimiter', foreground: '000000' },
      ],
      colors: {
        'editor.background': '#ffffff',
        'editor.foreground': '#000000',
        'editor.lineHighlightBackground': '#f0f0f0',
        'editor.selectionBackground': '#add6ff',
        'editor.inactiveSelectionBackground': '#e5ebf1',
        'editorCursor.foreground': '#000000',
        'editorLineNumber.foreground': '#237893',
        'editorLineNumber.activeForeground': '#0b216f',
        'editor.selectionHighlightBackground': '#add6ff80',
        'editor.wordHighlightBackground': '#57575740',
        'editor.wordHighlightStrongBackground': '#0064004d',
        'editorBracketMatch.background': '#0064001a',
        'editorBracketMatch.border': '#b9b9b9',
        'editorGutter.background': '#ffffff',
        'editorGutter.modifiedBackground': '#1b81a8',
        'editorGutter.addedBackground': '#487e02',
        'editorGutter.deletedBackground': '#f85149',
        'editorError.foreground': '#e51400',
        'editorWarning.foreground': '#bf8803',
        'editorInfo.foreground': '#1a85ff',
        'editorHint.foreground': '#6c6c6c',
      }
    });

    // 设置编辑器主题为 Dracula
    monaco.editor.setTheme('dracula');

    // 确保语法高亮正常工作
    const model = editor.getModel();
    if (model) {
      monaco.editor.setModelLanguage(model, language);
      console.log('Language set to:', language);
      console.log('Theme set to: dracula');
    }

    // 强制刷新编辑器
    setTimeout(() => {
      editor.trigger('source', 'editor.action.formatDocument');
    }, 100);
  };

  const handleEditorChange = (value: string) => {
    setContent(value);
  };

  return (
    <div style={{ height: '100%', width: '100%', background: '#282A36' }}>
      {loading ? (
        <div style={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: '#282A36',
          color: '#F8F8F2'
        }}>
          <Spin tip="加载中..." />
        </div>
      ) : file ? (
        file.is_directory ? (
          <div style={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: '#282A36',
            color: '#F8F8F2'
          }}>
            <Empty description="无法编辑文件夹" />
          </div>
        ) : (
          <MonacoEditor
            width="100%"
            height="100%"
            language={language}
            value={content}
            options={{
              selectOnLineNumbers: true,
              roundedSelection: false,
              readOnly: false,
              cursorStyle: 'line',
              automaticLayout: true,
              fontSize: fontSize,
              minimap: { enabled: true },
              scrollBeyondLastLine: false,
              wordWrap: 'on',
              lineNumbers: 'on',
              folding: true,
              foldingStrategy: 'auto',
              showFoldingControls: 'always',
              renderLineHighlight: 'all',
              parameterHints: { enabled: true },
              suggestOnTriggerCharacters: true,
              acceptSuggestionOnEnter: 'on',
              tabCompletion: 'on',
              snippetSuggestions: 'inline',
              formatOnPaste: true,
              formatOnType: true,
              autoIndent: 'full',
              autoClosingBrackets: 'always',
              autoClosingQuotes: 'always',
              autoSurround: 'languageDefined',
              bracketPairColorization: { enabled: true },
              guides: { bracketPairs: true, indentation: true },
              renderWhitespace: 'selection',
              renderControlCharacters: true,
              rulers: [],
              colorDecorators: true,
              codeActionsOnSaveTimeout: 1000,
              quickSuggestions: { other: true, comments: true, strings: true },
              quickSuggestionsDelay: 100,
              contextmenu: true,
              mouseWheelZoom: true,
              multiCursorModifier: 'alt',
              accessibilitySupport: 'auto',
              copyWithSyntaxHighlighting: true,
              emptySelectionClipboard: true,
              dragAndDrop: true,
              links: true,
              hover: { enabled: true, delay: 300 },
            }}
            onChange={handleEditorChange}
            editorDidMount={editorDidMount}
          />
        )
      ) : (
        <div style={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: '#282A36',
          color: '#F8F8F2'
        }}>
          <Empty description="请先选择一个文件" />
        </div>
      )}
    </div>
  );
};

export default FileEditor;
