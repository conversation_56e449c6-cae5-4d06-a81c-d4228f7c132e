/* Monaco Editor 自定义样式 */

/* 修复编辑器容器样式，防止边框闪烁 */
.monaco-editor {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 修复编辑器背景 */
.monaco-editor .monaco-editor-background {
  background-color: #282A36 !important;
}

/* 修复滚动条样式，防止闪烁 */
.monaco-editor .monaco-scrollable-element > .scrollbar {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar > .slider {
  background: rgba(68, 71, 90, 0.5) !important;
  border-radius: 3px !important;
  border: none !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar > .slider:hover {
  background: rgba(68, 71, 90, 0.7) !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar > .slider.active {
  background: rgba(68, 71, 90, 0.9) !important;
}

/* 修复小地图样式 */
.monaco-editor .minimap {
  background: #282A36 !important;
  border: none !important;
}

.monaco-editor .minimap-slider {
  background: rgba(68, 71, 90, 0.5) !important;
  border: none !important;
}

.monaco-editor .minimap-slider:hover {
  background: rgba(68, 71, 90, 0.7) !important;
}

/* 修复概览标尺样式 */
.monaco-editor .decorationsOverviewRuler {
  background: #282A36 !important;
  border: none !important;
}

/* 修复行号区域 */
.monaco-editor .margin {
  background-color: #282A36 !important;
  border: none !important;
}

/* 修复代码折叠区域 */
.monaco-editor .folding-decoration {
  background: transparent !important;
  border: none !important;
}

/* 修复建议窗口样式 */
.monaco-editor .suggest-widget {
  background: #282A36 !important;
  border: 1px solid #44475A !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row {
  background: transparent !important;
  color: #F8F8F2 !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row.focused {
  background: #44475A !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row:hover {
  background: #44475A !important;
}

/* 修复悬停提示样式 */
.monaco-editor .monaco-hover {
  background: #282A36 !important;
  border: 1px solid #44475A !important;
  color: #F8F8F2 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

/* 修复查找窗口样式 */
.monaco-editor .find-widget {
  background: #282A36 !important;
  border: 1px solid #44475A !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.monaco-editor .find-widget input {
  background: #44475A !important;
  color: #F8F8F2 !important;
  border: 1px solid #6272A4 !important;
}

/* 修复参数提示样式 */
.monaco-editor .parameter-hints-widget {
  background: #282A36 !important;
  border: 1px solid #44475A !important;
  color: #F8F8F2 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

/* 修复代码镜头样式 */
.monaco-editor .codelens-decoration {
  color: #6272A4 !important;
}

.monaco-editor .codelens-decoration:hover {
  color: #8BE9FD !important;
}

/* 修复内联建议样式 */
.monaco-editor .ghost-text-decoration {
  color: #6272A4 !important;
  font-style: italic !important;
}

/* 修复粘性滚动样式 */
.monaco-editor .sticky-line-root {
  background: #282A36 !important;
  border-bottom: 1px solid #44475A !important;
}

/* 修复括号对指南 */
.monaco-editor .bracket-match {
  background: rgba(80, 250, 123, 0.2) !important;
  border: 1px solid #50FA7B !important;
}

/* 修复缩进指南 */
.monaco-editor .lines-content .cigr {
  background: #44475A !important;
}

.monaco-editor .lines-content .cigra {
  background: #6272A4 !important;
}

/* 修复选择高亮 */
.monaco-editor .selected-text {
  background: rgba(68, 71, 90, 0.5) !important;
}

/* 修复单词高亮 */
.monaco-editor .wordHighlight {
  background: rgba(68, 71, 90, 0.7) !important;
  border: 1px solid rgba(68, 71, 90, 0.9) !important;
}

.monaco-editor .wordHighlightStrong {
  background: rgba(68, 71, 90, 0.9) !important;
  border: 1px solid #44475A !important;
}

/* 修复错误和警告装饰 */
.monaco-editor .squiggly-error {
  border-bottom: 2px solid #FF5555 !important;
}

.monaco-editor .squiggly-warning {
  border-bottom: 2px solid #FFB86C !important;
}

.monaco-editor .squiggly-info {
  border-bottom: 2px solid #8BE9FD !important;
}

.monaco-editor .squiggly-hint {
  border-bottom: 2px solid #6272A4 !important;
}

/* 修复光标样式 */
.monaco-editor .cursor {
  background: #F8F8F2 !important;
  border-color: #F8F8F2 !important;
}

/* 修复行高亮 */
.monaco-editor .current-line {
  background: #44475A !important;
  border: none !important;
}

/* 修复编辑器焦点样式 */
.monaco-editor.focused {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 防止编辑器容器闪烁 */
.monaco-editor-container {
  position: relative !important;
  overflow: hidden !important;
  background: #282A36 !important;
}

/* 修复编辑器装载时的闪烁 */
.monaco-editor .monaco-editor-background,
.monaco-editor .inputarea.ime-input {
  background: #282A36 !important;
}

/* 确保编辑器完全填充容器 */
.monaco-editor {
  width: 100% !important;
  height: 100% !important;
}
