import React, { useState, useEffect, useRef } from 'react';
import { Layout, Button, Tooltip, message, Spin, Divider, Form } from 'antd';
import ResizableDivider from '../common/ResizableDivider';
import {
  FolderOpenOutlined,
  ScanOutlined,
  UpOutlined,
  DownOutlined,
  ReloadOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  CodeOutlined,
  DesktopOutlined
} from '@ant-design/icons';
import ProjectList from './ProjectList';
import FileExplorer from './FileExplorer';
import FileEditor from './FileEditor';
import VSCodeTerminal from '../terminal/VSCodeTerminal';
import { Project, ProjectFile } from '../../types';
import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-dialog';
import './ProjectManagement.css';
import '../../styles/resizable-divider.css';

const { Content, Sider } = Layout;
import LocalTerminal from '../terminal/LocalTerminal'; // <-- 新增导入
import { Resizable } from 're-resizable'; // <-- 新增导入，用于调整大小
import '../../styles/resizable-divider.css'; // <-- 新增导入样式

const ProjectManagement: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [terminalHeight, setTerminalHeight] = useState(300); // <-- 新增状态管理终端高度
  const containerRef = useRef<HTMLDivElement>(null); // <-- 新增 Ref 获取容器高度
  const [selectedFile, setSelectedFile] = useState<ProjectFile | null>(null);
  const [openFiles, setOpenFiles] = useState<ProjectFile[]>([]);
  const [activeFileIndex, setActiveFileIndex] = useState<number>(-1);
  const [scanning, setScanning] = useState<boolean>(false);
  const [projectFiles, setProjectFiles] = useState<ProjectFile[]>([]);
  const [loadingFiles, setLoadingFiles] = useState<boolean>(false);

  // 折叠状态
  const [explorerCollapsed, setExplorerCollapsed] = useState<boolean>(false);
  const [projectListCollapsed, setProjectListCollapsed] = useState<boolean>(false);
  const [fileListCollapsed, setFileListCollapsed] = useState<boolean>(false);
  const [terminalCollapsed, setTerminalCollapsed] = useState<boolean>(false);
  const [editorCollapsed, setEditorCollapsed] = useState<boolean>(false);

  // 可调整大小的区域
  const [projectListHeight, setProjectListHeight] = useState<number>(200);
  const [editorTerminalRatio, setEditorTerminalRatio] = useState<number>(0.7); // 编辑器占70%，终端占30%

  // 选择项目后自动加载文件列表
  const handleSelectProject = async (project: Project) => {
    setSelectedProject(project);
    setSelectedFile(null);

    // 自动加载项目文件
    await loadProjectFiles(project.id);
  };

  // 加载项目文件列表
  const loadProjectFiles = async (projectId: string) => {
    try {
      setLoadingFiles(true);
      const files = await invoke('get_project_files', { projectId }) as ProjectFile[];
      setProjectFiles(files);
    } catch (error) {
      message.error('加载项目文件失败：' + error);
    } finally {
      setLoadingFiles(false);
    }
  };

  const handleSelectFile = (file: ProjectFile) => {
    setSelectedFile(file);

    // 检查文件是否已经打开
    const existingIndex = openFiles.findIndex(f => f.id === file.id);

    if (existingIndex >= 0) {
      // 如果文件已经打开，切换到该文件
      setActiveFileIndex(existingIndex);
    } else {
      // 如果文件未打开，添加到打开文件列表
      setOpenFiles(prev => [...prev, file]);
      setActiveFileIndex(openFiles.length);
    }
  };

  // 关闭文件标签
  const handleCloseFile = (index: number, e: React.MouseEvent) => {
    e.stopPropagation();

    const newOpenFiles = [...openFiles];
    newOpenFiles.splice(index, 1);
    setOpenFiles(newOpenFiles);

    // 如果关闭的是当前活动文件，切换到上一个文件
    if (index === activeFileIndex) {
      if (newOpenFiles.length > 0) {
        const newIndex = index === 0 ? 0 : index - 1;
        setActiveFileIndex(newIndex);
        setSelectedFile(newOpenFiles[newIndex]);
      } else {
        setActiveFileIndex(-1);
        setSelectedFile(null);
      }
    } else if (index < activeFileIndex) {
      // 如果关闭的文件在当前活动文件之前，需要调整活动文件索引
      setActiveFileIndex(activeFileIndex - 1);
    }
  };

  // 切换项目列表的展开/收起状态
  const toggleProjectList = () => {
    console.log('Toggling project list, current state:', projectListCollapsed);
    setProjectListCollapsed(prev => !prev);
  };

  // 扫描项目文件
  const handleScanProject = async () => {
    if (!selectedProject) {
      message.warning('请先选择一个项目');
      return;
    }

    try {
      setScanning(true);
      message.loading('正在扫描项目文件...', 0);

      // 调用后端扫描项目文件
      await invoke('scan_project_files', { projectId: selectedProject.id });

      message.destroy();
      message.success('项目文件扫描完成');

      // 扫描完成后自动加载文件列表
      await loadProjectFiles(selectedProject.id);
    } catch (error) {
      message.destroy();
      message.error('扫描项目文件失败：' + error);
    } finally {
      setScanning(false);
    }
  };

  // 新建项目并扫描目录
  const handleCreateProjectWithScan = async () => {
    try {
      // 打开文件夹选择对话框
      const selected = await open({
        directory: true,
        multiple: false,
        title: '选择项目目录'
      });

      if (selected === null || selected === undefined) {
        return; // 用户取消了选择
      }

      // 在Tauri 2.x中，selected可能是一个数组或单个路径
      const projectPath = Array.isArray(selected) ? selected[0] : selected as string;
      const pathParts = projectPath.split(/[/\\]/);
      const projectName = pathParts[pathParts.length - 1];

      // 创建项目
      const newProject = await invoke('create_new_project', {
        name: projectName,
        description: `项目路径: ${projectPath}`,
        leader: '',
        tags: 'scanned'
      }) as Project;

      message.success('项目创建成功');
      setSelectedProject(newProject);

      // 扫描项目文件
      setScanning(true);
      message.loading('正在扫描项目文件...', 0);
      await invoke('scan_project_directory', {
        projectId: newProject.id,
        directoryPath: projectPath
      });

      message.destroy();
      message.success('项目文件扫描完成');

      // 扫描完成后自动加载文件列表
      await loadProjectFiles(newProject.id);
    } catch (error) {
      message.destroy();
      message.error('创建项目失败：' + error);
    } finally {
      setScanning(false);
    }
  };

  return (
    <Layout className="vs-code-layout site-layout" style={{ height: '100%', overflow: 'hidden', margin: 0, padding: 0 }}>
      {/* VS Code 风格的布局 */}
      <Layout style={{ display: 'flex', flexDirection: 'row', height: '100%', margin: 0, padding: 0 }}>
        {/* 左侧边栏 - 项目和文件浏览器 */}
        <Sider
          width={250}
          collapsible
          collapsed={explorerCollapsed}
          trigger={null}
          collapsedWidth={40}
          style={{
            background: '#f5f5f5',
            borderRight: '1px solid #e8e8e8',
            height: '100%',
            overflow: 'hidden'
          }}
        >
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            overflow: 'hidden',
            visibility: explorerCollapsed ? 'visible' : 'visible'
          }}>
            {/* 侧边栏头部 - 折叠按钮 */}
            <div style={{
              display: 'flex',
              justifyContent: explorerCollapsed ? 'center' : 'flex-end',
              padding: '4px',
              borderBottom: explorerCollapsed ? 'none' : '1px solid #e8e8e8',
              height: '40px',
              alignItems: 'center'
            }}>
              <Tooltip title={explorerCollapsed ? "展开资源管理器" : "折叠资源管理器"}>
                <Button
                  type="text"
                  icon={explorerCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                  onClick={() => setExplorerCollapsed(!explorerCollapsed)}
                  size="small"
                  className="explorer-toggle-btn"
                />
              </Tooltip>
            </div>

            {/* 项目列表区域 - 可折叠 */}
            <div style={{
              display: explorerCollapsed ? 'none' : 'flex',
              flexDirection: 'column',
              borderBottom: '1px solid #e8e8e8',
              background: '#f5f5f5',
              overflow: 'visible',
              minHeight: '40px'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px',
                background: '#e8e8e8',
                cursor: 'pointer'
              }}
              onClick={toggleProjectList}>
                <div style={{ fontWeight: 'bold', fontSize: '14px' }}>项目列表</div>
                <div>
                  <Tooltip title="折叠/展开项目列表">
                    <Button
                      type="text"
                      icon={projectListCollapsed ? <DownOutlined /> : <UpOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleProjectList();
                      }}
                      size="small"
                    />
                  </Tooltip>
                  <Tooltip title="新建项目并扫描">
                    <Button
                      type="text"
                      icon={<FolderOpenOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCreateProjectWithScan();
                      }}
                      loading={scanning}
                      size="small"
                    />
                  </Tooltip>
                  {selectedProject && (
                    <Tooltip title="扫描项目文件">
                      <Button
                        type="text"
                        icon={<ScanOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleScanProject();
                        }}
                        loading={scanning}
                        size="small"
                      />
                    </Tooltip>
                  )}
                </div>
              </div>

              {/* 项目列表内容 - 可折叠，可调整高度 */}
              <div style={{
                height: projectListCollapsed ? '0' : `${projectListHeight}px`, // 可调整高度
                maxHeight: `${projectListHeight}px`, // 最大高度限制
                overflow: 'auto',
                transition: projectListCollapsed ? 'height 0.3s ease-in-out' : 'none',
                opacity: projectListCollapsed ? '0' : '1',
                visibility: projectListCollapsed ? 'hidden' : 'visible'
              }}>
                <ProjectList
                  onSelectProject={handleSelectProject}
                  selectedProject={selectedProject}
                />
              </div>
            </div>

            {/* 项目列表和文件浏览器之间的可调整大小的分隔线 */}
            <div style={{ padding: '0' }}>
              <ResizableDivider
                direction="horizontal"
                defaultSize={projectListHeight}
                minSize={100}
                maxSize={400}
                onResize={(size) => {
                  setProjectListHeight(size);
                }}
                vsCodeStyle={true}
                style={{ width: '100%' }}
              />
            </div>

            {/* 文件浏览器区域 - 可折叠，占用剩余空间 */}
            <div style={{
              flex: '1',
              display: explorerCollapsed ? 'none' : 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              minHeight: '40px',
              height: `calc(100% - ${projectListHeight + 40}px)` // 减去项目列表区域的高度和分隔线
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px',
                background: '#e8e8e8',
                cursor: 'pointer'
              }}
              onClick={() => setFileListCollapsed(!fileListCollapsed)}>
                <div style={{ fontWeight: 'bold', fontSize: '14px' }}>文件浏览器</div>
                <div>
                  <Tooltip title="折叠/展开文件浏览器">
                    <Button
                      type="text"
                      icon={fileListCollapsed ? <DownOutlined /> : <UpOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        setFileListCollapsed(!fileListCollapsed);
                      }}
                      size="small"
                    />
                  </Tooltip>
                  <Tooltip title="刷新">
                    <Button
                      type="text"
                      icon={<ReloadOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        selectedProject && loadProjectFiles(selectedProject.id);
                      }}
                      size="small"
                    />
                  </Tooltip>
                </div>
              </div>

              {/* 文件浏览器内容 - 可折叠 */}
              <div style={{
                height: fileListCollapsed ? '0' : 'calc(100% - 40px)',
                overflow: 'auto',
                transition: 'height 0.3s ease-in-out',
                flex: fileListCollapsed ? '0' : '1',
                padding: fileListCollapsed ? '0' : '8px',
                opacity: fileListCollapsed ? '0' : '1',
                visibility: fileListCollapsed ? 'hidden' : 'visible',
                display: 'flex',
                flexDirection: 'column'
              }}>
                {loadingFiles ? (
                  <div style={{ textAlign: 'center', padding: '20px 0' }}>
                    <Spin tip="加载中..." />
                  </div>
                ) : (
                  <FileExplorer
                    project={selectedProject}
                    onSelectFile={handleSelectFile}
                    files={projectFiles}
                  />
                )}
              </div>
            </div>
          </div>
        </Sider>

        {/* 文件浏览器和编辑器之间的可调整大小的分隔线 */}
        <ResizableDivider
          direction="vertical"
          defaultSize={250}
          minSize={150}
          maxSize={500}
          onResize={(size) => {
            // 调整侧边栏宽度
            const sider = document.querySelector('.ant-layout-sider') as HTMLElement;
            if (sider) {
              sider.style.width = `${size}px`;
              sider.style.minWidth = `${size}px`;
              sider.style.maxWidth = `${size}px`;
              sider.style.flex = `0 0 ${size}px`;
            }
          }}
          vsCodeStyle={true}
          style={{ height: '100%' }}
        />

        {/* 右侧内容区域 - 编辑器和终端 */}
        <Layout style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          background: '#fff'
        }}>
          {/* 编辑器外层容器 - 稳定的容器防止边框闪烁 */}
          <div style={{
            flex: editorCollapsed ? '0' : `${editorTerminalRatio}`,
            position: 'relative',
            minHeight: '40px',
            transition: 'flex 0.3s ease-in-out'
          }}>
            {/* 编辑器区域 - 可折叠 */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              borderBottom: '1px solid #e8e8e8'
            }}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                background: '#e8e8e8',
                borderBottom: '1px solid #e8e8e8'
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px',
                  cursor: 'pointer'
                }}
                onClick={() => setEditorCollapsed(!editorCollapsed)}>
                  <div style={{ fontWeight: 'bold', fontSize: '14px', display: 'flex', alignItems: 'center' }}>
                    <CodeOutlined style={{ marginRight: '8px' }} />
                    编辑器
                    <Tooltip title={editorCollapsed ? "展开编辑器" : "折叠编辑器"}>
                      <Button
                        type="text"
                        icon={editorCollapsed ? <DownOutlined /> : <UpOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditorCollapsed(!editorCollapsed);
                        }}
                        size="small"
                        style={{ marginLeft: '8px', border: 'none', background: 'transparent' }}
                      />
                    </Tooltip>
                  </div>
                  <div>
                    {/* 其他按钮可以放在这里 */}
                  </div>
                </div>

                {/* 文件标签栏 */}
                {!editorCollapsed && openFiles.length > 0 && (
                  <div style={{
                    display: 'flex',
                    overflowX: 'auto',
                    background: '#f5f5f5',
                    borderBottom: '1px solid #e8e8e8'
                  }}>
                    {openFiles.map((file, index) => (
                      <div
                        key={file.id}
                        style={{
                          padding: '6px 12px',
                          borderRight: '1px solid #e8e8e8',
                          background: index === activeFileIndex ? '#fff' : 'transparent',
                          borderBottom: index === activeFileIndex ? '2px solid #1890ff' : 'none',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          fontSize: '13px'
                        }}
                        onClick={() => {
                          setActiveFileIndex(index);
                          setSelectedFile(file);
                        }}
                      >
                        <span style={{ marginRight: '8px' }}>{file.name}</span>
                        <Button
                          type="text"
                          size="small"
                          style={{ padding: '0', minWidth: '16px', height: '16px', lineHeight: '16px' }}
                          onClick={(e) => handleCloseFile(index, e)}
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 编辑器内容 */}
              <div style={{
                flex: '1',
                overflow: editorCollapsed ? 'hidden' : 'auto',
                height: editorCollapsed ? '0' : 'auto',
                transition: 'height 0.3s ease-in-out',
                position: 'relative'
              }}>
                {activeFileIndex >= 0 && openFiles.length > 0 ? (
                  <FileEditor
                    file={openFiles[activeFileIndex]}
                  />
                ) : (
                  <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                    color: '#999',
                    flexDirection: 'column'
                  }}>
                    <CodeOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <p>请从文件浏览器中选择一个文件</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 编辑器和终端之间的可调整大小的分隔线 */}
          <div style={{ padding: '0' }}>
            <ResizableDivider
              direction="horizontal"
              defaultSize={Math.round(window.innerHeight * editorTerminalRatio)}
              minSize={100}
              maxSize={window.innerHeight - 100}
              onResize={(size) => {
                // 计算新的比例
                const newRatio = size / window.innerHeight;
                setEditorTerminalRatio(newRatio);
              }}
              vsCodeStyle={true}
              style={{ width: '100%' }}
            />
          </div>

          {/* 终端外层容器 - 稳定的容器防止边框闪烁 */}
          <div style={{
            flex: terminalCollapsed ? '0 0 40px' : `${1 - editorTerminalRatio}`,
            position: 'relative',
            minHeight: '40px',
            transition: 'flex 0.3s ease-in-out',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* 终端标题栏 - 始终可见 */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '8px',
              background: '#e8e8e8',
              borderBottom: '1px solid #e8e8e8',
              cursor: 'pointer',
              height: '40px',
              flexShrink: 0
            }}
            onClick={() => setTerminalCollapsed(!terminalCollapsed)}>
              <div style={{ fontWeight: 'bold', fontSize: '14px', display: 'flex', alignItems: 'center' }}>
                <DesktopOutlined style={{ marginRight: '8px' }} />
                终端
                <Tooltip title={terminalCollapsed ? "展开终端" : "折叠终端"}>
                  <Button
                    type="text"
                    icon={terminalCollapsed ? <DownOutlined /> : <UpOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      setTerminalCollapsed(!terminalCollapsed);
                    }}
                    size="small"
                    style={{ marginLeft: '8px', border: 'none', background: 'transparent' }}
                    className="terminal-toggle-btn"
                  />
                </Tooltip>
              </div>
              <div>
                {/* 其他按钮可以放在这里 */}
              </div>
            </div>

            {/* 终端内容 */}
            <div style={{
              flex: '1',
              overflow: 'hidden',
              height: terminalCollapsed ? '0' : 'calc(100% - 40px)',
              maxHeight: terminalCollapsed ? '0' : 'calc(100% - 40px)',
              transition: 'height 0.3s ease-in-out, max-height 0.3s ease-in-out',
              position: 'relative',
              visibility: terminalCollapsed ? 'hidden' : 'visible',
              opacity: terminalCollapsed ? 0 : 1
            }}>
              <div className="terminal-container">
                <VSCodeTerminal
                  projectId={selectedProject?.id}
                  projectPath={selectedProject?.description?.replace('项目路径: ', '')}
                />
              </div>
            </div>
          </div>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default ProjectManagement;
