.project-management-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.project-layout {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
}

.project-sidebar {
  display: flex;
  flex-direction: column;
  width: 300px;
  min-width: 200px;
  max-width: 400px;
  height: 100%;
  border-right: 1px solid #e0e0e0;
  background-color: #f5f5f5;
  overflow: hidden;
  resize: horizontal;
}

.project-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.project-list-container {
  flex: 1;
  overflow-y: auto;
  border-bottom: 1px solid #e0e0e0;
}

.file-list-container {
  flex: 2;
  overflow-y: auto;
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  background-color: #f0f0f0;
  border-bottom: 1px solid #ddd;
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

.monaco-editor-container {
  height: 100%;
  width: 100%;
}

.file-list-item {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.file-list-item:hover {
  background-color: #e6f7ff;
}

.file-list-item.selected {
  background-color: #e6f7ff;
}

.file-list-item-icon {
  margin-right: 5px;
}

.file-list-item-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.project-list-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #eee;
}

.project-list-item:hover {
  background-color: #f0f0f0;
}

.project-list-item.selected {
  background-color: #e6f7ff;
}

.project-list-item-icon {
  margin-right: 8px;
  font-size: 16px;
}

.project-list-item-name {
  flex: 1;
  font-weight: 500;
}

.project-list-item-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.project-list-item:hover .project-list-item-actions {
  opacity: 1;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.project-header-title {
  font-weight: bold;
  font-size: 16px;
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.file-header-title {
  font-weight: bold;
  font-size: 16px;
}

.resizable-handle {
  width: 5px;
  height: 100%;
  background-color: #e0e0e0;
  cursor: col-resize;
}

.collapse-button {
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: none;
  border-radius: 3px;
}

.collapse-button:hover {
  background-color: #e0e0e0;
}

.terminal-container {
  height: 100%;
  width: 100%;
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', 'Courier New', monospace;
  border-radius: 4px;
  overflow: hidden;
}

/* 终端折叠按钮样式 */
.terminal-toggle-btn {
  z-index: 100;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 4px !important;
}

.terminal-toggle-btn:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  background-color: #252526;
  border-bottom: 1px solid #333;
  color: #cccccc;
}
