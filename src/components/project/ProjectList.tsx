import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-dialog';
import {
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Layout,
  Tag,
  Tooltip,
  Popconfirm,
  Typography,
  Row,
  Col,
  Card,
  Divider,
  List,
  Dropdown,
  Spin,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FolderOutlined,
  MoreOutlined,
  FolderOpenOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { Project, ProjectStatus } from '../../types';

const { Title } = Typography;

interface ProjectListProps {
  onSelectProject: (project: Project) => void;
  selectedProject?: Project | null;
}

const ProjectList: React.FC<ProjectListProps> = ({ onSelectProject, selectedProject }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);
  const [directoryPath, setDirectoryPath] = useState<string>('');
  const [selectingDirectory, setSelectingDirectory] = useState(false);
  const [form] = Form.useForm();

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const projectList = await invoke('get_projects') as Project[];

      // 使用Map根据项目ID去重
      const uniqueProjects = new Map<string, Project>();
      projectList.forEach(project => {
        uniqueProjects.set(project.id, project);
      });

      // 将Map转换回数组
      setProjects(Array.from(uniqueProjects.values()));
    } catch (error) {
      message.error('获取项目列表失败：' + error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const handleCreate = () => {
    setEditingProject(null);
    form.resetFields();
    setDirectoryPath('');
    setIsModalVisible(true);
  };

  // 选择目录
  const handleSelectDirectory = async () => {
    try {
      setSelectingDirectory(true);
      const selected = await open({
        directory: true,
        multiple: false,
        title: '选择项目目录'
      });

      if (selected && selected !== null) {
        // 在Tauri 2.x中，selected可能是一个数组或单个路径
        const path = Array.isArray(selected) ? selected[0] : selected as string;
        setDirectoryPath(path);

        // 从路径中提取项目名称
        const pathParts = path.split(/[/\\]/);
        const projectName = pathParts[pathParts.length - 1];

        // 设置表单字段
        form.setFieldsValue({
          name: projectName,
          description: `项目路径: ${path}`
        });
      }
    } catch (error) {
      message.error('选择目录失败：' + error);
    } finally {
      setSelectingDirectory(false);
    }
  };

  const handleEdit = (record: Project) => {
    setEditingProject(record);
    form.setFieldsValue({
      ...record,
      status: record.status,
    });
    setIsModalVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await invoke('delete_project_by_id', { id });
      message.success('删除成功');
      fetchProjects();
    } catch (error) {
      message.error('删除失败：' + error);
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingProject) {
        await invoke('update_project_info', {
          id: editingProject.id,
          name: values.name,
          description: values.description,
          status: values.status,
          leader: values.leader,
          tags: values.tags,
        });
        message.success('更新成功');
      } else {
        // 检查是否选择了目录
        if (!directoryPath && !editingProject) {
          message.warning('请选择项目目录');
          return;
        }

        // 创建新项目
        const newProject = await invoke('create_new_project', {
          name: values.name,
          description: values.description,
          leader: values.leader,
          tags: values.tags,
        }) as Project;

        message.success('项目创建成功');

        // 扫描项目文件
        if (directoryPath) {
          message.loading('正在扫描项目文件...', 0);
          try {
            console.log('开始扫描项目目录:', {
              projectId: newProject.id,
              directoryPath: directoryPath
            });

            // 检查目录路径是否存在
            try {
              const exists = await invoke('check_path_exists', {
                path: directoryPath
              }) as boolean;

              if (!exists) {
                message.destroy();
                message.error(`目录路径不存在: ${directoryPath}`);
                return;
              }
            } catch (checkError) {
              console.error('检查路径是否存在失败:', checkError);
            }

            // 使用Promise和async/await处理扫描过程
            const scanProject = async () => {
              try {
                console.log('开始扫描项目目录:', {
                  projectId: newProject.id,
                  directoryPath: directoryPath
                });

                // 更新项目描述，确保包含路径信息
                await invoke('update_project_info', {
                  id: newProject.id,
                  name: newProject.name,
                  description: `项目路径: ${directoryPath}`,
                  status: newProject.status,
                  leader: newProject.leader,
                  tags: newProject.tags,
                });

                // 执行扫描
                await invoke('scan_project_directory', {
                  projectId: newProject.id,
                  directoryPath: directoryPath
                });

                message.destroy();
                message.success('项目文件扫描完成');

                // 刷新项目列表
                fetchProjects();
              } catch (scanError) {
                console.error('扫描项目目录失败:', scanError);
                message.destroy();
                message.error(`扫描项目文件失败: ${scanError}`);
              }
            };

            // 延迟执行扫描，避免UI阻塞
            setTimeout(scanProject, 500);
          } catch (scanError) {
            console.error('扫描项目目录失败:', scanError);
            message.destroy();
            message.error(`扫描项目文件失败: ${scanError}`);
          }
        }
      }
      setIsModalVisible(false);
      fetchProjects();
    } catch (error) {
      message.error('操作失败：' + error);
    }
  };

  const handleSelectProject = (project: Project) => {
    onSelectProject(project);
  };

  const columns: ColumnsType<Project> = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <a onClick={() => handleSelectProject(record)}>{text}</a>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: ProjectStatus) => {
        let color = 'default';
        switch (status) {
          case 'Active':
            color = 'green';
            break;
          case 'Inactive':
            color = 'orange';
            break;
          case 'Completed':
            color = 'blue';
            break;
          case 'Archived':
            color = 'red';
            break;
        }
        return <Tag color={color}>{status}</Tag>;
      },
      filters: [
        { text: '活跃', value: 'Active' },
        { text: '非活跃', value: 'Inactive' },
        { text: '已完成', value: 'Completed' },
        { text: '已归档', value: 'Archived' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: '负责人',
      dataIndex: 'leader',
      key: 'leader',
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags: string) => {
        if (!tags) return null;
        const tagArray = tags.split(',').filter(tag => tag.trim() !== '');
        return (
          <>
            {tagArray.map(tag => (
              <Tag key={tag}>{tag.trim()}</Tag>
            ))}
          </>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString(),
      sorter: (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="查看文件">
            <Button
              type="text"
              icon={<FolderOutlined />}
              onClick={() => handleSelectProject(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个项目吗？"
            description="删除后将无法恢复，项目中的所有文件也将被删除。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // VS Code 风格的简化视图
  return (
    <>
      <div className="project-list-container">
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Spin tip="加载中..." />
          </div>
        ) : (
          <div>
            <div style={{ marginBottom: '10px', display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                type="text"
                icon={<PlusOutlined />}
                onClick={handleCreate}
                size="small"
              >
                新建项目
              </Button>
            </div>

            <List
              dataSource={projects}
              renderItem={project => (
                <List.Item
                  key={project.id}
                  onClick={() => onSelectProject(project)}
                  style={{
                    cursor: 'pointer',
                    padding: '8px 12px',
                    borderRadius: '4px',
                    backgroundColor: selectedProject?.id === project.id ? '#e6f7ff' : 'transparent',
                    borderLeft: selectedProject?.id === project.id ? '3px solid #1890ff' : '3px solid transparent',
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <FolderOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                    <div style={{ flex: 1 }}>
                      <div style={{ fontWeight: selectedProject?.id === project.id ? 'bold' : 'normal' }}>
                        {project.name}
                      </div>
                      {project.description && (
                        <div style={{ fontSize: '12px', color: '#888', marginTop: '2px' }}>
                          {project.description.length > 30 ? project.description.substring(0, 30) + '...' : project.description}
                        </div>
                      )}
                    </div>
                    <div onClick={e => e.stopPropagation()}>
                      <Dropdown
                        menu={{
                          items: [
                            {
                              key: 'edit',
                              label: '编辑',
                              icon: <EditOutlined />,
                              onClick: () => handleEdit(project)
                            },
                            {
                              key: 'delete',
                              label: '删除',
                              icon: <DeleteOutlined />,
                              danger: true,
                              onClick: () => handleDelete(project.id)
                            }
                          ]
                        }}
                        trigger={['click']}
                      >
                        <Button type="text" icon={<MoreOutlined />} size="small" />
                      </Dropdown>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </div>
        )}
      </div>

      <Modal
          title={editingProject ? '编辑项目' : '新建项目'}
          open={isModalVisible}
          onOk={handleModalOk}
          onCancel={() => setIsModalVisible(false)}
          width={800}
        >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ status: 'Active' }}
        >
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input />
          </Form.Item>

          {!editingProject && (
            <Form.Item
              label="项目目录"
              required
              tooltip="选择项目所在的目录，将自动扫描目录中的文件"
            >
              <Input.Group compact>
                <Input
                  style={{ width: 'calc(100% - 40px)' }}
                  placeholder="请选择项目目录"
                  value={directoryPath}
                  readOnly
                />
                <Tooltip title="浏览目录">
                  <Button
                    icon={<FolderOpenOutlined />}
                    onClick={handleSelectDirectory}
                    loading={selectingDirectory}
                  />
                </Tooltip>
              </Input.Group>
            </Form.Item>
          )}

          <Form.Item
            name="description"
            label="项目描述"
          >
            <Input.TextArea rows={3} />
          </Form.Item>

          {editingProject && (
            <Form.Item
              name="status"
              label="项目状态"
              rules={[{ required: true, message: '请选择项目状态' }]}
            >
              <Select>
                <Select.Option value="Active">活跃</Select.Option>
                <Select.Option value="Inactive">非活跃</Select.Option>
                <Select.Option value="Completed">已完成</Select.Option>
                <Select.Option value="Archived">已归档</Select.Option>
              </Select>
            </Form.Item>
          )}

          <Form.Item
            name="leader"
            label="项目负责人"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Input placeholder="使用逗号分隔多个标签" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ProjectList;
