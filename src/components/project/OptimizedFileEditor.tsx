import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { message, Spin, Empty, Button } from 'antd';
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons';
import { ProjectFile } from '../../types';
import * as monaco from 'monaco-editor';

interface OptimizedFileEditorProps {
  file: ProjectFile | null;
}

const OptimizedFileEditor: React.FC<OptimizedFileEditorProps> = React.memo(({ file }) => {
  const [content, setContent] = useState<string>('');
  const [originalContent, setOriginalContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [language, setLanguage] = useState<string>('plaintext');
  const [hasChanges, setHasChanges] = useState(false);
  
  const editorRef = useRef<HTMLDivElement>(null);
  const monacoEditorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
  const initTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 优化的编辑器配置
  const editorConfig = useMemo(() => ({
    fontSize: 14,
    lineHeight: 1.4,
    automaticLayout: true,
    minimap: { enabled: false }, // 禁用小地图提升性能
    scrollBeyondLastLine: false,
    wordWrap: 'on' as const,
    lineNumbers: 'on' as const,
    folding: true,
    renderLineHighlight: 'line' as const,
    selectOnLineNumbers: true,
    roundedSelection: false,
    readOnly: false,
    cursorStyle: 'line' as const,
    cursorBlinking: 'blink' as const,
    theme: 'vs-dark',
    // 性能优化配置
    scrollbar: {
      vertical: 'auto' as const,
      horizontal: 'auto' as const,
      useShadows: false,
      verticalScrollbarSize: 12,
      horizontalScrollbarSize: 12,
    },
    // 减少功能以提升性能
    hover: { enabled: true, delay: 500 },
    parameterHints: { enabled: false },
    suggestOnTriggerCharacters: false,
    quickSuggestions: false,
    codeLens: false,
    lightbulb: { enabled: 'off' as const },
    inlineSuggest: { enabled: false },
    bracketPairColorization: { enabled: false },
    guides: { 
      bracketPairs: false,
      indentation: true,
      bracketPairsHorizontal: false,
      highlightActiveIndentation: false
    },
    renderWhitespace: 'none' as const,
    renderControlCharacters: false,
    colorDecorators: false,
    contextmenu: true,
    find: {
      seedSearchStringFromSelection: 'always' as const,
      autoFindInSelection: 'never' as const
    }
  }), []);

  // 根据文件扩展名获取语言
  const getLanguageByExtension = useCallback((extension: string): string => {
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'h': 'c',
      'hpp': 'cpp',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'html': 'html',
      'htm': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
      'md': 'markdown',
      'markdown': 'markdown',
      'sql': 'sql',
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',
      'ps1': 'powershell',
      'bat': 'bat',
      'cmd': 'bat',
      'dockerfile': 'dockerfile',
      'gitignore': 'ignore',
      'log': 'log',
      'txt': 'plaintext'
    };
    return languageMap[extension] || 'plaintext';
  }, []);

  // 获取文件内容
  const fetchFileContent = useCallback(async () => {
    if (!file || file.is_directory) return;

    try {
      setLoading(true);
      setHasChanges(false);
      
      const fileContent = await invoke('read_file', { fileId: file.id }) as string;
      setContent(fileContent);
      setOriginalContent(fileContent);

      // 根据文件扩展名设置语言
      const extension = file.name.split('.').pop()?.toLowerCase() || '';
      const newLanguage = getLanguageByExtension(extension);
      setLanguage(newLanguage);

      // 如果编辑器已存在，更新内容和语言
      if (monacoEditorRef.current) {
        const model = monacoEditorRef.current.getModel();
        if (model) {
          model.setValue(fileContent);
          monaco.editor.setModelLanguage(model, newLanguage);
        }
      }
    } catch (error) {
      message.error('获取文件内容失败：' + error);
    } finally {
      setLoading(false);
    }
  }, [file, getLanguageByExtension]);

  // 保存文件
  const handleSave = useCallback(async () => {
    if (!file || !hasChanges) return;

    try {
      setSaving(true);
      await invoke('write_file', { 
        fileId: file.id, 
        content: content 
      });
      setOriginalContent(content);
      setHasChanges(false);
      message.success('文件保存成功');
    } catch (error) {
      message.error('保存文件失败：' + error);
    } finally {
      setSaving(false);
    }
  }, [file, content, hasChanges]);

  // 重新加载文件
  const handleReload = useCallback(() => {
    if (hasChanges) {
      if (window.confirm('文件已修改，确定要重新加载吗？未保存的更改将丢失。')) {
        fetchFileContent();
      }
    } else {
      fetchFileContent();
    }
  }, [hasChanges, fetchFileContent]);

  // 初始化编辑器
  const initializeEditor = useCallback(() => {
    if (!editorRef.current || monacoEditorRef.current) return;

    const editor = monaco.editor.create(editorRef.current, {
      value: content,
      language: language,
      ...editorConfig
    });

    monacoEditorRef.current = editor;

    // 监听内容变化
    editor.onDidChangeModelContent(() => {
      const newContent = editor.getValue();
      setContent(newContent);
      setHasChanges(newContent !== originalContent);
    });

    // 添加保存快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });

    // 添加基本的右键菜单功能
    editor.addAction({
      id: 'save-file',
      label: '💾 保存文件',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.0,
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS],
      run: () => handleSave()
    });

    editor.addAction({
      id: 'reload-file',
      label: '🔄 重新加载',
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.1,
      run: () => handleReload()
    });

    console.log('Optimized editor initialized');
  }, [content, language, editorConfig, originalContent, handleSave, handleReload]);

  // 文件变化时重新获取内容
  useEffect(() => {
    if (file) {
      fetchFileContent();
    } else {
      setContent('');
      setOriginalContent('');
      setHasChanges(false);
    }
  }, [file, fetchFileContent]);

  // 内容变化时初始化编辑器
  useEffect(() => {
    if (content !== undefined && editorRef.current && !loading) {
      // 清理之前的定时器
      if (initTimerRef.current) {
        clearTimeout(initTimerRef.current);
      }

      // 延迟初始化编辑器
      initTimerRef.current = setTimeout(() => {
        initializeEditor();
      }, 100);
    }

    return () => {
      if (initTimerRef.current) {
        clearTimeout(initTimerRef.current);
      }
    };
  }, [content, loading, initializeEditor]);

  // 清理编辑器
  useEffect(() => {
    return () => {
      if (initTimerRef.current) {
        clearTimeout(initTimerRef.current);
      }
      if (monacoEditorRef.current) {
        monacoEditorRef.current.dispose();
        monacoEditorRef.current = null;
      }
    };
  }, []);

  if (!file) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100%',
        background: '#1e1e1e',
        color: '#cccccc'
      }}>
        <Empty 
          description="请选择一个文件进行编辑" 
          style={{ color: '#cccccc' }}
        />
      </div>
    );
  }

  if (file.is_directory) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100%',
        background: '#1e1e1e',
        color: '#cccccc'
      }}>
        <Empty 
          description="无法编辑目录" 
          style={{ color: '#cccccc' }}
        />
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      height: '100%',
      background: '#1e1e1e'
    }}>
      {/* 工具栏 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '8px 12px',
        background: '#2d2d30',
        borderBottom: '1px solid #3e3e42',
        color: '#cccccc',
        fontSize: '13px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span>{file.name}</span>
          {hasChanges && <span style={{ color: '#FF9800' }}>●</span>}
          <span style={{ color: '#858585' }}>({language})</span>
        </div>
        
        <div style={{ display: 'flex', gap: '4px' }}>
          <Button
            type="text"
            size="small"
            icon={<ReloadOutlined />}
            onClick={handleReload}
            disabled={loading}
            style={{ color: '#cccccc', border: 'none' }}
            title="重新加载"
          />
          <Button
            type="text"
            size="small"
            icon={<SaveOutlined />}
            onClick={handleSave}
            disabled={!hasChanges || saving}
            loading={saving}
            style={{ 
              color: hasChanges ? '#4CAF50' : '#cccccc', 
              border: 'none' 
            }}
            title="保存文件 (Ctrl+S)"
          />
        </div>
      </div>

      {/* 编辑器区域 */}
      <div style={{ flex: 1, position: 'relative' }}>
        {loading ? (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            background: '#1e1e1e'
          }}>
            <Spin size="large" />
          </div>
        ) : (
          <div
            ref={editorRef}
            style={{
              width: '100%',
              height: '100%',
              background: '#1e1e1e'
            }}
          />
        )}
      </div>
    </div>
  );
});

OptimizedFileEditor.displayName = 'OptimizedFileEditor';

export default OptimizedFileEditor;
