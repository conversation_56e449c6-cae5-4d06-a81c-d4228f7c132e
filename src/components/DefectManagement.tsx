import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Layout,
  Tag,
  Row,
  Col,
  Transfer,
} from 'antd';
import type { TransferProps } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { TestCaseStatus, TestCasePriority } from '../types'; // Import types

import ModuleTree from './ModuleTree';

interface TestCase {
  id: string;
  name: string;
  module_id: string;
  priority: TestCasePriority; // 使用枚举类型
  status: TestCaseStatus;    // 使用枚举类型
  is_automated: boolean;
}

interface Defect {
  id: string;
  title: string;
  description?: string;
  severity: string;
  priority: string;
  status: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  assigned_to?: string;
  project_id: string;
  module_id?: string;
  steps_to_reproduce?: string;
  expected_result?: string;
  actual_result?: string;
  test_cases?: string[];
}

const DefectManagement: React.FC = () => {
  const [defects, setDefects] = useState<Defect[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingDefect, setEditingDefect] = useState<Defect | null>(null);
  const [form] = Form.useForm();
  const [selectedModuleId, setSelectedModuleId] = useState<string>('');
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [selectedTestCases, setSelectedTestCases] = useState<React.Key[]>([]);
  const [isTestCaseModalVisible, setIsTestCaseModalVisible] = useState(false);
  const [currentDefect, setCurrentDefect] = useState<Defect | null>(null);

  const fetchDefects = async () => {
    try {
      const defectList = await invoke('get_defects', { moduleId: selectedModuleId }) as Defect[];
      setDefects(defectList);
    } catch (error) {
      message.error('获取缺陷列表失败：' + error);
    }
  };

  const fetchTestCases = async () => {
    try {
      const caseList = await invoke('get_test_cases', { moduleId: selectedModuleId }) as TestCase[];
      setTestCases(caseList);
    } catch (error) {
      message.error('获取测试用例失败：' + error);
    }
  };

  const handleModuleSelect = (moduleId: string) => {
    setSelectedModuleId(moduleId);
  };

  const handleTestCaseSelect = async (defect: Defect) => {
    setCurrentDefect(defect);
    setSelectedTestCases(defect.test_cases?.map(id => id as React.Key) || []);
    await fetchTestCases();
    setIsTestCaseModalVisible(true);
  };

  const handleTestCaseModalOk = async () => {
    if (currentDefect) {
      try {
        await invoke('update_defect', {
          defect: {
            ...currentDefect,
            test_cases: selectedTestCases as string[],
            updated_at: new Date().toISOString()
          }
        });
        message.success('更新测试用例关联成功');
        setIsTestCaseModalVisible(false);
        fetchDefects();
      } catch (error) {
        message.error('更新测试用例关联失败：' + error);
      }
    }
  };

  useEffect(() => {
    if (selectedModuleId) {
      fetchDefects();
      fetchTestCases();
    }
  }, [selectedModuleId]);

  const handleCreate = () => {
    setEditingDefect(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record: Defect) => {
    setEditingDefect(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await invoke('delete_defect', { id });
      message.success('删除成功');
      fetchDefects();
    } catch (error) {
      message.error('删除失败：' + error);
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      if (editingDefect) {
        const newStatus = values.status;
        const oldStatus = editingDefect.status;
        const isValidTransition = await invoke('validate_defect_status_transition', {
          fromStatus: oldStatus,
          toStatus: newStatus
        }) as boolean;

        if (!isValidTransition) {
          message.error(`不允许从 ${oldStatus} 状态转换到 ${newStatus} 状态`);
          return;
        }

        await invoke('update_defect', {
          defect: {
            ...editingDefect,
            ...values,
            updated_at: new Date().toISOString()
          }
        });
        message.success('更新成功');
      } else {
        await invoke('create_defect', {
          defect: {
            ...values,
            id: crypto.randomUUID(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            created_by: 'System',
            status: 'New',
            module_id: selectedModuleId
          }
        });
        message.success('创建成功');
      }
      setIsModalVisible(false);
      fetchDefects();
    } catch (error) {
      message.error('操作失败：' + error);
    }
  };

  const columns: ColumnsType<Defect> = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity: string) => {
        const color = 
          severity === 'Critical' ? 'red' :
          severity === 'High' ? 'orange' :
          severity === 'Medium' ? 'blue' : 'green';
        return <Tag color={color}>{severity}</Tag>;
      }
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => {
        const color = 
          priority === 'High' ? 'red' :
          priority === 'Medium' ? 'orange' : 'green';
        return <Tag color={color}>{priority}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = 
          status === 'New' ? 'blue' :
          status === 'In Progress' ? 'orange' :
          status === 'Resolved' ? 'green' : 'red';
        return <Tag color={color}>{status}</Tag>;
      }
    },
    {
      title: '指派给',
      dataIndex: 'assigned_to',
      key: 'assigned_to',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
          <Button type="link" onClick={() => handleTestCaseSelect(record)}>关联用例</Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>删除</Button>
        </Space>
      ),
    },
  ];

  return (
    <Layout style={{ background: '#fff', minHeight: 'calc(100vh - 112px)' }}>
      <Row>
        <Col span={4} style={{ borderRight: '1px solid #f0f0f0' }}>
          <ModuleTree onSelect={handleModuleSelect} />
        </Col>
        <Col span={20} style={{ padding: '24px' }}>
          <div style={{ marginBottom: '16px' }}>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleCreate}
              disabled={!selectedModuleId}
            >
              新建缺陷
            </Button>
          </div>

          <Table
            columns={columns}
            dataSource={defects}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />

          <Modal
            title={editingDefect ? '编辑缺陷' : '新建缺陷'}
            open={isModalVisible}
            onOk={handleModalOk}
            onCancel={() => setIsModalVisible(false)}
            width={800}
          >
            <Form
              form={form}
              layout="vertical"
            >
              <Form.Item
                name="title"
                label="缺陷标题"
                rules={[{ required: true, message: '请输入缺陷标题' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="description"
                label="缺陷描述"
                rules={[{ required: true, message: '请输入缺陷描述' }]}
              >
                <Input.TextArea rows={3} />
              </Form.Item>

              <Form.Item
                name="severity"
                label="严重程度"
                initialValue="Medium"
              >
                <Select>
                  <Select.Option value="Critical">致命</Select.Option>
                  <Select.Option value="High">严重</Select.Option>
                  <Select.Option value="Medium">中等</Select.Option>
                  <Select.Option value="Low">轻微</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="priority"
                label="优先级"
                initialValue="Medium"
              >
                <Select>
                  <Select.Option value="High">高</Select.Option>
                  <Select.Option value="Medium">中</Select.Option>
                  <Select.Option value="Low">低</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="status"
                label="状态"
                initialValue="New"
              >
                <Select>
                  <Select.Option value="New">新建</Select.Option>
                  <Select.Option value="In Progress">处理中</Select.Option>
                  <Select.Option value="Resolved">已解决</Select.Option>
                  <Select.Option value="Closed">已关闭</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="assigned_to"
                label="指派给"
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="steps_to_reproduce"
                label="重现步骤"
              >
                <Input.TextArea rows={3} />
              </Form.Item>

              <Form.Item
                name="expected_result"
                label="预期结果"
              >
                <Input.TextArea rows={2} />
              </Form.Item>

              <Form.Item
                name="actual_result"
                label="实际结果"
              >
                <Input.TextArea rows={2} />
              </Form.Item>
            </Form>
          </Modal>

          <Modal
            title="关联测试用例"
            open={isTestCaseModalVisible}
            onOk={handleTestCaseModalOk}
            onCancel={() => setIsTestCaseModalVisible(false)}
            width={800}
          >
            <Transfer
              dataSource={testCases.map(item => ({
                key: item.id,
                title: item.name,
                description: `优先级: ${item.priority}, 状态: ${item.status}`
              }))}
              titles={['可选用例', '已选用例']}
              targetKeys={selectedTestCases}
              onChange={(targetKeys) => setSelectedTestCases(targetKeys as string[])} // Simplified onChange signature
              render={item => item.title}
              listStyle={{
                width: 300,
                height: 400,
              }}
            />
          </Modal>
        </Col>
      </Row>
    </Layout>
  );
};

export default DefectManagement;