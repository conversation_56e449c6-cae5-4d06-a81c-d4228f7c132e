import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-dialog';
import {
  Button,
  Table,
  Row,
  Col,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Layout,
  Switch,
  Tag
} from 'antd';
import ModuleTree from './ModuleTree';
import { UploadOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { TestCaseStatus, TestCasePriority } from '../types';

interface TestCase {
  id: string;
  name: string;
  description?: string;
  priority: TestCasePriority;
  status: TestCaseStatus;
  expected_result: string;
  actual_result?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  assigned_to?: string;
  tags?: string;
  module_id: string;
  preconditions?: string;
  test_steps?: string;
  is_automated: boolean;
}

const TestCaseManagement: React.FC = () => {
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTestCase, setEditingTestCase] = useState<TestCase | null>(null);
  const [selectedModuleId, setSelectedModuleId] = useState<string | null>(null);
  const [form] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const fetchTestCases = async () => {
    try {
      const cases = await invoke('get_test_cases', { moduleId: selectedModuleId });
      setTestCases(cases as TestCase[]);
    } catch (error) {
      message.error('获取测试用例失败：' + error);
    }
  };

  useEffect(() => {
    if (selectedModuleId) {
      fetchTestCases();
    }
  }, [selectedModuleId]);

  const handleImportExcel = async () => {
    try {
      const selected = await open({
        multiple: false,
        filters: [{
          name: 'Excel',
          extensions: ['xlsx', 'xls']
        }]
      });

      if (selected && selected !== null) {
        // 在Tauri 2.x中，selected可能是一个数组或单个路径
        const filePath = Array.isArray(selected) ? selected[0] : selected as string;

        await invoke('import_test_cases_from_excel', {
          filePath: filePath
        });
        message.success('导入成功');
        fetchTestCases();
      }
    } catch (error) {
      message.error('导入失败：' + error);
    }
  };

  const handleCreate = () => {
    setEditingTestCase(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record: TestCase) => {
    setEditingTestCase(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await invoke('delete_test_case', { id });
      message.success('删除成功');
      fetchTestCases();
    } catch (error) {
      message.error('删除失败：' + error);
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const payload = {
        ...values,
        actual_result: values.actual_result || null,
        is_automated: values.is_automated || false,
      };

      if (editingTestCase) {
        await invoke('update_test_case', {
          testCase: {
            ...editingTestCase,
            ...payload,
            updated_at: new Date().toISOString(),
          }
        });
        message.success('更新成功');
      } else {
        await invoke('create_test_case', {
          testCase: {
            ...payload,
            id: crypto.randomUUID(),
            module_id: selectedModuleId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            created_by: 'System',
            status: values.status || 'Not Started'
          }
        });
        message.success('创建成功');
      }
      setIsModalVisible(false);
      fetchTestCases();
    } catch (error) {
      message.error('操作失败：' + error);
    }
  };

  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的用例');
      return;
    }
    Modal.confirm({
      title: `确定要删除选中的 ${selectedRowKeys.length} 条用例吗？`,
      content: '此操作不可恢复。',
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await invoke('batch_delete_test_cases', { ids: selectedRowKeys });
          message.success('批量删除成功');
          fetchTestCases();
          setSelectedRowKeys([]);
        } catch (error) {
          message.error('批量删除失败：' + error);
        }
      },
    });
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const hasSelected = selectedRowKeys.length > 0;

  const columns: ColumnsType<TestCase> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: TestCasePriority) => {
        const color =
          priority === 'High' ? 'red' :
          priority === 'Medium' ? 'orange' : 'green';
        return <Tag color={color}>{priority}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: TestCaseStatus) => {
        const color =
          status === 'Passed' ? 'green' :
          status === 'Failed' ? 'red' :
          status === 'Blocked' ? 'grey' :
          status === 'InProgress' ? 'blue' : 'default'; // Corrected 'In Progress' to 'InProgress'
        return <Tag color={color}>{status}</Tag>;
      }
    },
    {
      title: '自动化',
      dataIndex: 'is_automated',
      key: 'is_automated',
      render: (isAutomated: boolean) => (
        <Tag color={isAutomated ? 'blue' : 'default'}>
          {isAutomated ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>删除</Button>
        </Space>
      ),
    },
  ];

  return (
    <Layout style={{ background: '#fff', minHeight: 'calc(100vh - 112px)' }}>
      <Row>
        <Col span={4} style={{ borderRight: '1px solid #f0f0f0' }}>
          <ModuleTree onSelect={(moduleId) => setSelectedModuleId(moduleId)} />
        </Col>
        <Col span={20} style={{ padding: '24px' }}>
          <div style={{ marginBottom: '16px' }}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreate}
                disabled={!selectedModuleId}
              >
                新建用例
              </Button>
              <Button
                icon={<UploadOutlined />}
                onClick={handleImportExcel}
                disabled={!selectedModuleId}
              >
                导入Excel
              </Button>
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={handleBatchDelete}
                disabled={!hasSelected}
              >
                批量删除
              </Button>
              <span style={{ marginLeft: 8 }}>
                {hasSelected ? `已选择 ${selectedRowKeys.length} 项` : ''}
              </span>
            </Space>
          </div>

          <Table
            rowSelection={rowSelection}
            columns={columns}
            dataSource={testCases}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />

          <Modal
            title={editingTestCase ? '编辑测试用例' : '新建测试用例'}
            open={isModalVisible}
            onOk={handleModalOk}
            onCancel={() => setIsModalVisible(false)}
            width={800}
          >
            <Form
              form={form}
              layout="vertical"
            >
              <Form.Item
                name="name"
                label="用例名称"
                rules={[{ required: true, message: '请输入用例名称' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="description"
                label="用例描述"
              >
                <Input.TextArea rows={3} />
              </Form.Item>

              <Form.Item
                name="priority"
                label="优先级"
                initialValue="Medium"
              >
                <Select>
                  <Select.Option value="High">高</Select.Option>
                  <Select.Option value="Medium">中</Select.Option>
                  <Select.Option value="Low">低</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="status"
                label="状态"
                initialValue="Not Started"
              >
                <Select>
                  <Select.Option value="Not Started">未开始</Select.Option>
                  <Select.Option value="In Progress">进行中</Select.Option>
                  <Select.Option value="Passed">通过</Select.Option>
                  <Select.Option value="Failed">失败</Select.Option>
                  <Select.Option value="Blocked">阻塞</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="preconditions"
                label="前置条件"
              >
                <Input.TextArea rows={2} />
              </Form.Item>

              <Form.Item
                name="test_steps"
                label="测试步骤"
                rules={[{ required: true, message: '请输入测试步骤' }]}
              >
                <Input.TextArea rows={4} />
              </Form.Item>

              <Form.Item
                name="expected_result"
                label="预期结果"
                rules={[{ required: true, message: '请输入预期结果' }]}
              >
                <Input.TextArea rows={2} />
              </Form.Item>

              <Form.Item
                name="actual_result"
                label="实际结果"
              >
                <Input.TextArea rows={2} />
              </Form.Item>

              <Form.Item
                name="assigned_to"
                label="指派给"
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="is_automated"
                label="是否自动化"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Form>
          </Modal>
        </Col>
      </Row>
    </Layout>
  );
};

export default TestCaseManagement;