import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Modal, Button, Tabs, Input, message, Tree, Spin, Tooltip, Select, Space, Tag } from 'antd';
import type { ModalProps } from 'antd';
import type { InputRef } from 'antd';
import { CloseOutlined, MinusOutlined, UploadOutlined, DownloadOutlined, ReloadOutlined, FolderOutlined, FileOutlined, HistoryOutlined, PlusOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import type { UnlistenFn } from '@tauri-apps/api/event';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import 'xterm/css/xterm.css';
import './SSHTerminal.css';

// 添加debounce函数定义
function debounce<F extends (...args: any[]) => any>(func: F, wait: number): (...args: Parameters<F>) => void {
  let timeout: ReturnType<typeof setTimeout> | undefined;
  return function(...args: Parameters<F>) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

interface SSHTerminalProps {
  open: boolean;
  agent: {
    id: string;
    name: string;
    ip: string;
    username: string;
    work_dir: string;
  } | null;
  onClose: () => void;
}

interface FileNode {
  title: string;
  key: string;
  isLeaf: boolean;
  children?: FileNode[];
}

interface TabItem {
  key: string;
  title: string;
  command: string;
  output: string;
  terminalId?: string; // 用于标识每个标签页对应的终端实例
}

/**
 * 初始化PTY会话
 * @param terminal 终端实例
 * @param agentId 执行机ID
 */
async function initPtySession(terminal: Terminal, agentId: string): Promise<void> {
  try {
    await invoke('init_pty', { agentId });
    console.log('[SSH Debug] PTY会话已初始化');
    terminal.writeln('\x1b[32mPTY会话已初始化\x1b[0m');
  } catch (error) {
    console.error('[SSH Debug] 初始化PTY会话失败:', error);
    terminal.writeln(`\r\n\x1b[31m初始化PTY会话失败: ${error}\x1b[0m\r\n`);
    throw error;
  }
}

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * SSH终端组件
 * 提供SSH连接、命令执行、文件浏览和传输功能
 */
const SSHTerminal: React.FC<SSHTerminalProps> = ({ open, agent, onClose }) => {
  // 设置Modal的属性
  const modalProps: ModalProps = {
    open,
    onCancel: onClose,
    width: '80%',
    style: { top: 20 },
    footer: null,
    destroyOnClose: true,
    maskClosable: false,
    keyboard: true,
    zIndex: 900, // 降低zIndex，确保不会遮挡执行机编辑窗口
    className: 'ssh-terminal-modal',
    wrapClassName: 'ssh-terminal-modal-wrap' // 添加包装类名以便自定义样式
  };
  const [activeKey, setActiveKey] = useState<string>('1');
  const [tabs, setTabs] = useState<TabItem[]>([]);
  const [command, setCommand] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [currentPath, setCurrentPath] = useState<string>('');
  const [inputPath, setInputPath] = useState<string>('');
  const [fileLoading, setFileLoading] = useState<boolean>(false);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(-1);
  const [rootDirectories, setRootDirectories] = useState<string[]>(['/home', '/tmp', '/usr', '/var']);
  const [selectedRootDir, setSelectedRootDir] = useState<string>('');

  const [connectionInitiated, setConnectionInitiated] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [connectionRetries, setConnectionRetries] = useState<number>(0);
  const [lastError, setLastError] = useState<string>('');
  // 使用正确的类型定义
  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout> | undefined>(undefined);

  // 命令输入框引用
  const commandInputRef = useRef<InputRef>(null);

  // 使用Map存储每个标签页的终端实例
  const terminalInstancesRef = useRef<Map<string, { terminal: Terminal | undefined, fitAddon: FitAddon }>>(new Map());
  const terminalRef = useRef<Terminal | undefined>(undefined);

  useEffect(() => {
    const terminalInstance = terminalInstancesRef.current.get('terminal-1');
    if (terminalInstance?.terminal) {
      terminalRef.current = terminalInstance.terminal;
    }
  }, []);

  // 清理函数定义
  const cleanup = useCallback(async () => {
    if (agent) {
      try {
        // 停止PTY会话
        await invoke('stop_pty', { agentId: agent.id });
        console.log(`[SSH Debug] PTY会话已停止`);
      } catch (error) {
        console.error('[SSH Debug] 停止PTY会话失败:', error);
      }
    }

    // 清理所有终端实例
    terminalInstancesRef.current.forEach(({ terminal }) => {
      try {
        if (terminal) {
          terminal.dispose();
        }
      } catch (error) {
        console.error('[SSH Debug] 清理终端实例失败:', error);
      }
    });
    terminalInstancesRef.current.clear();

    // 清理重连定时器
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = undefined;
    }

    // 重置状态
    setIsConnected(false);
    setConnectionInitiated(false);
    setLoading(false);
    setConnectionRetries(0);
    setLastError('');

    // 保存当前连接状态
    if (agent) {
      localStorage.setItem(`ssh_connection_${agent.id}`, JSON.stringify({
        lastConnected: new Date().toISOString(),
        retryCount: 0
      }));
    }
  }, [agent]);

  // 连接SSH
  const connectSSH = useCallback(async () => {
    if (!agent?.id) {
      console.error('[SSH Debug] 无法连接：未设置执行机信息');
      return false;
    }

    try {
      await invoke('connect_ssh', { agent });
      setIsConnected(true);
      return true;
    } catch (err) {
      console.error('[SSH Debug] SSH连接失败:', err);
      setIsConnected(false);
      return false;
    }
  }, [agent]);

  // 处理SSH连接错误
  const handleSSHError = useCallback(async (error: any, terminal: Terminal | undefined) => {
    const errorStr = String(error);
    console.error(`[SSH Debug] 连接失败:`, errorStr);

    const errorType =
      errorStr.includes('Failed getting banner') ? '无法获取SSH服务器信息' :
      errorStr.includes('timeout') || errorStr.includes('timed out') ? '连接超时' :
      errorStr.includes('Authentication') ? '认证失败' :
      errorStr.includes('Connection refused') ? '连接被拒绝' :
      errorStr.includes('Host key verification failed') ? '主机密钥验证失败' :
      errorStr.includes('keepalive') ? 'TCP保活失败' :
      errorStr.includes('Connection reset') ? '连接被重置' :
      errorStr.includes('Connection closed') ? '连接已关闭' :
      errorStr.includes('Network error') ? '网络错误' :
      errorStr.includes('Permission denied') ? '权限被拒绝' :
      '未知错误';

    setLastError(errorType);

    // 根据错误类型决定是否需要立即清理状态
    const needsImmediateCleanup = [
      '认证失败',
      '主机密钥验证失败',
      'TCP保活失败',
      '权限被拒绝'
    ].includes(errorType);

    if (needsImmediateCleanup) {
      await cleanup();
    }

    return { errorType, needsImmediateCleanup };
  }, [cleanup]);

  // 建立SSH连接 - 扩展版本
  const handleSSHConnection = useCallback(async () => {
    if (!agent) return false;

    if (isConnected) {
      console.log(`[SSH Debug] 已经存在连接，跳过连接过程`);
      return true;
    }

    console.log(`[SSH Debug] 开始连接到执行机 ${agent.name} (${agent.ip})...`);
    setLoading(true);
    setConnectionInitiated(true);

    const maxRetries = 3;
    const terminal = terminalInstancesRef.current.get('terminal-1')?.terminal;

    try {
      if (connectionRetries > 0) {
        console.log(`[SSH Debug] 第${connectionRetries}次重试连接...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // 先尝试断开可能存在的连接
      try {
        await invoke('disconnect_ssh', { agentId: agent.id });
        console.log('[SSH Debug] 成功断开旧连接');
      } catch (e) {
        console.log('[SSH Debug] 断开旧连接时出错，继续新建连接:', e);
      }

      await new Promise(resolve => setTimeout(resolve, 500));
      await invoke('connect_ssh', { agentId: agent.id });
      console.log(`[SSH Debug] SSH连接已初始化`);
      await new Promise(resolve => setTimeout(resolve, 300));

      const pingResult = await invoke<number>('test_agent_connection', { agent });
      console.log(`[SSH Debug] 连接延迟: ${pingResult}ms`);

      if (pingResult > 0) {
        setIsConnected(true);
        setLoading(false);
        setConnectionRetries(0);
        setLastError('');
        message.success('SSH连接成功');

        if (terminal) {
          terminal.writeln(`\r\n\x1b[32m成功连接到 ${agent.name} (${agent.ip})，延迟: ${pingResult}ms\x1b[0m\r\n`);
        }

        return true;
      } else {
        throw new Error('连接测试失败');
      }
    } catch (error) {
      const { errorType } = await handleSSHError(error, terminal);

      if (connectionRetries >= maxRetries) {
        message.error(`SSH连接失败: ${errorType}，已重试${maxRetries}次`);
        setLoading(false);
        setIsConnected(false);
        setConnectionInitiated(false);
        setConnectionRetries(0);

        if (errorType === '认证失败' || errorType === '主机密钥验证失败' || errorType === '权限被拒绝') {
          message.warning('请检查SSH连接配置是否正确');
        } else if (errorType === '连接超时' || errorType === '连接被拒绝' || errorType === '网络错误') {
          message.warning('请检查网络连接和服务器状态');
        }

        if (terminal) {
          terminal.writeln(`\r\n\x1b[31mSSH连接失败: ${errorType}\x1b[0m`);
          terminal.writeln('\x1b[33m请检查服务器状态和连接信息后重试\x1b[0m');
          terminal.writeln('\x1b[33m可以点击右上角的"重新连接"按钮尝试重新建立连接\x1b[0m');
        }

        return false;
      }

      setConnectionRetries(prev => prev + 1);
      message.warning(`SSH连接失败: ${errorType}，正在重试...`);

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      const retryDelay = 2000 + Math.random() * 1000;
      reconnectTimeoutRef.current = setTimeout(() => {
        handleSSHConnection();
      }, retryDelay);

      return false;
    }
  }, [agent, isConnected, connectionRetries, cleanup, handleSSHError]);

  // 自动重连逻辑
  const handleAutoReconnect = useCallback(async (terminal: Terminal) => {
    if (!agent) {
      setLastError('无法重连：未设置执行机信息');
      return;
    }

    // 最大重试次数从5次增加到10次，提高连接成功率
    if (connectionRetries >= 10) {
      setLastError('重连次数已达上限，请手动重新连接');
      terminal.writeln(`\x1b[31m自动重连已达最大尝试次数(10次)，请点击"重新连接"按钮\x1b[0m`);
      return;
    }

    // 增加重试计数
    setConnectionRetries(prev => prev + 1);
    terminal.writeln(`\x1b[33m正在尝试第${connectionRetries + 1}次重连...\x1b[0m`);

    try {
      // 尝试重新连接
      const connected = await handleSSHConnection();
      if (connected) {
        terminal.writeln('\x1b[32m重连成功，正在初始化终端...\x1b[0m');
        if (agent?.id) {
          await initPtySession(terminal, agent.id);
        }
        setConnectionRetries(0);
        setLastError('');
      } else {
        throw new Error('重连失败');
      }
    } catch (error) {
      console.error('[SSH Debug] 自动重连失败:', error);
      terminal.writeln(`\x1b[31m自动重连失败: ${error}\x1b[0m`);

      // 设置延迟后再次尝试
      reconnectTimeoutRef.current = setTimeout(() => {
        handleAutoReconnect(terminal);
      }, 3000); // 3秒后重试
    }
  }, [agent, connectionRetries, handleSSHConnection]);

  // 初始化终端实例
  const initTerminalInstance = useCallback(async (terminalId: string): Promise<(() => void) | undefined> => {
      const containerId = `xterm-container-${terminalId}`;
      const container = document.getElementById(containerId);
      if (!container || !agent) {
          console.error(`[SSH Debug] Terminal container ${containerId} not found or agent not set`);
          return undefined;
      }

      try {
          // 创建新的终端实例
          const terminal = new Terminal({
              cursorBlink: true,
              fontSize: 14,
              fontFamily: 'Menlo, Monaco, Consolas, monospace',
              theme: {
                  background: '#1e1e1e',
                  foreground: '#f0f0f0',
                  cursor: '#ffffff',
                  cursorAccent: '#000000',
                  selectionBackground: 'rgba(255, 255, 255, 0.3)'
              },
              convertEol: true,
              scrollback: 3000,
              allowTransparency: true,
              rows: 24,
              cols: 80,
              disableStdin: false      // 确保允许输入
          });

          const fitAddon = new FitAddon();
          terminal.loadAddon(fitAddon);
          terminal.loadAddon(new WebLinksAddon());

          // 打开终端前先清空容器
          container.innerHTML = '';

          // 打开终端
          terminal.open(container);
          fitAddon.fit();

          // 保存终端实例引用
          terminalInstancesRef.current.set(terminalId, { terminal, fitAddon });

          // 设置终端大小调整处理器
          const handleResize = async () => {
              if (!terminalInstancesRef.current.has(terminalId)) return;

              const { fitAddon } = terminalInstancesRef.current.get(terminalId)!;
              fitAddon.fit();

              try {
                  const { cols, rows } = terminal;
                  console.log(`[SSH Debug] 调整终端大小为: ${cols}x${rows}`);

                  if (isConnected && agent?.id) {
                      await invoke('resize_pty', { agentId: agent.id, cols, rows });
                  }
              } catch (error) {
                  console.error('[SSH Debug] 调整PTY大小失败:', error);
                  terminal.write('\r\n\x1b[31m终端大小调整失败\x1b[0m\r\n');
              }
          };

          // 初始调整大小
          handleResize();

          // 使用防抖处理窗口大小调整事件
          const debouncedResize = debounce(handleResize, 100);
          window.addEventListener('resize', debouncedResize);

          // 确保终端获得焦点并可见
          setTimeout(() => {
              terminal.focus();
              container.style.visibility = 'visible';
          }, 100);

          // 点击终端时自动获取焦点
          container.addEventListener('click', () => terminal.focus());

          // 设置终端输入处理
          terminal.onData(async (data) => {
              if (!isConnected) {
                  terminal.write('\r\n\x1b[31m未连接到服务器\x1b[0m\r\n');
                  return;
              }

              try {
                  if (agent?.id) {
                      await invoke('write_to_pty', { agentId: agent.id, data });
                  }
              } catch (error) {
                  console.error('[SSH Debug] 发送输入失败:', error);
                  terminal.write('\r\n\x1b[31m发送输入失败\x1b[0m\r\n');

                  // 尝试重新连接
                  handleAutoReconnect(terminal);
              }
          });

          // 确保终端始终可以接收输入
          terminal.attachCustomKeyEventHandler((event) => {
              // 阻止浏览器默认快捷键，但允许复制粘贴
              if (event.type === 'keydown' && event.ctrlKey) {
                  if (event.key === 'c' || event.key === 'v') {
                      return true;
                  }
                  return false;
              }
              return true;
          });

          // 订阅PTY输出
          if (isConnected && agent?.id) {
              await invoke('subscribe_to_pty_output', { agentId: agent.id });
              console.log('[SSH Debug] PTY输出订阅成功');

              // 显示连接成功信息
              terminal.writeln('\x1b[1;32m成功连接到 ' + agent.name + ' (' + agent.ip + ')\x1b[0m');
              terminal.writeln('\x1b[32m终端已就绪\x1b[0m');
          }

          return () => {
              window.removeEventListener('resize', debouncedResize);
              terminal.dispose();
          };
      } catch (error) {
          console.error('[SSH Debug] 终端初始化失败:', error);
          throw error;
      }
  }, [agent, isConnected, handleAutoReconnect]);

  // 处理重连
  const handleReconnect = useCallback(async () => {
    if (!agent?.id || loading) return;

    try {
      setLoading(true);
      setConnectionRetries(0);

      // 在终端显示重连信息
      const terminal = terminalInstancesRef.current.get('terminal-1')?.terminal;
      if (!terminal) return;

      terminal.writeln('\r\n\x1b[33m正在尝试重新连接...\x1b[0m');

      // 先清理现有连接
      await cleanup();

      // 尝试重新连接
      const connected = await handleSSHConnection();

      if (connected) {
        const terminalId = tabs[0]?.terminalId || 'terminal-1';
        await new Promise(resolve => setTimeout(resolve, 500)); // 等待连接稳定

        terminal.writeln('\r\n\x1b[32m重新连接成功，正在初始化终端...\x1b[0m');

        await initTerminalInstance(terminalId);

        terminal.writeln('\r\n\x1b[32m终端初始化完成，可以开始使用了\x1b[0m');
        terminal.focus();
      } else {
        throw new Error('重连失败');
      }
    } catch (error) {
      console.error('[SSH Debug] 重连失败:', error);
      message.error('重连失败，请手动重试');
      setLoading(false);
      setIsConnected(false);
      setConnectionInitiated(false);

      // 在终端显示重连失败信息
      const terminal = terminalInstancesRef.current.get('terminal-1')?.terminal;
      if (!terminal) return;

      terminal.writeln('\r\n\x1b[31m重新连接失败，请检查网络和服务器状态后再试\x1b[0m');
      terminal.writeln('\x1b[33m您可以尝试关闭终端窗口后重新打开\x1b[0m');
    } finally {
      setLoading(false);
    }
  }, [agent, loading, tabs, cleanup, handleSSHConnection, initTerminalInstance]);

  // 监听PTY输出
  useEffect(() => {
    if (!open || !agent || !isConnected) return;

    let unsubscribe: UnlistenFn;
    const subscribeToOutput = async () => {
      try {
        unsubscribe = await listen<string>('pty_output', (event) => {
          const terminal = terminalInstancesRef.current.get('terminal-1')?.terminal;
          if (!terminal) return;

          if (event.payload) {
            terminal.write(event.payload);
          }
        });
        console.log('[SSH Debug] 已订阅PTY输出事件');
      } catch (error) {
        console.error('[SSH Debug] 订阅PTY输出失败:', error);
      }
    };

    subscribeToOutput();
    return () => {
      if (unsubscribe) {
        unsubscribe();
        console.log('[SSH Debug] 已取消订阅PTY输出事件');
      }
    };
  }, [open, agent, isConnected]);
  // 连接状态检查
  useEffect(() => {
    if (!open || !agent) {
      cleanup();
      return;
    }

    const checkConnection = async () => {
      // 如果未连接但已初始化连接，尝试自动连接
      if (!isConnected && connectionInitiated) {
        console.log('[SSH Debug] 检测到未连接状态，尝试自动连接...');
        const terminal = terminalInstancesRef.current.get('terminal-1')?.terminal;
        if (!terminal) return;

        terminal.writeln('\x1b[33m正在尝试自动连接...\x1b[0m');
        await connectSSH();
        if (isConnected && agent.id) {
          await initPtySession(terminal, agent.id);
        }
        return;
      }

      // 检查已连接的会话是否仍然有效
      if (isConnected) {
        try {
          const pingResult = await invoke<number>('test_agent_connection', { agent });
          if (pingResult <= 0) {
            console.log('[SSH Debug] 检测到连接断开，准备重连...');
            setIsConnected(false);
            const terminal = terminalInstancesRef.current.get('terminal-1')?.terminal;
            if (!terminal) return;

            terminal.writeln('\x1b[33m检测到连接断开，正在尝试恢复连接...\x1b[0m');
            handleAutoReconnect(terminal);
          } else {
            // 连接正常，重置重试计数
            if (connectionRetries > 0) {
              setConnectionRetries(0);
              setLastError('');
            }
            // 每次成功检测连接时，更新最后连接时间
            if (agent.id) {
              localStorage.setItem(`ssh_connection_${agent.id}`, JSON.stringify({
                lastConnected: new Date().toISOString(),
                retryCount: connectionRetries
              }));
            }
          }
        } catch (error) {
          console.error('[SSH Debug] 连接检查失败:', error);
          setIsConnected(false);
          const terminal = terminalInstancesRef.current.get('terminal-1')?.terminal;
          if (!terminal) return;

          terminal.writeln(`\x1b[31m连接检查失败: ${error}\x1b[0m`);
          terminal.writeln('\x1b[33m正在尝试恢复连接...\x1b[0m');
          handleAutoReconnect(terminal);
        }
      };
    };

    // 每15秒检查一次连接状态，提高检测频率
    const connectionCheckInterval = setInterval(checkConnection, 15000);

    // 初始连接检查
    if (agent && !isConnected && open) {
      // 延迟1秒执行初始连接，确保组件已完全挂载
      setTimeout(() => {
        const terminal = terminalInstancesRef.current.get('terminal-1')?.terminal;
        if (terminal && !isConnected && agent.id) {
          handleSSHConnection().then(connected => {
            if (connected) {
              initPtySession(terminal, agent.id);
            }
          }).catch(err => {
            console.error('[SSH Debug] 初始连接失败:', err);
          });
        }
      }, 1000);
    }

    // 清理函数
    return () => {
      clearInterval(connectionCheckInterval);
      cleanup();
    };
  }, [agent, isConnected, open, connectionInitiated, connectSSH, handleSSHConnection, cleanup, connectionRetries]);

  return (
    <Modal {...modalProps}>
      <div className="ssh-terminal-container">
        <div className="terminal-header">
          <Space>
            {isConnected ? (
              <Tag color="success">已连接</Tag>
            ) : (
              <Tag color="error">未连接</Tag>
            )}
            <Button
              type="primary"
              onClick={handleReconnect}
              icon={<ReloadOutlined />}
              loading={loading}
              disabled={loading}
            >
              重新连接
            </Button>
          </Space>
        </div>
        <div id="xterm-container-terminal-1" style={{ height: '500px', padding: '10px' }} />
      </div>
    </Modal>
  );
};

export default SSHTerminal;