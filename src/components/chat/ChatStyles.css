/* 聊天组件样式 - 已优化并合并 */
/* 此文件包含所有聊天相关组件的样式 */

/* 容器样式 */
.ai-chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

/* 对话列表侧边栏 */
.conversation-sider {
  border-right: 1px solid #f0f0f0;
  overflow: auto;
}

.conversation-sider-header,
.conversation-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px;
}

.new-conversation-button {
  width: 100%;
}

.conversation-menu {
  border-right: none;
}

.conversation-item {
  margin: 4px 0;
  border-radius: 4px;
}

.conversation-item-content {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.conversation-title {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 12px;
}

.empty-conversations {
  margin-top: 40px;
}

/* 主内容区 */
.chat-content-layout {
  background: #fff;
  height: 100%;
}

.chat-content {
  height: 100%;
  overflow: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 消息区域 */
.messages-area,
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 16px;
  min-height: 300px;
}

.message {
  margin-bottom: 16px;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

/* 消息卡片样式 */
.message-card {
  max-width: 85%;
  margin-bottom: 16px;
  border-radius: 8px;
}

.user-message {
  margin-left: auto;
  background-color: #e6f7ff;
}

.assistant-message {
  margin-right: auto;
  background-color: #f5f5f5;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-role {
  font-weight: 500;
}

.message-time {
  font-size: 12px;
}

.message-content {
  white-space: pre-wrap;
  word-break: break-word;
}

.delete-button {
  opacity: 0.5;
  transition: opacity 0.3s;
}

.delete-button:hover {
  opacity: 1;
}

/* 输入区域 */
.input-area,
.message-input-container {
  display: flex;
  gap: 8px;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
}

.input-area .ant-input-textarea,
.message-textarea {
  flex: 1;
  margin-right: 16px;
  resize: none;
}

.input-area .ant-btn,
.send-button {
  align-self: flex-end;
}

/* 模型选择器 */
.model-selector-container {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.model-selector-label {
  margin-right: 8px;
}

.model-selector {
  flex: 1;
}

/* 聊天布局 */
.chat-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

/* 空状态 */
.empty-chat {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 24px;
  text-align: center;
}

.empty-chat-title {
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 500;
}

.empty-chat-description {
  margin-bottom: 24px;
  color: rgba(0, 0, 0, 0.45);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .conversation-sider {
    position: absolute;
    z-index: 10;
    height: 100%;
    left: 0;
    top: 0;
  }

  .ant-layout-sider-collapsed {
    transform: translateX(-100%);
  }
}

/* 修复 Tooltip 组件的 findDOMNode 警告 */
.ant-tooltip {
  pointer-events: none;
}

.ant-tooltip-inner {
  pointer-events: auto;
}