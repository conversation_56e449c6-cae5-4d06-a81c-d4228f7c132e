import React, { useEffect } from 'react';
import { Modal, Form, Input, Select, Button } from 'antd';
import { ApiConfig } from '../../types';
import './ChatStyles.css';

interface NewConversationModalProps {
  visible: boolean;
  onCancel: () => void;
  onCreateConversation: (values: { title: string; provider: string; model: string }) => void;
  apiConfigs: ApiConfig[];
  loading: boolean;
}

const { Option } = Select;

const NewConversationModal: React.FC<NewConversationModalProps> = ({
  visible,
  onCancel,
  onCreateConversation,
  apiConfigs,
  loading,
}) => {
  const [form] = Form.useForm();
  const [providerModels, setProviderModels] = React.useState<string[]>([]);

  // 重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
      setProviderModels([]);
    }
  }, [visible, form]);

  // 处理服务提供商变化
  const handleProviderChange = (value: string) => {
    // 根据选择的服务提供商更新可用模型列表
    const config = apiConfigs.find(c => c.provider === value);
    if (config) {
      setProviderModels(config.models || []);
      // 重置模型选择
      form.setFieldValue('model', undefined);
    } else {
      setProviderModels([]);
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onCreateConversation(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title="创建新对话"
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
          disabled={apiConfigs.length === 0}
        >
          创建
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ title: '新对话' }}
      >
        <Form.Item
          name="title"
          label="对话标题"
          rules={[{ required: true, message: '请输入对话标题' }]}
        >
          <Input placeholder="输入对话标题" />
        </Form.Item>

        <Form.Item
          name="provider"
          label="服务提供商"
          rules={[{ required: true, message: '请选择服务提供商' }]}
        >
          <Select
            placeholder="选择服务提供商"
            onChange={handleProviderChange}
            disabled={apiConfigs.length === 0}
          >
            {apiConfigs.map(config => (
              <Option key={config.provider} value={config.provider}>
                {config.provider}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="model"
          label="模型"
          rules={[{ required: true, message: '请选择模型' }]}
        >
          <Select
            placeholder="选择模型"
            disabled={providerModels.length === 0}
          >
            {providerModels.map(model => (
              <Option key={model} value={model}>
                {model}
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default NewConversationModal;