import React from 'react';
import { ChatMessage } from '../../types';
import { Card, Typography, Button, Tooltip, Space } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import './ChatStyles.css';

interface MessageListProps {
  messages: ChatMessage[];
  isLoading: boolean;
  onDeleteMessage: (messageId: string) => void;
}

const { Text } = Typography;

const MessageList: React.FC<MessageListProps> = ({ messages, isLoading, onDeleteMessage }) => {
  // 格式化消息时间
  const formatMessageTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } catch (error) {
      console.error('日期格式化错误:', error);
      return dateString; // 如果解析失败，返回原始字符串
    }
  };

  return (
    <div className="message-list">
      {messages.map((msg) => (
        <Card
          key={msg.id}
          className={`message-card ${msg.role === 'assistant' ? 'assistant-message' : 'user-message'}`}
          bordered={false}
          style={{ marginBottom: 16 }}
        >
          <div className="message-header">
            <Text type="secondary" className="message-role">
              {msg.role === 'user' ? '用户' : 'AI助手'}
            </Text>
            <Space>
              <Text type="secondary" className="message-time">
                {formatMessageTime(msg.created_at)}
              </Text>
              <Tooltip title="删除消息">
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onDeleteMessage(msg.id)}
                  className="delete-button"
                />
              </Tooltip>
            </Space>
          </div>
          <div className="message-content">
            {msg.content.split('\n').map((line, index) => {
              // 检测是否为代码块
              if (line.startsWith('```') && line.length > 3) {
                const language = line.slice(3).trim();
                return (
                  <div key={index} className="code-block-header">
                    <span className="code-language">{language}</span>
                  </div>
                );
              } else if (line === '```') {
                return <div key={index} className="code-block-footer"></div>;
              } else if (line.startsWith('    ') || line.startsWith('\t')) {
                // 缩进的行视为代码
                return <div key={index} className="code-line">{line}</div>;
              } else {
                return <div key={index}>{line || <br />}</div>;
              }
            })}
          </div>
        </Card>
      ))}
    </div>
  );
};

export default MessageList;