import React from 'react';
import { Select, Typography, Spin } from 'antd';
import './ChatStyles.css';

interface ModelSelectorProps {
  availableModels: string[];
  selectedModel: string;
  onModelChange: (value: string) => void;
  isLoading: boolean;
  disabled: boolean;
}

const { Text } = Typography;
const { Option } = Select;

const ModelSelector: React.FC<ModelSelectorProps> = ({
  availableModels,
  selectedModel,
  onModelChange,
  isLoading,
  disabled,
}) => {
  return (
    <div className="model-selector-container">
      <Text strong className="model-selector-label">当前模型：</Text>
      <Select
        value={selectedModel}
        onChange={onModelChange}
        style={{ width: 200 }}
        loading={isLoading}
        disabled={disabled || isLoading || availableModels.length === 0}
        placeholder="选择模型"
        className="model-selector"
      >
        {availableModels.map((model) => (
          <Option key={model} value={model}>
            {model}
          </Option>
        ))}
      </Select>
    </div>
  );
};

export default ModelSelector;