import React from 'react';
import { Layout, <PERSON>u, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typography, Empty } from 'antd';
import { PlusOutlined, MessageOutlined } from '@ant-design/icons';
import { ChatConversation } from '../../types';
import './ChatStyles.css';

interface ConversationSidebarProps {
  conversations: ChatConversation[];
  currentConversation: ChatConversation | null;
  collapsed: boolean;
  onSelectConversation: (conversation: ChatConversation) => void;
  onNewConversation: () => void;
}

const { Sider } = Layout;
const { Text } = Typography;

const ConversationSidebar: React.FC<ConversationSidebarProps> = ({
  conversations,
  currentConversation,
  collapsed,
  onSelectConversation,
  onNewConversation,
}) => {
  // 格式化对话时间
  const formatConversationTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    } catch (error) {
      console.error('日期格式化错误:', error);
      return '';
    }
  };

  return (
    <Sider
      width={250}
      collapsible
      collapsed={collapsed}
      className="conversation-sider"
      theme="light"
    >
      <div className="conversation-header">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={onNewConversation}
          block
          className="new-conversation-button"
        >
          {!collapsed && '新建对话'}
        </Button>
      </div>

      {conversations.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无对话"
          className="empty-conversations"
        />
      ) : (
        <Menu
          mode="inline"
          selectedKeys={currentConversation ? [currentConversation.id] : []}
          className="conversation-menu"
        >
          {conversations.map((conversation) => (
            <Menu.Item
              key={conversation.id}
              onClick={() => onSelectConversation(conversation)}
              icon={<MessageOutlined />}
              className="conversation-item"
            >
              <div className="conversation-item-content">
                <Tooltip title={conversation.title} placement="right">
                  <Text ellipsis className="conversation-title">
                    {conversation.title}
                  </Text>
                </Tooltip>
                {!collapsed && (
                  <Text type="secondary" className="conversation-time">
                    {formatConversationTime(conversation.updated_at)}
                  </Text>
                )}
              </div>
            </Menu.Item>
          ))}
        </Menu>
      )}
    </Sider>
  );
};

export default ConversationSidebar;