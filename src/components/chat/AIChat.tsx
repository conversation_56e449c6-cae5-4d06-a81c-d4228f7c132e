import React, { useState, useEffect, useRef, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { ChatMessage, ChatConversation, ApiConfig } from '../../types';
import { Layout, Typography, Empty, Button, Spin, Alert, Modal, Form, message, notification } from 'antd';
import { PlusOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import './ChatStyles.css';

// 导入子组件
import ConversationSidebar from './ConversationSidebar';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import ModelSelector from './ModelSelector';
import NewConversationModal from './NewConversationModal';

interface AIChatProps {
  currentConversation: ChatConversation | null;
  setCurrentConversation: (conversation: ChatConversation | null) => void;
  apiConfigs: ApiConfig[];
  refreshConversations: () => void;
}

const { Content } = Layout;
const { Title, Text } = Typography;

const AIChat: React.FC<AIChatProps> = ({
  currentConversation,
  setCurrentConversation,
  apiConfigs,
  refreshConversations,
}) => {
  // 状态管理
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSending, setIsSending] = useState(false);
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [siderCollapsed, setSiderCollapsed] = useState(false);
  const [isNewConversationModalVisible, setIsNewConversationModalVisible] = useState(false);
  const [isCreatingConversation, setIsCreatingConversation] = useState(false);
  
  // 引用
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 获取消息列表
  const fetchMessages = useCallback(async () => {
    if (!currentConversation) return;
    setIsLoading(true);
    setError(null);
    try {
      // 添加超时处理
      const timeoutPromise = new Promise<ChatMessage[]>((_, reject) => {
        setTimeout(() => reject(new Error('获取消息超时，请检查网络连接')), 10000);
      });

      // 使用 Promise.race 来实现超时处理
      const fetchedMessages = await Promise.race([
        invoke<ChatMessage[]>('get_messages', { conversationId: currentConversation.id }),
        timeoutPromise
      ]) as ChatMessage[];

      // 防止空值或非数组
      if (!fetchedMessages || !Array.isArray(fetchedMessages)) {
        throw new Error('返回的消息格式无效');
      }

      // 按创建时间排序
      fetchedMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      setMessages(fetchedMessages);
    } catch (err: any) {
      console.error('Failed to fetch messages:', err);
      let errorMessage = err.toString();

      // 处理常见错误类型
      if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
        errorMessage = '获取消息超时，请检查网络连接或稍后重试';
      } else if (errorMessage.includes('format') || errorMessage.includes('格式')) {
        errorMessage = '消息格式错误，请刷新页面重试';
      }

      setError(`获取消息失败: ${errorMessage}`);
      message.error(`获取消息失败: ${errorMessage}`);

      // 保留现有消息，防止渲染错误
      if (messages.length === 0) {
        setMessages([]);
      }
    } finally {
      setIsLoading(false);
    }
  }, [currentConversation, messages.length]);

  // 获取对话列表
  const fetchConversations = useCallback(async () => {
    try {
      // 添加超时处理
      const timeoutPromise = new Promise<ChatConversation[]>((_, reject) => {
        setTimeout(() => reject(new Error('获取对话列表超时，请检查网络连接')), 10000);
      });

      // 使用 Promise.race 来实现超时处理
      const fetchedConversations = await Promise.race([
        invoke<ChatConversation[]>('get_conversations'),
        timeoutPromise
      ]) as ChatConversation[];

      // 防止空值或非数组
      if (!fetchedConversations || !Array.isArray(fetchedConversations)) {
        throw new Error('返回的对话列表格式无效');
      }

      setConversations(fetchedConversations);

      // 如果当前选中的对话不在列表中，重置选中状态
      if (currentConversation && fetchedConversations.length > 0) {
        const stillExists = fetchedConversations.some(conv => conv.id === currentConversation.id);
        if (!stillExists) {
          setCurrentConversation(fetchedConversations[0]);
          message.info('当前对话已不存在，已自动切换到其他对话');
        }
      }
    } catch (err: any) {
      console.error('Failed to fetch conversations:', err);
      let errorMessage = err.toString();

      // 处理常见错误类型
      if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
        errorMessage = '获取对话列表超时，请检查网络连接或稍后重试';
      } else if (errorMessage.includes('format') || errorMessage.includes('格式')) {
        errorMessage = '对话列表格式错误，请刷新页面重试';
      }

      setError(`获取对话列表失败: ${errorMessage}`);
      message.error(`获取对话列表失败: ${errorMessage}`);

      // 保留现有对话列表，防止渲染错误
      if (conversations.length === 0) {
        setConversations([]);
      }
    }
  }, [currentConversation, conversations.length, setCurrentConversation]);

  // 初始加载时获取对话列表
  useEffect(() => {
    fetchConversations();
  }, [fetchConversations]);

  // 全局错误处理
  useEffect(() => {
    const handleError = (error: Error) => {
      console.error('Unhandled error in AIChat component:', error);
      notification.error({
        message: '应用错误',
        description: `发生意外错误: ${error.message}`,
        duration: 0,
      });
    };

    window.addEventListener('error', (event) => handleError(event.error));
    window.addEventListener('unhandledrejection', (event) => handleError(event.reason));

    return () => {
      window.removeEventListener('error', (event) => handleError(event.error));
      window.removeEventListener('unhandledrejection', (event) => handleError(event.reason));
    };
  }, []);

  // 更新可用模型和选中模型
  useEffect(() => {
    if (currentConversation && apiConfigs.length > 0) {
      const config = apiConfigs.find(c => c.provider === currentConversation.provider);
      if (config) {
        setAvailableModels(config.models || []);
        setSelectedModel(currentConversation.model);
      } else {
        setAvailableModels([]);
        setSelectedModel('');

        // 如果没有找到对应的API配置，显示警告
        notification.warning({
          message: '缺少API配置',
          description: (
            <div>
              <p>未找到提供商 <strong>{currentConversation.provider}</strong> 的API配置。</p>
              <p>请先在API配置页面添加该提供商的API密钥。</p>
            </div>
          ),
          duration: 5,
        });
      }

      // 获取消息列表
      try {
        fetchMessages().catch(err => {
          console.error('Failed to fetch messages:', err);
          setError(`获取消息失败: ${err.toString()}`);
        });
      } catch (err: any) {
        console.error('Exception in fetchMessages:', err);
        setError(`获取消息异常: ${err.toString()}`);
      }
    } else {
      setMessages([]);
      setAvailableModels([]);
      setSelectedModel('');
    }
  }, [currentConversation, apiConfigs, fetchMessages]);

  // 滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !currentConversation || isSending) return;

    setIsSending(true);
    setError(null);
    const userMessageContent = newMessage;
    setNewMessage(''); // 立即清空输入框

    // 乐观地添加用户消息
    const optimisticUserMessage: ChatMessage = {
      id: `temp-user-${Date.now()}`,
      role: 'user',
      content: userMessageContent,
      created_at: new Date().toISOString(),
      conversation_id: currentConversation.id,
    };
    setMessages(prev => [...prev, optimisticUserMessage]);

    try {
      // 检查API配置
      const config = apiConfigs.find(c => c.provider === currentConversation.provider);
      if (!config) {
        throw new Error(`未找到提供商 ${currentConversation.provider} 的API配置，请先配置API`);
      }

      if (!config.api_key) {
        throw new Error(`提供商 ${currentConversation.provider} 的API密钥未设置，请先配置API密钥`);
      }

      if (!selectedModel) {
        throw new Error('未选择模型，请先选择模型');
      }

      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时，请检查网络连接')), 60000);
      });

      // 显示加载提示
      const loadingMessage = message.loading('正在生成回复...', 0);

      try {
        // 使用 Promise.race 来实现超时处理
        const response: any = await Promise.race([
          invoke('send_chat_message', {
            request: {
              conversation_id: currentConversation.id,
              message: userMessageContent,
              model: selectedModel,
              provider: currentConversation.provider,
            },
          }),
          timeoutPromise
        ]);

        // 关闭加载提示
        loadingMessage();

        // 重新获取消息列表
        await fetchMessages();
      } catch (err) {
        // 关闭加载提示
        loadingMessage();
        throw err; // 将错误传递给外层catch块
      }
    } catch (err: any) {
      console.error('Failed to send message:', err);
      let errorMessage = err.toString();

      // 处理常见错误类型
      if (errorMessage.includes('API key')) {
        errorMessage = 'API密钥无效或未提供，请检查API配置';
      } else if (errorMessage.includes('model')) {
        errorMessage = '模型无效或不可用，请选择其他模型';
      } else if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
        errorMessage = '请求超时，请检查网络连接或稍后重试';
      } else if (errorMessage.includes('Unsupported provider')) {
        errorMessage = `不支持的提供商: ${currentConversation.provider}，请选择其他提供商`;
      } else if (errorMessage.includes('format')) {
        errorMessage = `API响应格式错误，请检查提供商和模型配置`;
      }

      setError(`发送消息失败: ${errorMessage}`);

      // 移除乐观添加的消息
      setMessages(prev => prev.filter(msg => msg.id !== optimisticUserMessage.id));
      // 恢复输入框内容
      setNewMessage(userMessageContent);

      // 显示错误提示
      notification.error({
        message: '发送消息失败',
        description: errorMessage,
        duration: 5,
        placement: 'topRight',
      });
    } finally {
      setIsSending(false);
    }
  };

  // 更改模型
  const handleModelChange = async (value: string) => {
    const newModel = value;
    if (!currentConversation || newModel === selectedModel) return;

    // 验证模型名称
    if (!newModel || newModel.trim() === '') {
      message.error('请选择有效的模型');
      return;
    }

    // 验证模型是否在可用模型列表中
    const config = apiConfigs.find(c => c.provider === currentConversation.provider);
    if (config && config.models && !config.models.includes(newModel)) {
      console.warn(`模型 ${newModel} 不在提供商 ${currentConversation.provider} 的可用模型列表中`);
    }

    setIsLoading(true);
    setError(null);

    // 显示加载提示
    const loadingMessage = message.loading('正在更新模型...', 0);

    try {
      await invoke('update_conversation_model', {
        conversation_id: currentConversation.id,
        new_model: newModel,
      });

      // 关闭加载提示
      loadingMessage();

      setSelectedModel(newModel);
      // 更新对话对象
      setCurrentConversation({ ...currentConversation, model: newModel });
      // 刷新对话列表
      refreshConversations();
      // 显示成功提示
      message.success(`模型已更新为 ${newModel}`);
    } catch (err: any) {
      // 关闭加载提示
      loadingMessage();

      console.error('Failed to update model:', err);
      let errorMessage = err.toString();

      // 处理常见错误类型
      if (errorMessage.includes('model')) {
        errorMessage = '模型无效或不可用，请选择其他模型';
      } else if (errorMessage.includes('database')) {
        errorMessage = '数据库操作失败，请重试';
      } else if (errorMessage.includes('conversation')) {
        errorMessage = '对话不存在或已被删除';
      }

      setError(`更新模型失败: ${errorMessage}`);
      // 显示错误提示
      notification.error({
        message: '更新模型失败',
        description: errorMessage,
        duration: 5,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 删除消息
  const handleDeleteMessage = async (messageId: string) => {
    if (!currentConversation) return;

    // 添加确认对话框
    Modal.confirm({
      title: '删除消息',
      content: '确定要删除这条消息吗？',
      okText: '删除',
      cancelText: '取消',
      okButtonProps: { danger: true },
      onOk: async () => {
        try {
          // 乐观地先从界面移除消息
          setMessages(prev => prev.filter(msg => msg.id !== messageId));

          await invoke('delete_message', { messageId });
          message.success('消息已删除');
        } catch (err: any) {
          console.error('Failed to delete message:', err);
          let errorMessage = err.toString();
          setError(`删除消息失败: ${errorMessage}`);
          message.error(`删除消息失败: ${errorMessage}`);

          // 如果删除失败，重新获取消息列表恢复正确状态
          fetchMessages();
        }
      },
    });
  };

  // 创建新对话
  const handleCreateConversation = async (values: { title: string; provider: string; model: string }) => {
    setIsCreatingConversation(true);
    try {
      // 验证表单值
      if (!values.title || !values.provider || !values.model) {
        throw new Error('请填写所有必填字段');
      }

      // 检查API配置
      const config = apiConfigs.find(c => c.provider === values.provider);
      if (!config) {
        throw new Error(`未找到提供商 ${values.provider} 的API配置，请先配置API`);
      }

      if (!config.api_key) {
        throw new Error(`提供商 ${values.provider} 的API密钥未设置，请先配置API密钥`);
      }

      // 创建新对话
      const newConversation = await invoke<ChatConversation>('create_conversation', {
        title: values.title,
        provider: values.provider,
        model: values.model,
      });

      // 关闭模态框
      setIsNewConversationModalVisible(false);
      // 刷新对话列表
      refreshConversations();
      // 选中新创建的对话
      setCurrentConversation(newConversation);
      // 显示成功提示
      message.success('对话创建成功');
    } catch (err: any) {
      console.error('Failed to create conversation:', err);
      let errorMessage = err.toString();

      // 处理常见错误类型
      if (errorMessage.includes('API key')) {
        errorMessage = 'API密钥无效或未提供，请检查API配置';
      } else if (errorMessage.includes('model')) {
        errorMessage = '模型无效或不可用，请选择其他模型';
      } else if (errorMessage.includes('provider')) {
        errorMessage = '提供商无效或不可用，请选择其他提供商';
      } else if (errorMessage.includes('database')) {
        errorMessage = '数据库操作失败，请重试';
      }

      // 显示错误提示
      notification.error({
        message: '创建对话失败',
        description: errorMessage,
        duration: 5,
      });
    } finally {
      setIsCreatingConversation(false);
    }
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="empty-chat">
      <Title level={3} className="empty-chat-title">欢迎使用AI聊天</Title>
      <Text className="empty-chat-description">
        选择一个现有对话或创建一个新对话开始聊天
      </Text>
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={() => setIsNewConversationModalVisible(true)}
        disabled={apiConfigs.length === 0}
      >
        创建新对话
      </Button>
    </div>
  );

  return (
    <Layout style={{ height: '100%' }}>
      <ConversationSidebar
        conversations={conversations}
        currentConversation={currentConversation}
        collapsed={siderCollapsed}
        onSelectConversation={setCurrentConversation}
        onNewConversation={() => setIsNewConversationModalVisible(true)}
      />

      <Content className="chat-container">
        {!currentConversation ? (
          renderEmptyState()
        ) : (
          <>
            <div className="chat-header">
              <Title level={4} className="chat-title">{currentConversation.title}</Title>
              <ModelSelector
                availableModels={availableModels}
                selectedModel={selectedModel}
                onModelChange={handleModelChange}
                isLoading={isLoading}
                disabled={!currentConversation}
              />
            </div>

            <div className="chat-content">
              {error && (
                <Alert
                  message="错误"
                  description={error}
                  type="error"
                  showIcon
                  closable
                  style={{ margin: '16px' }}
                  onClose={() => setError(null)}
                />
              )}

              {isLoading ? (
                <div style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
                  <Spin size="large" tip="加载消息中..." />
                </div>
              ) : messages.length === 0 ? (
                <Empty
                  description="暂无消息，开始发送第一条消息吧"
                  style={{ margin: '40px 0' }}
                />
              ) : (
                <MessageList
                  messages={messages}
                  isLoading={isLoading}
                  onDeleteMessage={handleDeleteMessage}
                />
              )}

              <div ref={messagesEndRef} />
            </div>

            <MessageInput
              value={newMessage}
              onChange={setNewMessage}
              onSend={handleSendMessage}
              isSending={isSending}
              disabled={!currentConversation || isLoading}
            />
          </>
        )}
      </Content>

      <NewConversationModal
        visible={isNewConversationModalVisible}
        onCancel={() => setIsNewConversationModalVisible(false)}
        onCreateConversation={handleCreateConversation}
        apiConfigs={apiConfigs}
        loading={isCreatingConversation}
      />
    </Layout>
  );
};

export default AIChat;