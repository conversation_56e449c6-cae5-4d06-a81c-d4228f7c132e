import React, { useState, KeyboardEvent } from 'react';
import { Input, Button, Tooltip } from 'antd';
import { SendOutlined } from '@ant-design/icons';
import './ChatStyles.css';

interface MessageInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  isSending: boolean;
  disabled: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({
  value,
  onChange,
  onSend,
  isSending,
  disabled,
}) => {
  // 处理按键事件，支持Ctrl+Enter发送消息
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey) && !disabled && !isSending) {
      e.preventDefault();
      onSend();
    }
  };

  return (
    <div className="message-input-container">
      <Input.TextArea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="输入消息，按Ctrl+Enter发送"
        autoSize={{ minRows: 2, maxRows: 6 }}
        disabled={disabled || isSending}
        onKeyDown={handleKeyDown}
        className="message-textarea"
      />
      <Tooltip title="发送消息">
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={onSend}
          loading={isSending}
          disabled={disabled || isSending || !value.trim()}
          className="send-button"
        >
          发送
        </Button>
      </Tooltip>
    </div>
  );
};

export default MessageInput;