import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Layout,
  Tag,
  Divider, // Import Divider
} from 'antd';
import { PlusOutlined, CodeOutlined } from '@ant-design/icons'; // Import CodeOutlined
import type { ColumnsType } from 'antd/es/table';
import type { ProjectStatus } from '../types'; // Import ProjectStatus
import LocalTerminal from './terminal/LocalTerminal'; // Import LocalTerminal
import { ResizableBox } from 'react-resizable'; // Import ResizableBox
import 'react-resizable/css/styles.css'; // Import Resizable CSS

interface Project {
  id: string;
  name: string;
  description?: string;
  status: ProjectStatus; // Use ProjectStatus type
  created_at: string;
  updated_at: string;
  created_by: string;
  leader?: string;
  tags?: string;
}

const ProjectManagement: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null); // State for selected project ID
  const [terminalVisible, setTerminalVisible] = useState<boolean>(false); // State for terminal visibility
  const [form] = Form.useForm();

  const fetchProjects = async () => {
    try {
      const projectList = await invoke('get_projects') as Project[];
      setProjects(projectList);
    } catch (error) {
      message.error('获取项目列表失败：' + error);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const handleCreate = () => {
    setEditingProject(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record: Project) => {
    setEditingProject(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await invoke('delete_project', { id });
      message.success('删除成功');
      fetchProjects();
    } catch (error) {
      message.error('删除失败：' + error);
    }
  };

  const handleModalOk = async () => {
    try {
      // Validate all fields including the new status field
       const values = await form.validateFields() as Omit<Project, 'id' | 'created_at' | 'updated_at' | 'created_by'> & { status: ProjectStatus };
      if (editingProject) {
        await invoke('update_project', {
          project: {
            ...editingProject,
            ...values,
            updated_at: new Date().toISOString()
          }
        });
        message.success('更新成功');
      } else {
        await invoke('create_project', {
          project: {
            ...values,
            id: crypto.randomUUID(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            created_by: 'System',
            status: 'Active'
          }
        });
        message.success('创建成功');
      }
      setIsModalVisible(false);
      fetchProjects();
    } catch (error) {
      message.error('操作失败：' + error);
    }
  };

  const handleRowClick = (record: Project) => {
    setSelectedProjectId(record.id);
    setTerminalVisible(true); // Show terminal when a project is selected
    message.info(`已选择项目: ${record.name}，正在加载终端...`);
  };

  const columns: ColumnsType<Project> = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Project) => (
        <a onClick={() => handleRowClick(record)}>{text}</a>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: ProjectStatus) => { // Use ProjectStatus type
        const color = status === 'Active' ? 'green' : 'red';
        return <Tag color={color}>{status}</Tag>;
      }
    },
    {
      title: '项目负责人',
      dataIndex: 'leader',
      key: 'leader',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
          <Button type="link" icon={<CodeOutlined />} onClick={() => handleRowClick(record)}>终端</Button> {/* Add Terminal button */}
          <Button type="link" danger onClick={() => handleDelete(record.id)}>删除</Button>
        </Space>
      ),
    },
  ];

  return (
    <Layout style={{ background: '#fff', padding: '24px', display: 'flex', flexDirection: 'column', height: 'calc(100vh - 112px)' }}>
      <div style={{ marginBottom: '16px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreate}
        >
          新建项目
        </Button>
      </div>

      <div style={{ flexGrow: 1, overflow: 'auto' }}>
        <Table
          columns={columns}
          dataSource={projects}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          onRow={(record) => ({
            onClick: () => handleRowClick(record), // Make rows clickable
          })}
          rowClassName={(record) => (record.id === selectedProjectId ? 'ant-table-row-selected' : '')} // Highlight selected row
        />
      </div>

      {terminalVisible && selectedProjectId && (
        <>
          <Divider />
          <ResizableBox
            height={300} // Initial height
            minConstraints={[Infinity, 100]} // Min height 100px
            maxConstraints={[Infinity, 600]} // Max height 600px
            axis="y"
            handle={<div style={{ cursor: 'row-resize', height: '10px', background: '#f0f0f0', borderTop: '1px solid #d9d9d9', borderBottom: '1px solid #d9d9d9' }} />} // Custom handle
            resizeHandles={['n']}
            style={{ background: '#1e1e1e', overflow: 'hidden' }}
          >
            <div style={{ height: '100%', width: '100%' }}>
              <LocalTerminal projectId={selectedProjectId} />
            </div>
          </ResizableBox>
        </>
      )}

      <Modal
        title={editingProject ? '编辑项目' : '新建项目'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => setIsModalVisible(false)}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
         initialValues={{ status: 'Active' }} // Set default status for new projects
        >
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="description"
            label="项目描述"
          >
            <Input.TextArea rows={3} />
          </Form.Item>

          <Form.Item
           name="status"
           label="项目状态"
           rules={[{ required: true, message: '请选择项目状态' }]}
         >
           <Select>
             <Select.Option value="Active">Active</Select.Option>
             <Select.Option value="Inactive">Inactive</Select.Option>
             <Select.Option value="Completed">Completed</Select.Option>
           </Select>
         </Form.Item>
 
         <Form.Item
            name="leader"
            label="项目负责人"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Input placeholder="使用逗号分隔多个标签" />
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
};

export default ProjectManagement;