import React, { useState, useRef, useEffect } from 'react';
import '../../styles/resizable-divider.css';

interface ResizableDividerProps {
  direction: 'horizontal' | 'vertical';
  defaultSize?: number;
  minSize?: number;
  maxSize?: number;
  onResize?: (size: number) => void;
  onResizeStart?: () => void;
  onResizeEnd?: () => void;
  className?: string;
  style?: React.CSSProperties;
  vsCodeStyle?: boolean;
}

const ResizableDivider: React.FC<ResizableDividerProps> = ({
  direction,
  defaultSize,
  minSize = 100,
  maxSize = 500,
  onResize,
  onResizeStart,
  onResizeEnd,
  className = '',
  style = {},
  vsCodeStyle = true
}) => {
  const [resizing, setResizing] = useState(false);
  const dividerRef = useRef<HTMLDivElement>(null);
  const startPosRef = useRef<number>(0);
  const currentSizeRef = useRef<number>(defaultSize || 0);

  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setResizing(true);

    // 记录起始位置
    startPosRef.current = direction === 'horizontal' ? e.clientY : e.clientX;

    // 添加全局事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // 添加遮罩层防止文本选择
    const overlay = document.createElement('div');
    overlay.className = `resize-overlay ${direction === 'horizontal' ? 'row-resize' : 'col-resize'}`;
    overlay.id = 'resize-overlay';
    document.body.appendChild(overlay);

    // 调用开始调整大小的回调
    onResizeStart && onResizeStart();
  };

  // 处理鼠标移动事件
  const handleMouseMove = (e: MouseEvent) => {
    if (!resizing) return;

    // 计算移动距离
    const currentPos = direction === 'horizontal' ? e.clientY : e.clientX;
    const delta = currentPos - startPosRef.current;

    // 计算新的大小
    let newSize = currentSizeRef.current + delta;

    // 限制大小范围
    newSize = Math.max(minSize, Math.min(maxSize, newSize));

    // 更新当前大小
    currentSizeRef.current = newSize;

    // 更新起始位置
    startPosRef.current = currentPos;

    // 调用回调函数
    onResize && onResize(newSize);
  };

  // 处理鼠标释放事件
  const handleMouseUp = () => {
    setResizing(false);

    // 移除全局事件监听
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    // 移除遮罩层
    const overlay = document.getElementById('resize-overlay');
    if (overlay) {
      document.body.removeChild(overlay);
    }

    // 调用结束调整大小的回调
    onResizeEnd && onResizeEnd();
  };

  // 组件卸载时清理事件监听
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      const overlay = document.getElementById('resize-overlay');
      if (overlay) {
        document.body.removeChild(overlay);
      }
    };
  }, []);

  const dividerClass = vsCodeStyle
    ? `vs-code-divider ${direction} ${resizing ? 'resizing' : ''} ${className}`
    : `resizable-divider ${direction} ${resizing ? 'resizing' : ''} ${className}`;

  const handleClass = vsCodeStyle
    ? `vs-code-divider-handle ${direction}`
    : `resizable-handle ${direction}`;

  return (
    <div
      ref={dividerRef}
      className={dividerClass}
      style={style}
      onMouseDown={handleMouseDown}
    >
      <div className={handleClass} />
    </div>
  );
};

export default ResizableDivider;
