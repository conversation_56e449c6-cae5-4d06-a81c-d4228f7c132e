.compact-table {
  /* 减小表格整体内边距 */
  margin: 0;
  padding: 0;
}

/* 减小表格行高 */
.compact-table .ant-table-tbody > tr > td {
  padding: 4px 6px;
  font-size: 12px;
  line-height: 1.1;
}

/* 调整表头高度和内边距，确保过滤图标完整显示，并加粗表头文字 */
.compact-table .ant-table-thead > tr > th {
  padding: 8px 8px;
  font-size: 12px;
  line-height: 1.2;
  font-weight: 600;
}

/* 优化按钮组样式 */
.compact-table .ant-btn {
  padding: 0 6px;
  height: 22px;
  font-size: 12px;
  min-width: 22px;
}

/* 减小按钮间距 */
.compact-table .ant-space {
  gap: 3px !important;
}

/* 优化标签样式 */
.compact-table .ant-tag {
  margin-right: 1px;
  padding: 0 3px;
  font-size: 10px;
  line-height: 14px;
  height: 16px;
}

/* 优化工具提示 */
.compact-table .ant-tooltip {
  font-size: 12px;
}

/* 减小表格分页器的大小 */
.compact-table .ant-pagination-item,
.compact-table .ant-pagination-prev,
.compact-table .ant-pagination-next {
  min-width: 24px;
  height: 24px;
  line-height: 22px;
}

/* 减小表格分页器的字体大小 */
.compact-table .ant-pagination {
  font-size: 11px;
}

/* 优化表格筛选器和排序按钮 */
.compact-table .ant-table-filter-trigger,
.compact-table .ant-table-column-sorter {
  width: 16px;
  height: 16px;
  margin-left: 4px;
}

/* 优化表格选择框 */
.compact-table .ant-checkbox-wrapper {
  font-size: 12px;
}

/* 优化表格空状态 */
.compact-table .ant-empty-normal {
  margin: 12px 0;
}

/* 优化表格加载状态 */
.compact-table .ant-spin {
  margin: 6px 0;
}

/* 优化表格行选择样式 */
.compact-table .ant-table-row-selected > td {
  background-color: rgba(24, 144, 255, 0.1);
}

/* 优化操作按钮区域 */
.compact-actions {
  margin-bottom: 6px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 优化操作按钮，加粗按钮文字 */
.compact-actions .ant-btn {
  height: 24px;
  padding: 0 6px;
  font-size: 12px;
  font-weight: 600;
}

/* 设置表格最大高度，启用滚动 */
.compact-table .ant-table-body {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 增加表格每页显示行数 */
.compact-table .ant-pagination-options-size-changer.ant-select {
  margin-right: 0;
}

/* 优化表格行悬停效果 */
.compact-table .ant-table-tbody > tr:hover > td {
  background-color: rgba(24, 144, 255, 0.05);
}

/* 减小表格内容间距 */
.compact-table .ant-table-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

/* 优化表格内的图标大小 */
.compact-table .anticon {
  font-size: 12px;
}

/* 加粗表格内按钮文字 */
.compact-table .ant-btn {
  font-weight: 600;
}

/* 减小表格内的下拉菜单大小 */
.compact-table .ant-select {
  font-size: 12px;
}

/* 优化表格内的输入框大小 */
.compact-table .ant-input {
  padding: 2px 6px;
  font-size: 12px;
}

/* 减小表格内的弹出框大小 */
.compact-table .ant-popover {
  font-size: 12px;
}

/* 优化表格内的日期选择器大小 */
.compact-table .ant-picker {
  padding: 2px 6px;
  font-size: 12px;
}

/* 减小表格行间距 */
.compact-table .ant-table-tbody > tr {
  height: 28px;
}

/* 优化表格头部过滤器按钮 */
.compact-table .ant-table-filter-column {
  margin: 0;
}

/* 确保过滤图标完整显示 */
.compact-table .ant-table-filter-trigger-container {
  right: 0;
  padding: 0 4px;
}

/* 调整过滤图标的位置和大小 */
.compact-table .ant-table-filter-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 减小表格内的间距 */
.compact-table .ant-table-container {
  border-spacing: 0;
}

/* 优化表格内的分割线 */
.compact-table .ant-table-cell-fix-left-last:after, 
.compact-table .ant-table-cell-fix-right-first:after {
  width: 1px;
}