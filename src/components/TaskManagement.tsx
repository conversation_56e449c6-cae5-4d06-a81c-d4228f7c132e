import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Tag, Modal, message, Tooltip, Progress } from 'antd';
import { PlusOutlined, PlayCircleOutlined, StopOutlined, DeleteOutlined, CopyOutlined, EditOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import TaskForm from './TaskForm';
import { invoke } from '@tauri-apps/api/core';
import './CompactLayout.css';

interface Task {
  id: string;
  name: string;
  description: string;
  task_type: 'Manual' | 'Automated';
  status: 'Unbuilt' | 'Pending' | 'Running' | 'Completed' | 'Failed';
  progress: number;
  steps: { name: string; command: string; order: number }[];
  working_dir?: string;
  schedule: string;
  scheduled_at?: string;
  created_at: string;
  updated_at: string;
  agent_id?: string;
  parent_id?: string; // 任务组ID，如果是子任务则指向父任务组
  is_group: boolean; // 是否为任务组
  execution_mode?: 'Serial' | 'Parallel'; // 子任务执行模式：串行或并行
  child_tasks?: string[]; // 子任务ID列表
  test_cases?: string[]; // 关联的测试用例ID列表
  loop_count?: number; // 循环执行次数
  lastRun?: string; // 上次执行时间
  scheduleTime?: string; // 定时执行时间
}

const TaskManagement: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [agents, setAgents] = useState<Array<{id: string, name: string}>>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [selectedTaskIds, setSelectedTaskIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    fetchTasks();
    fetchAgents();
    // 启动定时刷新
    const interval = setInterval(fetchTasks, 5000);
    setRefreshInterval(interval);
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, []);

  const fetchAgents = async () => {
    try {
      const fetchedAgents = await invoke<Array<{id: string, name: string}>>('get_agents');
      setAgents(fetchedAgents);
    } catch (error) {
      console.error('获取执行机列表失败:', error);
      // 确保即使出错也不会影响UI显示
      setAgents([]);
    }
  };

  const fetchTasks = async () => {
    try {
      setLoading(true);
      // 开始获取任务列表
      // 添加超时处理
      const fetchPromise = invoke<Task[]>('get_tasks');
      const timeoutPromise = new Promise<Task[]>((_, reject) => {
        setTimeout(() => reject(new Error('获取任务列表超时')), 10000);
      });

      const fetchedTasks = await Promise.race([fetchPromise, timeoutPromise]);

      if (Array.isArray(fetchedTasks)) {
        // 任务列表获取成功
        // 检查任务数据的完整性
        const validTasks = fetchedTasks.filter(task => {
          const isValid = task && task.id && task.name;
          if (!isValid) {
            console.warn('发现无效任务数据:', task);
          }
          return isValid;
        });

        setTasks(validTasks);
      } else {
        console.error('获取的任务列表数据格式不正确:', fetchedTasks);
        message.error('获取的任务列表数据格式不正确');
        setTasks([]);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      // 显示更友好的错误信息
      if (error instanceof Error && error.message.includes('超时')) {
        message.error('获取任务列表超时，请检查网络连接或服务器状态');
      } else {
        message.error(`获取任务列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
      // 确保即使出错也不会一直显示加载状态
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: Task, b: Task) => a.name.localeCompare(b.name),
      render: (text: string, record: Task) => (
        <Space>
          {text}
          {record.is_group && <Tag color="blue">任务组</Tag>}
          {record.parent_id && <Tag color="cyan">子任务</Tag>}
          <Tag color={record.task_type === 'Automated' ? 'green' : 'orange'}>
            {record.task_type === 'Automated' ? '自动化' : '手工'}
          </Tag>
        </Space>
      ),
      filters: [
        { text: '任务组', value: 'group' },
        { text: '子任务', value: 'child' },
        { text: '普通任务', value: 'normal' },
        { text: '自动化任务', value: 'automated' },
        { text: '手工任务', value: 'manual' },
      ],
      onFilter: (value: boolean | React.Key, record: Task) => {
        if (value.toString() === 'group') return record.is_group;
        if (value.toString() === 'child') return !!record.parent_id;
        if (value.toString() === 'normal') return !record.is_group && !record.parent_id;
        if (value.toString() === 'automated') return record.task_type === 'Automated';
        if (value.toString() === 'manual') return record.task_type === 'Manual';
        return true;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color =
          status === 'Completed' ? 'green' :
          status === 'Running' ? 'blue' :
          status === 'Failed' ? 'red' :
          status === 'Pending' ? 'orange' : 'purple';
        return <Tag color={color}>{status.toUpperCase()}</Tag>;
      },
      filters: [
        { text: '未构建', value: 'Unbuilt' },
        { text: '等待中', value: 'Pending' },
        { text: '运行中', value: 'Running' },
        { text: '已完成', value: 'Completed' },
        { text: '失败', value: 'Failed' },
      ],
      onFilter: (value: boolean | React.Key, record: Task) => record.status === value.toString(),
    },
    {
      title: '上次执行',
      dataIndex: 'lastRun',
      key: 'lastRun',
      sorter: (a: Task, b: Task) => {
        if (!a.lastRun && !b.lastRun) return 0;
        if (!a.lastRun) return -1;
        if (!b.lastRun) return 1;
        return dayjs(a.lastRun).unix() - dayjs(b.lastRun).unix();
      },
      filters: [
        { text: '今天', value: 'today' },
        { text: '本周', value: 'week' },
        { text: '本月', value: 'month' },
        { text: '未执行', value: 'never' },
      ],
      onFilter: (value: boolean | React.Key, record: Task) => {
        if (value.toString() === 'never') return !record.lastRun;
        if (!record.lastRun) return false;

        const lastRunDate = dayjs(record.lastRun);
        const today = dayjs().startOf('day');

        if (value.toString() === 'today') return lastRunDate.isAfter(today);
        if (value.toString() === 'week') return lastRunDate.isAfter(dayjs().startOf('week'));
        if (value.toString() === 'month') return lastRunDate.isAfter(dayjs().startOf('month'));
        return true;
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" status={
          progress === 100 ? 'success' :
          progress === 0 ? 'normal' : 'active'
        } />
      ),
      sorter: (a: Task, b: Task) => a.progress - b.progress,
      filters: [
        { text: '未开始', value: '0' },
        { text: '进行中', value: 'inprogress' },
        { text: '已完成', value: '100' },
      ],
      onFilter: (value: boolean | React.Key, record: Task) => {
        if (value.toString() === '0') return record.progress === 0;
        if (value.toString() === 'inprogress') return record.progress > 0 && record.progress < 100;
        if (value.toString() === '100') return record.progress === 100;
        return true;
      },
    },
    {
      title: '执行机',
      dataIndex: 'agent_id',
      key: 'agent_id',
      render: (agent_id: string | undefined) => {
        if (!agent_id) return '-';
        // 查找对应的执行机名称
        const agent = agents.find(a => a.id === agent_id);
        return agent ? agent.name : agent_id;
      },
      filters: [
        { text: '未分配', value: 'unassigned' },
        ...agents.map(agent => ({ text: agent.name, value: agent.id })),
      ],
      onFilter: (value: boolean | React.Key, record: Task) => {
        if (value.toString() === 'unassigned') return !record.agent_id;
        return record.agent_id === value.toString();
      },
    },
    {
      title: '执行模式',
      dataIndex: 'execution_mode',
      key: 'execution_mode',
      render: (mode: string, record: Task) => {
        if (!record.is_group) return '-';
        return (
          <Tag color={mode === 'Serial' ? 'purple' : 'green'}>
            {mode === 'Serial' ? '串行' : '并行'}
          </Tag>
        );
      },
      filters: [
        { text: '串行', value: 'Serial' },
        { text: '并行', value: 'Parallel' },
      ],
      onFilter: (value: boolean | React.Key, record: Task) => record.is_group && record.execution_mode === value.toString(),
    },
    {
      title: '循环次数',
      dataIndex: 'loop_count',
      key: 'loop_count',
      filters: [
        { text: '单次执行', value: '1' },
        { text: '多次执行', value: 'multiple' },
        { text: '未设置', value: 'unset' },
      ],
      onFilter: (value: boolean | React.Key, record: Task) => {
        if (value.toString() === '1') return record.loop_count === 1;
        if (value.toString() === 'multiple') return record.loop_count !== undefined && record.loop_count > 1;
        if (value.toString() === 'unset') return record.loop_count === undefined;
        return true;
      },
    },
  ];

  /**
   * 启动任务
   * @param taskId 任务ID
   */
  const handleStartTask = async (taskId: string) => {
    try {
      const task = tasks.find(t => t.id === taskId);
      if (!task) return;

      if (task.is_group) {
        // 如果是任务组，根据执行模式启动子任务
        if (task.child_tasks && task.child_tasks.length > 0) {
          if (task.execution_mode === 'Parallel') {
            // 并行执行所有子任务
            await Promise.all(task.child_tasks.map(childId => invoke('start_task', { taskId: childId })));
            message.success(`任务组 ${task.name} 的所有子任务已并行启动`);
          } else {
            // 串行执行，先启动第一个子任务
            await invoke('start_task', { taskId: task.child_tasks[0] });
            message.success(`任务组 ${task.name} 开始串行执行，已启动第一个子任务`);
          }
        } else {
          message.warning('该任务组没有子任务');
        }
      } else {
        // 普通任务直接启动
        await invoke('start_task', { taskId });
        message.success('任务启动成功');
      }
      fetchTasks();
    } catch (error) {
      console.error('启动任务失败:', error);
      message.error('启动任务失败');
    }
  };

  const handleStopTask = async (taskId: string) => {
    try {
      await invoke('stop_task', { taskId });
      message.success('任务已停止');
      fetchTasks();
    } catch (error) {
      console.error('停止任务失败:', error);
      message.error('停止任务失败');
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此任务吗？',
      onOk: async () => {
        try {
          await invoke('delete_task', { taskId });
          message.success('任务删除成功');
          fetchTasks();
        } catch (error) {
          console.error('删除任务失败:', error);
          message.error('删除任务失败');
        }
      },
    });
  };

  /**
   * 编辑任务
   * @param task 要编辑的任务对象
   */
  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setIsModalVisible(true);
  };

  /**
   * 添加或更新任务
   * @param values 表单提交的任务数据
   */
  const handleAddOrUpdateTask = async (values: any) => {
    try {
      const now = new Date().toISOString();
      const taskData = {
        ...values,
        id: editingTask ? editingTask.id : crypto.randomUUID(),
        status: editingTask ? editingTask.status : 'Unbuilt',
        description: values.description || '',
        schedule: values.schedule || 'manual',
        steps: values.is_group ? [] : values.steps?.map((step: any, index: number) => ({
          ...step,
          order: index + 1
        })) || [],
        created_at: editingTask ? editingTask.created_at : now,
        updated_at: now,
        is_group: values.is_group || false,
        execution_mode: values.is_group ? values.execution_mode : undefined,
        child_tasks: values.is_group ? values.child_tasks : undefined,
        parent_id: values.parent_id || null
      };

      if (editingTask) {
        await invoke('update_task', { task: { ...editingTask, ...taskData } });
        message.success('任务更新成功');

        // 如果是任务组，更新子任务的parent_id
        if (taskData.is_group && taskData.child_tasks && taskData.child_tasks.length > 0) {
          await Promise.all(taskData.child_tasks.map(async (childId: string) => {
            const childTask = tasks.find(t => t.id === childId);
            if (childTask) {
              await invoke('update_task', {
                task: {
                  ...childTask,
                  parent_id: taskData.id,
                  updated_at: now
                }
              });
            }
          }));
        }
      } else {
        await invoke('create_task', { task: taskData });
        message.success('新任务添加成功');

        // 如果是任务组，更新子任务的parent_id
        if (taskData.is_group && taskData.child_tasks && taskData.child_tasks.length > 0) {
          await Promise.all(taskData.child_tasks.map(async (childId: string) => {
            const childTask = tasks.find(t => t.id === childId);
            if (childTask) {
              await invoke('update_task', {
                task: {
                  ...childTask,
                  parent_id: taskData.id,
                  updated_at: now
                }
              });
            }
          }));
        }
      }
      fetchTasks();
      setIsModalVisible(false);
      setEditingTask(null);
    } catch (error) {
      console.error('保存任务失败:', error);
      message.error('保存任务失败');
    }
  };

  /**
   * 删除任务
   * @param taskToDelete 要删除的任务对象
   */
  const handleDelete = async (taskToDelete: Task) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除任务 "${taskToDelete.name}" 吗？`,
      onOk: async () => {
        try {
          await invoke('delete_task', { taskId: taskToDelete.id });
          message.success('任务删除成功');
          fetchTasks();
        } catch (error) {
          console.error('删除任务失败:', error);
          message.error('删除任务失败');
        }
      },
    });
  };

  /**
   * 复制任务
   * @param task 要复制的任务对象
   */
  const handleCopy = async (task: Task) => {
    try {
      const newTask = {
        ...task,
        id: crypto.randomUUID(),
        name: `${task.name} (复制)`,
        status: 'Unbuilt'
      };
      await invoke('create_task', { task: newTask });
      message.success(`已复制任务: ${task.name}`);
      fetchTasks();
    } catch (error) {
      console.error('复制任务失败:', error);
      message.error('复制任务失败');
    }
  };

  /**
   * 批量启动任务
   */
  const handleBatchStart = async () => {
    try {
      await Promise.all(selectedTaskIds.map(id => invoke('start_task', { taskId: id })));
      message.success(`正在批量启动 ${selectedTaskIds.length} 个任务`);
      fetchTasks();
    } catch (error) {
      console.error('批量启动任务失败:', error);
      message.error('批量启动任务失败');
    }
  };

  /**
   * 批量停止任务
   */
  const handleBatchStop = async () => {
    try {
      await Promise.all(selectedTaskIds.map(id => invoke('stop_task', { taskId: id })));
      message.success(`正在批量停止 ${selectedTaskIds.length} 个任务`);
      fetchTasks();
    } catch (error) {
      console.error('批量停止任务失败:', error);
      message.error('批量停止任务失败');
    }
  };

  /**
   * 批量删除任务
   */
  const handleBatchDelete = async () => {
    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedTaskIds.length} 个任务吗？`,
      onOk: async () => {
        try {
          await Promise.all(selectedTaskIds.map(id => invoke('delete_task', { taskId: id })));
          message.success('批量删除成功');
          fetchTasks();
          setSelectedTaskIds([]);
        } catch (error) {
          console.error('批量删除任务失败:', error);
          message.error('批量删除任务失败');
        }
      },
    });
  };

  const rowSelection = {
    selectedRowKeys: selectedTaskIds,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedTaskIds(selectedRowKeys as string[]);
    },
  };

  return (
    <div>
      <Space className="compact-actions" style={{ marginBottom: 8 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingTask(null);
            setIsModalVisible(true);
          }}
          size="small"
        >
          新建任务
        </Button>
        <Button
          icon={<CopyOutlined />}
          onClick={() => {
            const selectedTasks = tasks.filter(task => selectedTaskIds.includes(task.id));
            selectedTasks.forEach(handleCopy);
          }}
          disabled={selectedTaskIds.length === 0}
          size="small"
        >
          复制
        </Button>
        <Button
          icon={<PlayCircleOutlined />}
          onClick={handleBatchStart}
          disabled={selectedTaskIds.length === 0}
          size="small"
        >
          启动
        </Button>
        <Button
          icon={<StopOutlined />}
          onClick={handleBatchStop}
          disabled={selectedTaskIds.length === 0}
          size="small"
        >
          停止
        </Button>
        <Button
          icon={<EditOutlined />}
          onClick={() => {
            if (selectedTaskIds.length === 1) {
              const task = tasks.find(t => t.id === selectedTaskIds[0]);
              if (task) handleEditTask(task);
            } else {
              message.warning('请选择一个任务进行编辑');
            }
          }}
          disabled={selectedTaskIds.length !== 1}
          size="small"
        >
          编辑
        </Button>
        <Button
          icon={<DeleteOutlined />}
          onClick={handleBatchDelete}
          disabled={selectedTaskIds.length === 0}
          size="small"
        >
          删除
        </Button>
      </Space>
      <Table
        className="compact-table"
        rowSelection={rowSelection}
        columns={columns}
        dataSource={tasks}
        rowKey="id"
        loading={loading}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100']
        }}
        size="small"
      />
      <Modal
        title={editingTask ? "编辑任务" : "新建任务"}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingTask(null);
        }}
        footer={null}
      >
        <TaskForm
          initialValues={editingTask}
          onSubmit={handleAddOrUpdateTask}
          onCancel={() => {
            setIsModalVisible(false);
            setEditingTask(null);
          }}
        />
      </Modal>
    </div>
  );
};

export default TaskManagement;