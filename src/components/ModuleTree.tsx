import React, { useState, useEffect } from 'react';
import type { Key } from 'antd/es/table/interface';
import type { EventDataNode } from 'antd/es/tree';
import { Tree, Input, Button, Dropdown, Menu, message } from 'antd';
import { FolderOutlined, SearchOutlined, EllipsisOutlined } from '@ant-design/icons';
import type { DataNode } from 'antd/es/tree';
import { invoke } from '@tauri-apps/api/core';

interface ModuleNode {
  id: string;
  name: string;
  parentId: string | null;
  children?: ModuleNode[];
}

interface ModuleTreeProps {
  onSelect: (selectedModuleId: string) => void;
}

const ModuleTree: React.FC<ModuleTreeProps> = ({ onSelect }) => {
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  const fetchModules = async () => {
    try {
      const modules = await invoke('get_modules') as ModuleNode[];
      const formattedData = formatTreeData(modules);
      setTreeData(formattedData);
    } catch (error) {
      message.error('获取模块列表失败：' + error);
    }
  };

  useEffect(() => {
    fetchModules();
  }, []);

  const formatTreeData = (modules: ModuleNode[]): DataNode[] => {
    const buildTree = (items: ModuleNode[], parentId: string | null = null): DataNode[] => {
      return items
        .filter(item => item.parentId === parentId)
        .map(item => ({
          key: item.id,
          title: item.name,
          icon: <FolderOutlined />,
          children: buildTree(items, item.id)
        }));
    };

    return buildTree(modules);
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    const expandedKeys = treeData
      .map(item => {
        if (item.title?.toString().toLowerCase().includes(value.toLowerCase())) {
          return item.key;
        }
        return null;
      })
      .filter((key): key is string => key !== null);

    setExpandedKeys(expandedKeys);
    setAutoExpandParent(true);
  };

  const handleExpand = (expandedKeys: Key[], info: { node: EventDataNode<DataNode>; expanded: boolean; nativeEvent: MouseEvent; }) => {
    setExpandedKeys(expandedKeys.map(key => key.toString()));
    setAutoExpandParent(false);
  };

  const handleModuleAction = async (action: string, moduleId: string) => {
    try {
      switch (action) {
        case 'add':
          await invoke('create_module', { parentId: moduleId, name: '新建模块' });
          break;
        case 'edit':
          // TODO: Implement edit module name
          break;
        case 'delete':
          await invoke('delete_module', { id: moduleId });
          break;
      }
      fetchModules();
    } catch (error) {
      message.error('操作失败：' + error);
    }
  };

  const getModuleMenu = (moduleId: string) => (
    <Menu
      items={[
        {
          key: 'add',
          label: '添加子模块',
          onClick: () => handleModuleAction('add', moduleId)
        },
        {
          key: 'edit',
          label: '编辑模块',
          onClick: () => handleModuleAction('edit', moduleId)
        },
        {
          key: 'delete',
          label: '删除模块',
          onClick: () => handleModuleAction('delete', moduleId)
        }
      ]}
    />
  );

  const titleRender = (nodeData: DataNode): React.ReactNode => (
    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
      <span>{String(nodeData.title)}</span>
      <Dropdown overlay={getModuleMenu(nodeData.key as string)} trigger={['click']}>
        <Button type="text" icon={<EllipsisOutlined />} size="small" />
      </Dropdown>
    </div>
  );

  return (
    <div style={{ padding: '16px' }}>
      <Input
        placeholder="搜索模块"
        prefix={<SearchOutlined />}
        onChange={e => handleSearch(e.target.value)}
        style={{ marginBottom: '16px' }}
      />
      <Tree
        treeData={treeData}
        onSelect={(selectedKeys) => {
          if (selectedKeys.length > 0) {
            onSelect(selectedKeys[0] as string);
          }
        }}
        titleRender={titleRender}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        onExpand={handleExpand}
      />
    </div>
  );
};

export default ModuleTree;