import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Layout, Menu, Spin, message, notification } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DatabaseOutlined,
  BarsOutlined,
  DesktopOutlined,
  RobotOutlined,
  ApiOutlined,
  SettingOutlined,
  ProjectOutlined,
} from '@ant-design/icons';
import { WebviewWindow } from '@tauri-apps/api/webviewWindow';
import './App.css';
import Dashboard from './components/Dashboard';
import TaskManagement from './components/TaskManagement';
import ExecutorManagement from './components/ExecutorManagement';
import TestCaseManagement from './components/TestCaseManagement';
import ErrorBoundary from './components/ErrorBoundary';
import AIChat from './components/chat/AIChat';
import APIConfigManagement from './components/APIConfigManagement';
import ProjectManagement from './components/project/ProjectManagement';
import { invoke } from '@tauri-apps/api/core';
import { ChatConversation, ApiConfig } from './types'; // Import types

const { Header, Sider, Content } = Layout;

function App() {
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState('test-cases');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<ChatConversation | null>(null);
  const [apiConfigs, setApiConfigs] = useState<ApiConfig[]>([]);
  const errorNotificationRef = useRef<React.Key | null>(null);

  // 全局错误处理
  const handleGlobalError = useCallback((event: ErrorEvent) => {
    console.error('Global error caught:', event.error);

    // 关闭之前的错误通知
    if (errorNotificationRef.current) {
      notification.destroy();
    }

    // 显示错误通知
    const key = `error-${Date.now()}`;
    errorNotificationRef.current = key;

    notification.error({
      message: '应用错误',
      description: (
        <div>
          <p><strong>错误信息：</strong> {event.error?.message || event.message}</p>
          <p>请尝试刷新页面或重新启动应用</p>
        </div>
      ),
      duration: 0, // 不自动关闭
      key,
    });

    // 防止默认错误处理
    event.preventDefault();
  }, []);

  // 处理未捕获的Promise错误
  const handleUnhandledRejection = useCallback((event: PromiseRejectionEvent) => {
    console.error('Unhandled Promise rejection:', event.reason);

    // 关闭之前的错误通知
    if (errorNotificationRef.current) {
      notification.destroy();
    }

    // 显示错误通知
    const key = `promise-error-${Date.now()}`;
    errorNotificationRef.current = key;

    notification.error({
      message: 'Promise错误',
      description: (
        <div>
          <p><strong>错误信息：</strong> {event.reason?.message || String(event.reason)}</p>
          <p>请尝试刷新页面或重新启动应用</p>
        </div>
      ),
      duration: 0, // 不自动关闭
      key,
    });

    // 防止默认错误处理
    event.preventDefault();
  }, []);

  // 添加全局错误监听
  useEffect(() => {
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [handleGlobalError, handleUnhandledRejection]);

  // Fetch initial data (conversations and API configs) with timeout
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);

      // 使用Promise.race实现超时处理
      const timeout = (ms: number) => new Promise((_, reject) =>
        setTimeout(() => reject(new Error(`操作超时 (${ms}ms)`)), ms)
      );

      // 设置5秒超时
      const fetchWithTimeout = async () => {
        try {
          return await Promise.race([
            Promise.all([
              invoke<ChatConversation[]>('get_conversations').catch(e => {
                console.warn('获取对话列表失败，返回空数组:', e);
                return [] as ChatConversation[];
              }),
              invoke<ApiConfig[]>('get_api_configs').catch(e => {
                console.warn('获取API配置失败，返回空数组:', e);
                return [] as ApiConfig[];
              })
            ]),
            timeout(5000) // 5秒超时
          ]);
        } catch (e) {
          console.warn('数据获取超时，使用空数据继续:', e);
          return [[] as ChatConversation[], [] as ApiConfig[]];
        }
      };

      const [fetchedConversations, fetchedApiConfigs] = await fetchWithTimeout();

      // 即使数据获取失败，也不会阻止应用初始化
      setConversations(fetchedConversations || []);
      setApiConfigs(fetchedApiConfigs || []);

      // Optionally select the first conversation if none is selected
      if (!currentConversation && fetchedConversations && fetchedConversations.length > 0) {
        setCurrentConversation(fetchedConversations[0]);
      }

      setError(null);
    } catch (err: any) {
      console.error('Failed to fetch initial data:', err);
      // 不显示错误消息，只记录日志，允许应用继续运行
      console.warn('使用空数据继续初始化');
      setConversations([]);
      setApiConfigs([]);
    } finally {
      // 确保无论如何都会关闭加载状态
      setLoading(false);
    }
  }, [currentConversation]); // Depend on currentConversation to avoid re-selecting first on refresh

  useEffect(() => {
    // 添加超时保护，确保即使初始化过程卡住，也会在一定时间后解除加载状态
    let initTimeout: NodeJS.Timeout | null = null;

    const initializeApp = async () => {
      try {
        setLoading(true);

        // 设置10秒超时，确保不会永久卡在加载状态
        initTimeout = setTimeout(() => {
          console.warn('应用初始化超时，强制解除加载状态');
          setLoading(false);
        }, 10000);

        // 尝试居中窗口，但不会因为失败而中断初始化
        // 使用Promise.race添加超时处理
        const initWithTimeout = async () => {
          const timeout = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

          // 3秒后自动继续，不管centerWindow是否完成
          await Promise.race([
            centerWindow().catch(e => {
              console.warn('窗口居中失败，但继续初始化:', e);
            }),
            timeout(3000)
          ]);
        };

        await initWithTimeout();

        // 清除超时定时器
        if (initTimeout) {
          clearTimeout(initTimeout);
          initTimeout = null;
        }

        setLoading(false);
      } catch (err) {
        console.error('应用初始化过程中出现错误，但将继续执行:', err);
        // 不设置错误状态，允许应用继续初始化
        setLoading(false);
      }
    };

    const centerWindow = async () => {
      try {
        // 在Tauri 2.x中，我们需要先获取窗口实例
        const mainWindow = WebviewWindow.getByLabel('main');
        if (mainWindow) {
          // 使用await解析Promise
          const window = await mainWindow;
          if (window) {
            try {
              await window.center();
            } catch (centerError) {
              console.warn('窗口居中失败，但这不是致命错误，继续执行:', centerError);
              // 不抛出错误，允许应用继续初始化
            }
          }
        }
      } catch (error) {
        console.error('获取窗口实例失败:', error);
        // 不抛出错误，允许应用继续初始化
      }
    };

    initializeApp();
    fetchData(); // Fetch data on initial load

    let unlistenResize: (() => void) | undefined;

    // 在Tauri 2.x中，我们需要先获取窗口实例
    // 注意：我们不再尝试监听resize事件，因为这可能导致权限问题
    // 如果需要在窗口大小变化时执行操作，可以使用React的useEffect和窗口的resize事件

    // 添加窗口resize事件监听
    const handleResize = () => {
      console.log('窗口大小已改变');
      // 这里可以添加窗口大小变化时需要执行的操作
    };

    window.addEventListener('resize', handleResize);

    return () => {
      // 清除所有定时器和事件监听器
      window.removeEventListener('resize', handleResize);
      if (unlistenResize) unlistenResize();

      // 清除初始化超时定时器
      if (initTimeout) {
        clearTimeout(initTimeout);
        initTimeout = null;
      }
    };
  }, [fetchData]); // Add fetchData to dependency array

  const toggle = () => {
    setCollapsed(!collapsed);
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <Spin size="large">
            <div style={{ padding: '50px', textAlign: 'center' }}>
              <p>加载中...</p>
            </div>
          </Spin>
        </div>
      );
    }

    if (error) {
      return (
        <div style={{ textAlign: 'center', padding: '20px', color: 'red' }}>
          <h2>出错了</h2>
          <p>{error}</p>
        </div>
      );
    }

    switch (selectedKey) {
      case 'test-cases':
        return <TestCaseManagement />;
      case 'tasks':
        return <TaskManagement />;
      case 'executors':
        return <ExecutorManagement />;
      case 'ai-chat':
        // Pass necessary props to AIChat
        return (
          <AIChat
            currentConversation={currentConversation}
            setCurrentConversation={setCurrentConversation} // Pass setter
            apiConfigs={apiConfigs}
            refreshConversations={fetchData} // Pass refresh function
          />
        );
      case 'api-config':
        // Pass necessary props to APIConfigManagement
        return (
          <APIConfigManagement
            apiConfigs={apiConfigs}
            refreshApiConfigs={fetchData}
          />
        );
      case 'projects':
        return <ProjectManagement />;
      default:
        return null;
    }
  };

  const menuItems = [
    {
      key: 'test-cases',
      icon: <DatabaseOutlined />,
      label: '用例管理',
    },
    {
      key: 'ai-chat',
      icon: <RobotOutlined />,
      label: '智能对话',
    },
    {
      key: 'api-config',
      icon: <ApiOutlined />,
      label: 'API配置',
    },
    {
      key: 'tasks',
      icon: <BarsOutlined />,
      label: '任务管理',
    },
    {
      key: 'executors',
      icon: <DesktopOutlined />,
      label: '执行机管理',
    },
    {
      key: 'projects',
      icon: <ProjectOutlined />,
      label: '项目管理',
    },
  ];

  return (
    <ErrorBoundary>
      <Layout style={{ minHeight: '100vh' }}>
        <Sider trigger={null} collapsible collapsed={collapsed}>
          <div className="logo" onClick={toggle} style={{ cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <span style={{ marginRight: collapsed ? '0' : '8px' }}>Tasker</span>
            {collapsed ? <MenuUnfoldOutlined style={{ fontSize: '14px' }} /> : <MenuFoldOutlined style={{ fontSize: '14px' }} />}
          </div>
          <Menu
            theme="dark"
            mode="inline"
            defaultSelectedKeys={['1']}
            selectedKeys={[selectedKey]} // Control selected key
            onClick={({ key }) => {
              setSelectedKey(key);
              // If switching away from chat, maybe clear currentConversation?
              // if (key !== 'ai-chat') {
              //   setCurrentConversation(null);
              // }
            }}
            items={menuItems}
          />
        </Sider>
        <Layout className="site-layout">
          {selectedKey !== 'projects' && (
            <Header className="site-layout-background" style={{ padding: 0 }}>
              <div style={{ padding: '0 24px' }}>
                {selectedKey === 'ai-chat' ? '智能对话' :
                 selectedKey === 'api-config' ? 'API配置' :
                 selectedKey === 'tasks' ? '任务管理' :
                 selectedKey === 'executors' ? '执行机管理' : '用例管理'}
              </div>
            </Header>
          )}
          <Content
            className="site-layout-background"
            style={{
              margin: selectedKey === 'projects' ? '0' : '24px 16px',
              padding: selectedKey === 'projects' ? 0 : 24,
              minHeight: 280,
              height: selectedKey === 'projects' ? 'calc(100vh - 64px)' : 'auto',
            }}
          >
            {renderContent()}
          </Content>
        </Layout>
      </Layout>
    </ErrorBoundary>
  );
}

export default App;