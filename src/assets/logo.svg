<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4285F4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34A853;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect x="20" y="20" rx="10" ry="10" width="160" height="160" fill="url(#grad1)" />
  <circle cx="100" cy="100" r="60" fill="white" opacity="0.2" />
  <path d="M70,80 L130,80 L130,90 L70,90 Z" fill="white" />
  <path d="M70,100 L130,100 L130,110 L70,110 Z" fill="white" />
  <path d="M70,120 L110,120 L110,130 L70,130 Z" fill="white" />
  <text x="100" y="170" font-family="Arial" font-size="20" text-anchor="middle" fill="white">Taskers</text>
</svg>
