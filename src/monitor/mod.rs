use log::info;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use std::thread;
use std::time::{Duration, Instant};
use sysinfo::{System, CpuRefreshKind, ProcessRefreshKind, RefreshKind};

/// 系统资源监控模块
pub struct ResourceMonitor {
    running: Arc<AtomicBool>,
}

impl ResourceMonitor {
    /// 创建新的资源监控器
    pub fn new() -> Self {
        ResourceMonitor {
            running: Arc::new(AtomicBool::new(false)),
        }
    }

    /// 启动资源监控
    pub fn start(&self) {
        if self.running.load(Ordering::SeqCst) {
            info!("资源监控已经在运行中");
            return;
        }

        self.running.store(true, Ordering::SeqCst);
        let running = Arc::clone(&self.running);

        thread::spawn(move || {
            info!("资源监控线程已启动");
            
            // 初始化系统信息收集器，设置需要刷新的信息类型
            let refresh_kind = RefreshKind::new()
                .with_cpu(CpuRefreshKind::everything())
                .with_memory()
                .with_processes(ProcessRefreshKind::everything());
            let mut sys = System::new_with_specifics(refresh_kind);
            
            while running.load(Ordering::SeqCst) {
                // 收集并记录系统资源信息
                Self::collect_and_log_resources(&mut sys);
                
                // 等待3分钟
                let start = Instant::now();
                while start.elapsed() < Duration::from_secs(180) && running.load(Ordering::SeqCst) {
                    thread::sleep(Duration::from_secs(1));
                }
            }
            
            info!("资源监控线程已停止");
        });
        
        info!("资源监控已启动");
    }

    /// 停止资源监控
    pub fn stop(&self) {
        self.running.store(false, Ordering::SeqCst);
        info!("资源监控已停止");
    }

    /// 收集并记录系统资源信息
    fn collect_and_log_resources(sys: &mut System) {
        // 刷新系统信息
        sys.refresh_all();
        
        // 收集CPU信息
        let cpu_count = sys.cpus().len();
        let mut cpu_details = String::new();
        let mut global_cpu_usage = 0.0;
        
        for (i, cpu) in sys.cpus().iter().enumerate() {
            let usage = cpu.cpu_usage();
            global_cpu_usage += usage;
            cpu_details.push_str(&format!("CPU{}: {:.1}% ", i, usage));
        }
        
        if cpu_count > 0 {
            global_cpu_usage /= cpu_count as f32;
        }
        
        // 收集内存信息
        let total_memory = sys.total_memory() / 1024 / 1024; // MB
        let used_memory = sys.used_memory() / 1024 / 1024; // MB
        let memory_usage_percent = if total_memory > 0 {
            (used_memory as f64 / total_memory as f64) * 100.0
        } else {
            0.0
        };
        
        // 收集当前进程信息
        let current_pid = sysinfo::Pid::from_u32(std::process::id());
        
        let process_info = if let Some(process) = sys.process(current_pid) {
            format!(
                "进程内存: {:.1} MB, CPU使用率: {:.1}%",
                process.memory() / 1024 / 1024,
                process.cpu_usage()
            )
        } else {
            "无法获取当前进程信息".to_string()
        };
        
        // 记录到日志
        info!("=== 系统资源监控 ===");
        info!("CPU: 全局使用率 {:.1}%, 核心数: {}", global_cpu_usage, cpu_count);
        info!("CPU详情: {}", cpu_details);
        info!("内存: 已用 {} MB / 总计 {} MB ({:.1}%)", used_memory, total_memory, memory_usage_percent);
        info!("当前进程: {}", process_info);
        info!("===================");
    }
}

/// 初始化资源监控器并启动
pub fn init_resource_monitor() -> ResourceMonitor {
    let monitor = ResourceMonitor::new();
    monitor.start();
    monitor
}
