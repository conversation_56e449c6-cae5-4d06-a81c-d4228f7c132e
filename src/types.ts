// 定义聊天消息类型
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  created_at: string;
  conversation_id: string;
}

// 定义聊天会话类型
export interface ChatConversation {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  provider: string;
  model: string;
  topic?: string;
}

// 定义API配置类型
export interface ApiConfig {
  id?: string; // 可选，有些地方不需要id
  provider: string;
  api_key: string;
  base_url: string; // 与后端保持一致的字段名
  api_url?: string; // 为了兼容性保留
  models: string[];
}

// 定义测试用例优先级
export type TestCasePriority = 'High' | 'Medium' | 'Low';

// 定义测试用例状态
export type TestCaseStatus = 'NotStarted' | 'InProgress' | 'Passed' | 'Failed' | 'Blocked';

// 项目状态
export type ProjectStatus = 'Active' | 'Inactive' | 'Completed' | 'Archived';

// 项目
export interface Project {
  id: string;
  name: string;
  description?: string;
  status: ProjectStatus;
  created_at: string;
  updated_at: string;
  created_by: string;
  leader?: string;
  tags?: string;
}

// 项目文件
export interface ProjectFile {
  id: string;
  project_id: string;
  name: string;
  path: string;
  content?: string;
  size: number;
  is_directory: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
}

// 任务状态
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed';

// 任务
export interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  agent_id?: string;
  steps?: TaskStep[];
}

// 任务步骤
export interface TaskStep {
  id: string;
  task_id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  order: number;
}

// 代理
export interface Agent {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  capabilities: string[];
}

// 构建记录
export interface BuildRecord {
  id: string;
  project_id: string;
  version: string;
  status: string;
  created_at: string;
  completed_at?: string;
  log?: string;
  artifacts?: string[];
}
