[package]
name = "taskers"
version = "0.1.0"
description = "A task management application"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

[lib]
name = "taskers_lib"
crate-type = ["staticlib", "cdylib", "rlib"]
path = "src/lib.rs"

[[bin]]
name = "taskers"
path = "src/main.rs"

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }

[dependencies]
# 数据库
rusqlite = { version = "0.30.0", features = ["bundled"] }
r2d2 = "0.8"
r2d2_sqlite = "0.23"

# 序列化/反序列化
uuid = { version = "1.8", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Tauri
tauri = { version = "2.0.0-beta", features = [] }

# 异步
tokio = { version = "1", features = ["full"] }
futures = "0.3"
async-trait = "0.1"

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 日志
log = "0.4"
env_logger = "0.11"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 工具
cron = "0.12"
calamine = "0.24"
reqwest = { version = "0.12", features = ["json"] }
ssh2 = { version = "0.9", features = ["vendored-openssl"] }
libc = "0.2"

# 并发和缓存
once_cell = "1.19"
parking_lot = "0.12"
moka = { version = "0.12", features = ["future"] }

# 其他
lazy_static = "1.4"
sysinfo = "0.30.5"

# SSH Dependencies (if used)
rush = "0.1.0-alpha.7"
# tokio is already defined above, remove this duplicate
# tokio = { version = "1", features = ["full"] } # Ensure tokio is present and full features enabled

# Local PTY Dependencies <-- 新增
portable-pty = "0.7.0"


[features]
custom-protocol = ["tauri/custom-protocol"]
