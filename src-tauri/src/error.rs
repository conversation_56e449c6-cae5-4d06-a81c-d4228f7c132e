// src-tauri/src/error.rs
use thiserror::Error;
use std::fmt;

/// 应用程序错误类型
#[derive(Error, Debug)]
pub enum AppError {
    #[error("数据库错误: {0}")]
    Database(#[from] DbError),

    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    #[error("SSH错误: {0}")]
    Ssh(#[from] SshError),

    #[error("项目错误: {0}")]
    Project(#[from] ProjectError),

    #[error("任务错误: {0}")]
    Task(#[from] TaskError),

    #[error("执行机错误: {0}")]
    Agent(#[from] AgentError),

    #[error("AI聊天错误: {0}")]
    AiChat(#[from] AiChatError),

    #[error("测试用例错误: {0}")]
    TestCase(#[from] TestCaseError),

    #[error("未知错误: {0}")]
    Unknown(String),
}

/// 数据库错误类型
#[derive(Error, Debug)]
pub enum DbError {
    #[error("SQLite错误: {0}")]
    Sqlite(#[from] rusqlite::Error),

    #[error("记录不存在: {0}")]
    NotFound(String),

    #[error("无效输入: {0}")]
    InvalidInput(String),

    #[error("连接失败: {0}")]
    ConnectionFailed(String),

    #[error("查询失败: {0}")]
    QueryFailed(String),

    #[error("事务失败: {0}")]
    TransactionFailed(String),
}

/// SSH错误类型
#[derive(Error, Debug)]
pub enum SshError {
    #[error("连接失败: {0}")]
    ConnectionFailed(String),

    #[error("认证失败: {0}")]
    AuthenticationFailed(String),

    #[error("命令执行失败: {0}")]
    CommandFailed(String),

    #[error("文件传输失败: {0}")]
    FileTransferFailed(String),

    #[error("会话错误: {0}")]
    SessionError(String),

    #[error("SSH库错误: {0}")]
    LibraryError(#[from] ssh2::Error),
}

/// 项目错误类型
#[derive(Error, Debug)]
pub enum ProjectError {
    #[error("项目不存在: {0}")]
    NotFound(String),

    #[error("文件不存在: {0}")]
    FileNotFound(String),

    #[error("文件已存在: {0}")]
    FileExists(String),

    #[error("无效请求: {0}")]
    InvalidRequest(String),

    #[error("操作失败: {0}")]
    OperationFailed(String),

    #[error("数据库错误: {0}")]
    Database(#[from] DbError),
}

/// 任务错误类型
#[derive(Error, Debug)]
pub enum TaskError {
    #[error("任务不存在: {0}")]
    NotFound(String),

    #[error("任务已存在: {0}")]
    AlreadyExists(String),

    #[error("无效状态: {0}")]
    InvalidStatus(String),

    #[error("执行失败: {0}")]
    ExecutionFailed(String),

    #[error("调度失败: {0}")]
    SchedulingFailed(String),

    #[error("数据库错误: {0}")]
    Database(#[from] DbError),
}

/// 执行机错误类型
#[derive(Error, Debug)]
pub enum AgentError {
    #[error("执行机不存在: {0}")]
    NotFound(String),

    #[error("执行机已存在: {0}")]
    AlreadyExists(String),

    #[error("连接失败: {0}")]
    ConnectionFailed(String),

    #[error("认证失败: {0}")]
    AuthenticationFailed(String),

    #[error("无效状态: {0}")]
    InvalidStatus(String),

    #[error("数据库错误: {0}")]
    Database(#[from] DbError),

    #[error("SSH错误: {0}")]
    Ssh(#[from] SshError),
}

/// AI聊天错误类型
#[derive(Error, Debug)]
pub enum AiChatError {
    #[error("会话不存在: {0}")]
    ConversationNotFound(String),

    #[error("消息不存在: {0}")]
    MessageNotFound(String),

    #[error("API配置不存在: {0}")]
    ApiConfigNotFound(String),

    #[error("API请求失败: {0}")]
    ApiRequestFailed(String),

    #[error("无效请求: {0}")]
    InvalidRequest(String),

    #[error("数据库错误: {0}")]
    Database(#[from] DbError),
}

/// 测试用例错误类型
#[derive(Error, Debug)]
pub enum TestCaseError {
    #[error("测试用例不存在: {0}")]
    NotFound(String),

    #[error("测试用例已存在: {0}")]
    AlreadyExists(String),

    #[error("导入失败: {0}")]
    ImportFailed(String),

    #[error("无效格式: {0}")]
    InvalidFormat(String),

    #[error("数据库错误: {0}")]
    Database(#[from] DbError),
}

/// 将错误转换为字符串响应
pub fn to_string_error<E: fmt::Display>(err: E) -> String {
    err.to_string()
}

/// 结果类型别名，用于Tauri命令
pub type CommandResult<T> = Result<T, String>;

/// 将Result<T, E>转换为CommandResult<T>
pub fn to_command_result<T, E: fmt::Display>(result: Result<T, E>) -> CommandResult<T> {
    result.map_err(to_string_error)
}
