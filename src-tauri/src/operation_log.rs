// src-tauri/src/operation_log.rs
use serde::{Serialize, Deserialize};
use crate::models::DbDateTime;

/// 操作类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum OperationType {
    Command,
    FileTransfer,
    TaskExecution,
    AgentManagement,
    SystemOperation,
}

/// 操作状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum OperationStatus {
    Success,
    Failed,
    Pending,
}

/// 操作日志
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OperationLog {
    pub id: String,
    pub agent_id: String,
    pub operation_type: OperationType,
    pub description: String,
    pub status: OperationStatus,
    pub created_at: DbDateTime,
}
