use tauri::State;
use crate::models::{Task, TaskStatus, AppState, Agent, AgentStatus}; // Removed unused Project
use crate::db::DbManager;
use log::{error, info};
use std::sync::Arc;
use crate::operation_log::{OperationLog, OperationType, OperationStatus};
use crate::logger;
use crate::ssh_manager::SshManager;
use crate::pty_manager::PtyManager;
use std::sync::Mutex as StdMutex;
use uuid::Uuid;
use chrono::Utc;

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn get_tasks(state: State<'_, AppState>) -> Result<Vec<Task>, String> {
    info!("Fetching tasks");
    match state.db.get_tasks().await {
        Ok(tasks) => {
            info!("Successfully fetched {} tasks", tasks.len());
            Ok(tasks)
        },
        Err(e) => {
            error!("Failed to fetch tasks: {}", e);
            Err(e.to_string())
        }
    }
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn create_task(state: State<'_, AppState>, task: Task) -> Result<(), String> {
    state.db.create_task(&task)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn update_task(state: State<'_, AppState>, task: Task) -> Result<(), String> {
    state.db.update_task(&task)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn delete_task(state: State<'_, AppState>, task_id: String) -> Result<(), String> {
    state.db.delete_task(task_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn get_agents(state: State<'_, AppState>) -> Result<Vec<Agent>, String> {
    state.db.get_agents()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn run_task(state: State<'_, AppState>, task_id: String) -> Result<(), String> {
    info!("Running task with id: {}", task_id);
    let tasks = state.db.get_tasks().await.map_err(|e| e.to_string())?;
    if let Some(mut task) = tasks.into_iter().find(|t| t.id == task_id) {
        // 更新任务状态为运行中
        task.status = TaskStatus::Running;
        state.db.update_task(&task).await.map_err(|e| e.to_string())?;

        // 在后台线程中执行任务
        let db = state.db.clone();
        let mut task_clone = task.clone();

        // 获取循环次数
        let loop_count = task_clone.loop_count.unwrap_or(1);
        info!("任务 {} 将循环执行 {} 次", task_clone.name, loop_count);

        tokio::spawn(async move {
            let executor = crate::executor::Executor::new();

            // 如果是任务组，根据执行模式处理
            if task_clone.is_group {
                info!("执行任务组: {}", task_clone.name);

                // 检查是否有子任务
                if let Some(child_tasks) = &task_clone.child_tasks {
                    if child_tasks.is_empty() {
                        info!("任务组 {} 没有子任务，标记为完成", task_clone.name);
                        task_clone.status = TaskStatus::Completed;
                        if let Err(e) = db.update_task(&task_clone).await {
                            error!("Failed to update task group status: {}", e);
                        }
                        return;
                    }

                    // 根据执行模式执行子任务
                    let execution_mode = task_clone.execution_mode.as_ref().unwrap_or(&crate::models::ExecutionMode::Serial);

                    match execution_mode {
                        crate::models::ExecutionMode::Parallel => {
                            info!("任务组 {} 使用并行模式执行子任务", task_clone.name);

                            // 并行执行所有子任务
                            for _ in 0..loop_count {
                                for child_id in child_tasks {
                                    let child_id_clone = child_id.clone();
                                    let db_clone = db.clone();

                                    tokio::spawn(async move {
                                        if let Err(e) = run_single_task(&db_clone, &child_id_clone).await {
                                            error!("子任务 {} 执行失败: {}", child_id_clone, e);
                                        }
                                    });
                                }
                            }

                            // 更新任务组状态
                            task_clone.status = TaskStatus::Completed;
                            if let Err(e) = db.update_task(&task_clone).await {
                                error!("Failed to update task group status: {}", e);
                            }
                        },
                        crate::models::ExecutionMode::Serial => {
                            info!("任务组 {} 使用串行模式执行子任务", task_clone.name);

                            let mut all_success = true;

                            // 循环执行指定次数
                            'outer: for loop_index in 0..loop_count {
                                info!("任务组 {} 开始第 {}/{} 次循环执行", task_clone.name, loop_index + 1, loop_count);

                                // 串行执行所有子任务
                                for (i, child_id) in child_tasks.iter().enumerate() {
                                    info!("串行执行子任务 {}/{}: {}", i + 1, child_tasks.len(), child_id);

                                    // 执行子任务
                                    if let Err(e) = run_single_task(&db, child_id).await {
                                        error!("子任务 {} 执行失败: {}", child_id, e);
                                        all_success = false;
                                        break 'outer;
                                    }
                                }

                                info!("任务组 {} 第 {}/{} 次循环执行完成", task_clone.name, loop_index + 1, loop_count);
                            }

                            // 更新任务组状态
                            task_clone.status = if all_success { TaskStatus::Completed } else { TaskStatus::Failed };
                            if let Err(e) = db.update_task(&task_clone).await {
                                error!("Failed to update task group status: {}", e);
                            }
                        }
                    }
                } else {
                    info!("任务组 {} 没有子任务，标记为完成", task_clone.name);
                    task_clone.status = TaskStatus::Completed;
                    if let Err(e) = db.update_task(&task_clone).await {
                        error!("Failed to update task group status: {}", e);
                    }
                }
            } else {
                // 普通任务执行
                for i in 0..loop_count {
                    if loop_count > 1 {
                        info!("执行任务 {} 第 {}/{} 次循环", task_clone.name, i + 1, loop_count);
                    }

                    match executor.execute_task(&mut task_clone).await {
                        Ok(_) => {
                            info!("Task {} completed successfully", task_clone.name);
                            task_clone.status = TaskStatus::Completed;
                        },
                        Err(e) => {
                            error!("Task {} failed: {}", task_clone.name, e);
                            task_clone.status = TaskStatus::Failed;
                            break;
                        }
                    }
                }

                // 更新任务状态
                if let Err(e) = db.update_task(&task_clone).await {
                    error!("Failed to update task status: {}", e);
                }
            }
        });
    } else {
        return Err(format!("Task with id {} not found", task_id));
    }
    Ok(())
}

// 辅助函数：执行单个任务
#[allow(dead_code)] // 添加注解以允许未使用的函数
async fn run_single_task(db: &Arc<DbManager>, task_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let tasks = db.get_tasks().await?;
    if let Some(mut task) = tasks.into_iter().find(|t| t.id == task_id) {
        // 更新任务状态为运行中
        task.status = TaskStatus::Running;
        db.update_task(&task).await?;

        // 执行任务
        let executor = crate::executor::Executor::new();
        match executor.execute_task(&mut task).await {
            Ok(_) => {
                info!("Task {} completed successfully", task.name);
                task.status = TaskStatus::Completed;
                db.update_task(&task).await?;
                Ok(())
            },
            Err(e) => {
                error!("Task {} failed: {}", task.name, e);
                task.status = TaskStatus::Failed;
                db.update_task(&task).await?;
                Err(e)
            }
        }
    } else {
        Err(format!("Task with id {} not found", task_id).into())
    }
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn start_task(state: State<'_, AppState>, task_id: String) -> Result<(), String> {
    info!("Starting task with id: {}", task_id);
    let tasks = state.db.get_tasks().await.map_err(|e| e.to_string())?;
    if let Some(mut task) = tasks.into_iter().find(|t| t.id == task_id) {
        // 更新任务状态为运行中
        task.status = TaskStatus::Running;
        state.db.update_task(&task).await.map_err(|e| e.to_string())?;

        // 在后台线程中执行任务
        let db = state.db.clone();
        let mut task_clone = task.clone();

        tokio::spawn(async move {
            let executor = crate::executor::Executor::new();
            match executor.execute_task(&mut task_clone).await {
                Ok(_) => {
                    info!("Task {} completed successfully", task_clone.name);
                    task_clone.status = TaskStatus::Completed;
                    if let Err(e) = db.update_task(&task_clone).await {
                        error!("Failed to update task status: {}", e);
                    }
                },
                Err(e) => {
                    error!("Task {} failed: {}", task_clone.name, e);
                    task_clone.status = TaskStatus::Failed;
                    if let Err(e) = db.update_task(&task_clone).await {
                        error!("Failed to update task status: {}", e);
                    }
                }
            }
        });
    } else {
        return Err(format!("Task with id {} not found", task_id));
    }
    Ok(())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn stop_task(state: State<'_, AppState>, task_id: String) -> Result<(), String> {
    info!("Stopping task with id: {}", task_id);
    let tasks = state.db.get_tasks().await.map_err(|e| e.to_string())?;
    if let Some(mut task) = tasks.into_iter().find(|t| t.id == task_id) {
        // 更新任务状态为已停止
        task.status = TaskStatus::Failed; // 使用Failed状态表示任务被停止
        state.db.update_task(&task).await.map_err(|e| e.to_string())?;

        // 如果是任务组，同时停止所有子任务
        if task.is_group && task.child_tasks.is_some() {
            for child_id in task.child_tasks.unwrap() {
                // 使用Box::pin引入间接性，避免无限大小的Future
                if let Err(e) = Box::pin(stop_task(state.clone(), child_id)).await {
                    error!("Failed to stop child task: {}", e);
                }
            }
        }

        info!("Task {} stopped successfully", task.name);
    } else {
        return Err(format!("Task with id {} not found", task_id));
    }
    Ok(())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn create_agent(state: State<'_, AppState>, agent: Agent) -> Result<(), String> {
    info!("Creating new agent: {}", agent.name);
    state.db.create_agent(&agent).await.map_err(|e| {
        error!("Failed to create agent: {:?}", e);
        e.to_string()
    })
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn update_agent(state: State<'_, AppState>, agent: Agent) -> Result<(), String> {
    info!("Updating agent: {}", agent.name);
    state.db.update_agent(&agent).await.map_err(|e| {
        error!("Failed to update agent: {:?}", e);
        e.to_string()
    })
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn delete_agent(state: State<'_, AppState>, id: String) -> Result<(), String> {
    info!("Deleting agent: {}", id);

    // 先获取要删除的执行机信息
    let agents = state.db.get_agents().await.map_err(|e| {
        error!("Failed to get agent: {:?}", e);
        e.to_string()
    })?;

    // 查找要删除的执行机
    if let Some(agent) = agents.iter().find(|a| a.id == id) {
        // 检查是否是Master执行机
        if agent.name == "Master" && agent.ip == "127.0.0.1" {
            error!("Cannot delete Master agent");
            return Err("不能删除Master执行机，它是系统默认的本地执行机".to_string());
        }

        // 不是Master执行机，可以删除
        state.db.delete_agent(&id).await.map_err(|e| {
            error!("Failed to delete agent: {:?}", e);
            e.to_string()
        })
    } else {
        Err(format!("Agent with id {} not found", id))
    }
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn get_operation_logs(state: State<'_, AppState>) -> Result<Vec<OperationLog>, String> {
    info!("Fetching operation logs");
    state.db.get_operation_logs()
        .await
        .map_err(|e| {
            error!("Failed to fetch operation logs: {}", e);
            e.to_string()
        })
}


// Project Management Commands
// TODO: 实现项目管理功能
// 注释掉 get_projects 命令，因为它已经在 project/commands.rs 中实现
// #[tauri::command]
// #[allow(dead_code)] // 添加注解以允许未使用的函数
// pub async fn get_projects(_state: State<'_, AppState>) -> Result<Vec<crate::models::Project>, String> {
//     info!("Fetching projects via command");
//     // 暂时返回空列表，等待项目管理功能实现
//     Ok(Vec::new())
// }

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn create_project(_state: State<'_, AppState>, project: crate::models::Project) -> Result<(), String> {
    info!("Creating project via command: {}", project.name);
    // 暂时返回成功，等待项目管理功能实现
    Ok(())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn update_project(_state: State<'_, AppState>, project: crate::models::Project) -> Result<(), String> {
    info!("Updating project via command: {}", project.name);
    // 暂时返回成功，等待项目管理功能实现
    Ok(())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn delete_project(_state: State<'_, AppState>, id: String) -> Result<(), String> {
    info!("Deleting project via command: {}", id);
    // 暂时返回成功，等待项目管理功能实现
    Ok(())
}


// SSH相关命令
lazy_static::lazy_static! {
    static ref SSH_MANAGER: StdMutex<Option<Arc<SshManager>>> = StdMutex::new(None);
}

#[allow(dead_code)] // 添加注解以允许未使用的函数
fn get_ssh_manager(state: &State<'_, AppState>) -> Arc<SshManager> {
    let mut manager = SSH_MANAGER.lock().unwrap();
    if manager.is_none() {
        *manager = Some(Arc::new(SshManager::new(state.db.clone())));
    }
    manager.as_ref().unwrap().clone()
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn connect_ssh(state: State<'_, AppState>, agent_id: String) -> Result<(), String> {
    info!("Connecting to agent: {}", agent_id);
    let agents = state.db.get_agents().await.map_err(|e| e.to_string())?;
    let agent = agents.into_iter().find(|a| a.id == agent_id)
        .ok_or_else(|| format!("Agent with id {} not found", agent_id))?;

    let ssh_manager = get_ssh_manager(&state);
    ssh_manager.connect(&agent).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn disconnect_ssh(state: State<'_, AppState>, agent_id: String) -> Result<(), String> {
    info!("Disconnecting from agent: {}", agent_id);
    let ssh_manager = get_ssh_manager(&state);
    ssh_manager.disconnect(&agent_id).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn execute_ssh_command(state: State<'_, AppState>, agent_id: String, command: String) -> Result<String, String> {
    info!("Executing command on agent {}: {}", agent_id, command);
    let ssh_manager = get_ssh_manager(&state);

    // 执行命令
    let result = ssh_manager.execute_command(&agent_id, &command).await;

    // 记录操作日志
    let log = OperationLog {
        id: Uuid::new_v4().to_string(),
        agent_id: agent_id.clone(),
        operation_type: OperationType::Command,
        description: format!("执行命令: {}", command),
        status: match &result {
            Ok(_) => OperationStatus::Success,
            Err(_) => OperationStatus::Failed,
        },
        created_at: crate::models::DbDateTime(Utc::now()),
    };

    if let Err(e) = state.db.create_operation_log(&log).await {
        error!("Failed to create operation log: {}", e);
    }

    result.map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn send_ssh_input(state: State<'_, AppState>, agent_id: String, input: String) -> Result<(), String> {
    let ssh_manager = get_ssh_manager(&state);
    ssh_manager.write_to_pty(&agent_id, &input).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn read_ssh_output(state: State<'_, AppState>, agent_id: String) -> Result<String, String> {
    let ssh_manager = get_ssh_manager(&state);
    ssh_manager.read_from_pty(&agent_id).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn transfer_file(state: State<'_, AppState>, agent_id: String, source_path: String, target_path: String) -> Result<(), String> {
    info!("Transferring file from {} to {} on agent {}", source_path, target_path, agent_id);
    let ssh_manager = get_ssh_manager(&state);

    // 传输文件
    let result = ssh_manager.transfer_file(&agent_id, &source_path, &target_path).await;

    // 记录操作日志
    let log = OperationLog {
        id: Uuid::new_v4().to_string(),
        agent_id: agent_id.clone(),
        operation_type: OperationType::FileTransfer,
        description: format!("传输文件: {} -> {}", source_path, target_path),
        status: match &result {
            Ok(_) => OperationStatus::Success,
            Err(_) => OperationStatus::Failed,
        },
        created_at: crate::models::DbDateTime(Utc::now()),
    };

    if let Err(e) = state.db.create_operation_log(&log).await {
        error!("Failed to create operation log: {}", e);
    }

    result.map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn check_path_exists(path: String) -> Result<bool, String> {
    info!("Checking if path exists: {}", path);
    let path_exists = std::path::Path::new(&path).exists();
    info!("Path {} exists: {}", path, path_exists);
    Ok(path_exists)
}

/// 获取性能指标报告
#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn get_performance_metrics() -> Result<Vec<(String, u64, u64, u64)>, String> {
    info!("获取性能指标报告");
    Ok(logger::get_performance_report())
}

/// 获取错误计数报告
#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn get_error_metrics() -> Result<Vec<(String, u32)>, String> {
    info!("获取错误计数报告");
    Ok(logger::get_error_report())
}

/// 重置所有指标
#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn reset_metrics() -> Result<(), String> {
    info!("重置所有指标");
    logger::reset_metrics();
    Ok(())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn test_agent_connection(state: State<'_, AppState>, agent: Agent) -> Result<i32, String> {
    info!("Testing connection to agent: {}", agent.name);
    let ssh_manager = get_ssh_manager(&state);
    ssh_manager.test_connection(&agent).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub async fn ping_agents(state: State<'_, AppState>) -> Result<(), String> {
    info!("开始检测所有执行机状态");
    let agents = state.db.get_agents().await.map_err(|e| e.to_string())?;

    for agent in agents {
        let db = state.db.clone();
        let _agent_id = agent.id.clone();
        let agent_name = agent.name.clone();
        let agent_ip = agent.ip.clone();

        tokio::spawn(async move {
            info!("正在检测执行机 {} (IP: {}) 的状态", agent_name, agent_ip);
            let start = std::time::Instant::now();
            let ping_result = tokio::process::Command::new("ping")
                .arg("-c").arg("1")   // 只发送1个包
                .arg("-W").arg("2")   // 等待超时2秒
                .arg(&agent_ip)         // 使用IP地址而不是agent_id
                .output()
                .await;

            let mut agent_clone = agent.clone();
            agent_clone.last_seen = crate::models::DbDateTime(Utc::now());

            match ping_result {
                Ok(output) => {
                    let success = output.status.success();
                    let latency = start.elapsed().as_millis() as i32;

                    if success {
                        agent_clone.ping_latency = Some(latency as u32);
                        agent_clone.status = AgentStatus::Online;
                        info!("执行机 {} (IP: {}) ping成功，延迟: {}ms", agent_name, agent_ip, latency);
                    } else {
                        agent_clone.status = AgentStatus::Offline;
                        agent_clone.ping_latency = None;
                        error!("执行机 {} (IP: {}) ping失败: 主机不可达", agent_name, agent_ip);
                    }
                },
                Err(e) => {
                    agent_clone.status = AgentStatus::Offline;
                    agent_clone.ping_latency = None;
                    error!("执行机 {} (IP: {}) ping失败: {}", agent_name, agent_ip, e);
                }
            }

            if let Err(e) = db.update_agent(&agent_clone).await {
                error!("更新执行机 {} 状态失败: {}", agent_name, e);
            } else {
                info!("已更新执行机 {} 的状态", agent_name);
            }
        });
    }

    Ok(())
}




// --- PTY Management ---
lazy_static::lazy_static! {
    // Use StdMutex for lazy static initialization
    static ref PTY_MANAGER: StdMutex<Option<Arc<PtyManager>>> = StdMutex::new(None);
}

// Function to initialize and/or retrieve the PtyManager instance
fn get_pty_manager(state: &State<'_, AppState>, app_handle: &tauri::AppHandle) -> Arc<PtyManager> {
    let mut manager_guard = PTY_MANAGER.lock().unwrap();
    if manager_guard.is_none() {
        info!("Initializing PtyManager...");
        let ssh_manager = get_ssh_manager(state); // Assuming get_ssh_manager exists and works
        // Pass the app_handle during initialization
        *manager_guard = Some(Arc::new(PtyManager::new(ssh_manager, state.db.clone(), app_handle.clone())));
        info!("PtyManager initialized.");
    }
    manager_guard.as_ref().unwrap().clone()
}

// --- New Local PTY Commands ---

#[tauri::command]
pub async fn create_local_pty(state: State<'_, AppState>, app_handle: tauri::AppHandle, project_id: String, initial_command: Option<String>) -> Result<u32, String> {
    info!("Received request to create local PTY for project {}", project_id);
    let pty_manager = get_pty_manager(&state, &app_handle);
    pty_manager.create_local_pty(project_id, initial_command).await
}

#[tauri::command]
pub async fn write_to_pty(state: State<'_, AppState>, app_handle: tauri::AppHandle, pty_id: u32, data: String) -> Result<(), String> {
    // info!("Received request to write to PTY {}", pty_id);
    let pty_manager = get_pty_manager(&state, &app_handle);
    // Assuming write_to_pty now correctly dispatches or is replaced by write_to_local_pty
    pty_manager.write_to_local_pty(pty_id, &data).await
}

#[tauri::command]
pub async fn resize_pty(state: State<'_, AppState>, app_handle: tauri::AppHandle, pty_id: u32, cols: u16, rows: u16) -> Result<(), String> {
    info!("Received request to resize PTY {} to {}x{}", pty_id, cols, rows);
    let pty_manager = get_pty_manager(&state, &app_handle);
    // Assuming resize_pty now correctly dispatches or is replaced by resize_local_pty
    pty_manager.resize_local_pty(pty_id, cols, rows).await
}

#[tauri::command]
pub async fn close_pty(state: State<'_, AppState>, app_handle: tauri::AppHandle, pty_id: u32) -> Result<(), String> {
    info!("Received request to close PTY {}", pty_id);
    let pty_manager = get_pty_manager(&state, &app_handle);
    // Assuming close_pty now correctly dispatches or is replaced by close_local_pty
    pty_manager.close_local_pty(pty_id).await
}

#[tauri::command]
pub async fn start_pty(state: State<'_, AppState>, app_handle: tauri::AppHandle, project_id: String, initial_command: Option<String>) -> Result<u32, String> {
    info!("Starting PTY for project {}", project_id);
    let pty_manager = get_pty_manager(&state, &app_handle);
    pty_manager.create_local_pty(project_id, initial_command).await
}

#[tauri::command]
pub async fn stop_pty(state: State<'_, AppState>, app_handle: tauri::AppHandle, pty_id: u32) -> Result<(), String> {
    info!("Stopping PTY {}", pty_id);
    let pty_manager = get_pty_manager(&state, &app_handle);
    pty_manager.close_local_pty(pty_id).await
}

#[tauri::command]
pub async fn check_pty_status(state: State<'_, AppState>, app_handle: tauri::AppHandle, pty_id: u32) -> Result<bool, String> {
    let pty_manager = get_pty_manager(&state, &app_handle);
    pty_manager.is_pty_active(pty_id).await
}

#[tauri::command]
pub async fn subscribe_to_pty_output(state: State<'_, AppState>, app_handle: tauri::AppHandle, pty_id: u32) -> Result<(), String> {
    info!("Subscribing to PTY {} output", pty_id);
    let pty_manager = get_pty_manager(&state, &app_handle);
    pty_manager.subscribe_to_output(pty_id).await
}
