// src-tauri/src/cache/mod.rs
use moka::future::Cache;
use once_cell::sync::Lazy;
use std::hash::Hash;
use std::time::Duration;
use log::{debug, info};

/// 缓存配置
pub struct CacheConfig {
    pub max_capacity: u64,
    pub time_to_live: Duration,
    pub time_to_idle: Duration,
}

impl Default for CacheConfig {
    fn default() -> Self {
        CacheConfig {
            max_capacity: 10_000,
            time_to_live: Duration::from_secs(60 * 30), // 30分钟
            time_to_idle: Duration::from_secs(60 * 10), // 10分钟
        }
    }
}

/// 创建新的缓存
pub fn create_cache<K, V>(config: CacheConfig) -> Cache<K, V>
where
    K: Hash + Eq + Send + Sync + 'static,
    V: Clone + Send + Sync + 'static,
{
    debug!("创建缓存，容量: {}, TTL: {:?}, TTI: {:?}", 
           config.max_capacity, config.time_to_live, config.time_to_idle);
    
    Cache::builder()
        .max_capacity(config.max_capacity)
        .time_to_live(config.time_to_live)
        .time_to_idle(config.time_to_idle)
        .build()
}

// 项目缓存
static PROJECT_CACHE: Lazy<Cache<String, crate::domain::project::model::Project>> = Lazy::new(|| {
    info!("初始化项目缓存");
    create_cache(CacheConfig::default())
});

// 任务缓存
static TASK_CACHE: Lazy<Cache<String, crate::domain::task::model::Task>> = Lazy::new(|| {
    info!("初始化任务缓存");
    create_cache(CacheConfig::default())
});

// 执行机缓存
static AGENT_CACHE: Lazy<Cache<String, crate::domain::agent::model::Agent>> = Lazy::new(|| {
    info!("初始化执行机缓存");
    create_cache(CacheConfig::default())
});

// AI聊天会话缓存
static CONVERSATION_CACHE: Lazy<Cache<String, crate::domain::ai_chat::model::ChatConversation>> = Lazy::new(|| {
    info!("初始化AI聊天会话缓存");
    create_cache(CacheConfig::default())
});

/// 获取项目缓存
pub fn get_project_cache() -> &'static Cache<String, crate::domain::project::model::Project> {
    &PROJECT_CACHE
}

/// 获取任务缓存
pub fn get_task_cache() -> &'static Cache<String, crate::domain::task::model::Task> {
    &TASK_CACHE
}

/// 获取执行机缓存
pub fn get_agent_cache() -> &'static Cache<String, crate::domain::agent::model::Agent> {
    &AGENT_CACHE
}

/// 获取AI聊天会话缓存
pub fn get_conversation_cache() -> &'static Cache<String, crate::domain::ai_chat::model::ChatConversation> {
    &CONVERSATION_CACHE
}

/// 清除所有缓存
pub async fn clear_all_caches() {
    info!("清除所有缓存");
    PROJECT_CACHE.invalidate_all();
    TASK_CACHE.invalidate_all();
    AGENT_CACHE.invalidate_all();
    CONVERSATION_CACHE.invalidate_all();
}
