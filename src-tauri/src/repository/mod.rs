// src-tauri/src/repository/mod.rs
use crate::error::DbError;
use async_trait::async_trait;

/// 通用仓库接口
#[async_trait]
pub trait Repository<T, ID> {
    /// 获取所有记录
    async fn get_all(&self) -> Result<Vec<T>, DbError>;
    
    /// 根据ID获取记录
    async fn get_by_id(&self, id: ID) -> Result<T, DbError>;
    
    /// 创建记录
    async fn create(&self, item: &T) -> Result<(), DbError>;
    
    /// 更新记录
    async fn update(&self, item: &T) -> Result<(), DbError>;
    
    /// 删除记录
    async fn delete(&self, id: ID) -> Result<(), DbError>;
}

/// 数据库连接池
pub mod connection_pool;

/// 项目仓库
pub mod project_repository;

/// 任务仓库
pub mod task_repository;

/// 执行机仓库
pub mod agent_repository;

/// AI聊天仓库
pub mod ai_chat_repository;

/// 测试用例仓库
pub mod test_case_repository;

/// 操作日志仓库
pub mod operation_log_repository;
