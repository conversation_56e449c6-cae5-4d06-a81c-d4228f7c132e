// src-tauri/src/repository/connection_pool.rs
use crate::error::DbError;
use r2d2::Pool;
use r2d2_sqlite::SqliteConnectionManager;
use rusqlite::Connection;
use std::path::Path;
use std::sync::Arc;
use log::{error, info};
use once_cell::sync::OnceCell;

/// 数据库连接池
pub struct DbPool {
    pool: Pool<SqliteConnectionManager>,
}

// 全局数据库连接池
static DB_POOL: OnceCell<Arc<DbPool>> = OnceCell::new();

impl DbPool {
    /// 创建新的数据库连接池
    pub fn new(db_path: &Path) -> Result<Self, DbError> {
        info!("创建数据库连接池，路径: {:?}", db_path);
        
        let manager = SqliteConnectionManager::file(db_path)
            .with_flags(rusqlite::OpenFlags::SQLITE_OPEN_READ_WRITE | rusqlite::OpenFlags::SQLITE_OPEN_CREATE | rusqlite::OpenFlags::SQLITE_OPEN_FULL_MUTEX);
        
        let pool = Pool::builder()
            .max_size(10)
            .min_idle(Some(2))
            .build(manager)
            .map_err(|e| {
                error!("创建数据库连接池失败: {}", e);
                DbError::ConnectionFailed(e.to_string())
            })?;
        
        // 初始化连接，设置外键约束和超时
        let conn = pool.get().map_err(|e| {
            error!("获取数据库连接失败: {}", e);
            DbError::ConnectionFailed(e.to_string())
        })?;
        
        conn.execute_batch("
            PRAGMA foreign_keys = ON;
            PRAGMA busy_timeout = 5000;
            PRAGMA journal_mode = WAL;
            PRAGMA synchronous = NORMAL;
        ").map_err(|e| {
            error!("设置数据库参数失败: {}", e);
            DbError::QueryFailed(e.to_string())
        })?;
        
        info!("数据库连接池创建成功");
        Ok(DbPool { pool })
    }
    
    /// 获取数据库连接
    pub fn get_connection(&self) -> Result<r2d2::PooledConnection<SqliteConnectionManager>, DbError> {
        self.pool.get().map_err(|e| {
            error!("获取数据库连接失败: {}", e);
            DbError::ConnectionFailed(e.to_string())
        })
    }
    
    /// 执行事务
    pub fn transaction<F, T>(&self, f: F) -> Result<T, DbError>
    where
        F: FnOnce(&Connection) -> Result<T, DbError>,
    {
        let conn = self.get_connection()?;
        
        let tx = conn.transaction().map_err(|e| {
            error!("创建事务失败: {}", e);
            DbError::TransactionFailed(e.to_string())
        })?;
        
        match f(&tx) {
            Ok(result) => {
                tx.commit().map_err(|e| {
                    error!("提交事务失败: {}", e);
                    DbError::TransactionFailed(e.to_string())
                })?;
                Ok(result)
            },
            Err(e) => {
                if let Err(rollback_err) = tx.rollback() {
                    error!("回滚事务失败: {}", rollback_err);
                }
                Err(e)
            }
        }
    }
    
    /// 初始化全局数据库连接池
    pub fn init_global_pool(db_path: &Path) -> Result<Arc<DbPool>, DbError> {
        match DB_POOL.get() {
            Some(pool) => Ok(pool.clone()),
            None => {
                let pool = Arc::new(DbPool::new(db_path)?);
                match DB_POOL.set(pool.clone()) {
                    Ok(_) => Ok(pool),
                    Err(_) => {
                        // 另一个线程可能已经设置了连接池
                        match DB_POOL.get() {
                            Some(existing_pool) => Ok(existing_pool.clone()),
                            None => {
                                error!("初始化全局数据库连接池失败");
                                Err(DbError::ConnectionFailed("初始化全局数据库连接池失败".to_string()))
                            }
                        }
                    }
                }
            }
        }
    }
    
    /// 获取全局数据库连接池
    pub fn get_global_pool() -> Result<Arc<DbPool>, DbError> {
        match DB_POOL.get() {
            Some(pool) => Ok(pool.clone()),
            None => {
                error!("全局数据库连接池未初始化");
                Err(DbError::ConnectionFailed("全局数据库连接池未初始化".to_string()))
            }
        }
    }
}
