// src-tauri/src/pty_manager.rs
use std::sync::{Arc, Mutex as StdMutex}; // Use standard Mutex for simpler locking
use std::collections::HashMap;
use log::{info, error, warn};
use crate::db::DbManager;
use crate::ssh_manager::SshManager;
use tauri::{AppHandle, Emitter}; // 添加 Emitter trait
use portable_pty::{CommandBuilder, PtySize, NativePtySystem, PtySystem, MasterPty};
use tokio::sync::Mutex as TokioMutex; // Use Tokio Mutex for async locking

use crate::project::db_operations as project_db_ops;
use crate::db::common::DbOperations;

// Enum to differentiate between SSH and Local PTY sessions
enum PtySessionType {
    Ssh(SshPtySession),
    Local(LocalPtySession),
}

/// SSH PTY 会话信息 (Placeholder - Adapt if SSH PTYs are managed here too)
struct SshPtySession {
    agent_id: String,
    active: bool,
    rows: u16,
    cols: u16,
    output_buffer: String, // Consider if needed or handled by SSH manager
}

/// Local PTY 会话信息
struct LocalPtySession {
    pty_id: u32, // Unique ID for the local PTY
    project_id: String,
    master: Box<dyn MasterPty + Send>,
    // writer: Arc<TokioMutex<Box<dyn AsyncWrite + Unpin + Send>>>, // Use master.writer()
    // reader_handle: Option<tokio::task::JoinHandle<()>>,
    app_handle: AppHandle,
    active: bool,
}

/// PTY 管理器
pub struct PtyManager {
    ssh_manager: Arc<SshManager>, // Keep if SSH PTYs are also managed
    db: Arc<DbManager>,
    // Use StdMutex for the outer HashMap lock, TokioMutex for inner session data if needed
    sessions: StdMutex<HashMap<u32, Arc<TokioMutex<LocalPtySession>>>>, // Store local sessions by pty_id
    pty_system: Box<dyn PtySystem + Send + Sync>,
    next_pty_id: StdMutex<u32>,
    app_handle: AppHandle,
}

impl PtyManager {
    /// 创建新的 PTY 管理器
    pub fn new(ssh_manager: Arc<SshManager>, db: Arc<DbManager>, app_handle: AppHandle) -> Self {
        PtyManager {
            ssh_manager,
            db,
            sessions: StdMutex::new(HashMap::new()),
            pty_system: Box::new(NativePtySystem::default()),
            next_pty_id: StdMutex::new(1),
            app_handle,
        }
    }

    fn get_next_pty_id(&self) -> u32 {
        let mut guard = self.next_pty_id.lock().unwrap();
        let id = *guard;
        *guard += 1;
        id
    }

    /// 创建本地 PTY 会话
    pub async fn create_local_pty(&self, project_id: String, _initial_command: Option<String>) -> Result<u32, String> {
        let pty_id = self.get_next_pty_id();
        info!("Attempting to create local PTY session {} for project {}", pty_id, project_id);

        // 获取项目路径
        let project_path = match project_db_ops::get_project_db_ops().get_by_id(&project_id) {
            Ok(project) => {
                info!("Found project path for {}: {}", project_id, project.path);
                std::path::PathBuf::from(project.path)
            },
            Err(e) => {
                error!("Failed to get project path for {}: {}. Falling back to current directory.", project_id, e);
                // Fallback to current directory if project path retrieval fails
                std::env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?
            }
        };

        info!("Using PTY working directory: {:?}", project_path);

        let size = PtySize {
            rows: 24,
            cols: 80,
            pixel_width: 0,
            pixel_height: 0,
        };

        // Determine the shell command based on the OS
        let shell_cmd = if cfg!(target_os = "windows") {
            "cmd.exe"
        } else {
            // Prefer bash if available, otherwise sh
            if std::path::Path::new("/bin/bash").exists() {
                "/bin/bash"
            } else {
                "/bin/sh"
            }
        };

        let mut cmd = CommandBuilder::new(shell_cmd);
        cmd.cwd(project_path);

        // Add initial command if provided
        // Note: How initial commands are passed depends on the shell.
        // For bash/sh, -c might work. For cmd.exe, /C.
        // This simple approach might not cover all cases.
        // if let Some(command) = initial_command {
        //     if cfg!(target_os = "windows") {
        //         cmd.args(["/C", &command]);
        //     } else {
        //         cmd.args(["-c", &command]);
        //     }
        // }

        let pair = self.pty_system.openpty(size).map_err(|e| format!("Failed to open PTY: {}", e))?;
        let master = pair.master;
        let slave = pair.slave;

        // Spawn the command in the PTY
        let child_process = slave.spawn_command(cmd).map_err(|e| format!("Failed to spawn command in PTY: {}", e))?;
        drop(slave); // Slave is no longer needed after spawning

        info!("Spawned child process with PID: {:?}", child_process.process_id());

        let session = Arc::new(TokioMutex::new(LocalPtySession {
            pty_id,
            project_id: project_id.clone(),
            master,
            app_handle: self.app_handle.clone(),
            active: true,
        }));

        // Spawn a task to read output from the PTY and emit events
        let reader_session = Arc::clone(&session);
        tokio::spawn(async move {
            let reader = reader_session.lock().await.master.try_clone_reader().map_err(|e| format!("Failed to clone reader: {}", e));

            if let Ok(mut reader_instance) = reader {
                let mut buffer = [0u8; 1024];
                loop {
                    match reader_instance.read(&mut buffer) {
                        Ok(0) => {
                            info!("PTY {} reader stream ended (EOF)", pty_id);
                            break;
                        }
                        Ok(n) => {
                            let data = &buffer[..n];
                            match String::from_utf8(data.to_vec()) {
                                Ok(output) => {
                                    // info!("PTY {} output: {}", pty_id, output);
                                    let app_handle = reader_session.lock().await.app_handle.clone();
                                    let event_name = format!("pty-output-{}", pty_id);
                                    if let Err(e) = app_handle.emit(&event_name, &output) {
                                        error!("Failed to emit PTY output event {}: {}", event_name, e);
                                    }
                                }
                                Err(e) => {
                                    warn!("PTY {} received non-UTF8 data: {}", pty_id, e);
                                    // Handle non-UTF8 data if necessary, maybe emit as base64?
                                }
                            }
                        }
                        Err(e) => {
                            error!("Error reading from PTY {}: {}", pty_id, e);
                            break;
                        }
                    }
                }
            } else {
                error!("Failed to get reader instance for PTY {}", pty_id);
            }

            // Cleanup when reading stops
            info!("PTY {} reader task finished.", pty_id);
            let mut session_guard = reader_session.lock().await;
            session_guard.active = false;
            // Optionally emit a 'pty-closed' event
            let app_handle = session_guard.app_handle.clone();
            let event_name = format!("pty-closed-{}", pty_id);
            if let Err(e) = app_handle.emit(&event_name, &()) {
                error!("Failed to emit PTY closed event {}: {}", event_name, e);
            }
        });

        // Store the session
        self.sessions.lock().unwrap().insert(pty_id, Arc::clone(&session));
        info!("Local PTY session {} created successfully.", pty_id);
        Ok(pty_id)
    }

    /// 向本地 PTY 写入数据
    pub async fn write_to_local_pty(&self, pty_id: u32, data: &str) -> Result<(), String> {
        let session_arc = match self.sessions.lock().unwrap().get(&pty_id) {
            Some(s) => Arc::clone(s),
            None => return Err(format!("Local PTY session {} not found", pty_id)),
        };

        let session_guard = session_arc.lock().await;
        if !session_guard.active {
            return Err(format!("Local PTY session {} is not active", pty_id));
        }

        let mut writer = session_guard.master.try_clone_writer().map_err(|e| format!("Failed to clone writer: {}", e))?;
        writer.write_all(data.as_bytes()).map_err(|e| format!("Failed to write to PTY {}: {}", pty_id, e))
    }

    /// 调整本地 PTY 大小
    pub async fn resize_local_pty(&self, pty_id: u32, cols: u16, rows: u16) -> Result<(), String> {
        let session_arc = match self.sessions.lock().unwrap().get(&pty_id) {
            Some(s) => Arc::clone(s),
            None => return Err(format!("Local PTY session {} not found", pty_id)),
        };

        let session_guard = session_arc.lock().await;
        if !session_guard.active {
            // Allow resizing even if not fully active? Maybe log a warning.
            warn!("Attempting to resize inactive local PTY session {}", pty_id);
            // return Err(format!("Local PTY session {} is not active", pty_id));
        }

        let size = PtySize { rows, cols, pixel_width: 0, pixel_height: 0 };
        session_guard.master.resize(size).map_err(|e| format!("Failed to resize PTY {}: {}", pty_id, e))
    }

    /// 关闭本地 PTY 会话
    pub async fn close_local_pty(&self, pty_id: u32) -> Result<(), String> {
        info!("Closing local PTY session {}", pty_id);
        let session_arc = match self.sessions.lock().unwrap().remove(&pty_id) {
            Some(s) => s,
            None => {
                warn!("Attempted to close non-existent local PTY session {}", pty_id);
                return Ok(()); // Or return Err if preferred
            }
        };

        let mut session_guard = session_arc.lock().await;
        session_guard.active = false;

        // The reader task should exit automatically when the master is dropped.
        // Dropping the master PTY should signal the child process to terminate (e.g., SIGHUP).
        // Explicitly killing might be needed in some cases, but portable-pty aims to handle this.
        info!("Dropping master PTY for session {}", pty_id);
        // Master is dropped when session_guard goes out of scope

        Ok(())
    }

    // --- Existing SSH PTY methods (Keep or remove based on requirements) ---

    /// 启动 SSH PTY 会话 (Placeholder/Example)
    pub async fn start_ssh_pty(&self, agent_id: &str, cols: u16, rows: u16) -> Result<(), String> {
        info!("Starting SSH PTY for agent {} with size {}x{}", agent_id, cols, rows);
        // Delegate to SshManager or implement here
        // self.ssh_manager.start_pty(agent_id, cols, rows).await
        Err("SSH PTY management not fully implemented in PtyManager".to_string())
    }

    /// 停止 SSH PTY 会话 (Placeholder/Example)
    pub async fn stop_ssh_pty(&self, agent_id: &str) -> Result<(), String> {
        info!("Stopping SSH PTY for agent {}", agent_id);
        // Delegate to SshManager or implement here
        // self.ssh_manager.stop_pty(agent_id).await
        Err("SSH PTY management not fully implemented in PtyManager".to_string())
    }

    /// 向 SSH PTY 写入数据 (Placeholder/Example)
    pub async fn write_to_ssh_pty(&self, agent_id: &str, data: &str) -> Result<(), String> {
        info!("Writing to SSH PTY for agent {}", agent_id);
        self.ssh_manager.write_to_pty(agent_id, data).await
    }

    /// 调整 SSH PTY 大小 (Placeholder/Example)
    pub async fn resize_ssh_pty(&self, agent_id: &str, cols: u16, rows: u16) -> Result<(), String> {
        info!("Resizing SSH PTY for agent {} to {}x{}", agent_id, cols, rows);
        // Delegate to SshManager or implement here
        // self.ssh_manager.resize_pty(agent_id, cols, rows).await
        Err("SSH PTY management not fully implemented in PtyManager".to_string())
    }

    // --- Generic methods (might need adjustment if handling both types) ---

    // These methods might become ambiguous if PtyManager handles both SSH and Local.
    // Consider renaming or removing them in favor of specific methods like
    // `write_to_local_pty` and `write_to_ssh_pty`.

    // pub async fn write_to_pty(&self, id: &str, data: &str) -> Result<(), String> {
    //     // Determine if id is agent_id (SSH) or pty_id (Local) and call appropriate method
    //     Err("Generic write_to_pty is ambiguous. Use specific methods.".to_string())
    // }

    // pub async fn resize_pty(&self, id: &str, cols: u16, rows: u16) -> Result<(), String> {
    //     // Determine type and call appropriate method
    //     Err("Generic resize_pty is ambiguous. Use specific methods.".to_string())
    // }

    // pub async fn stop_pty(&self, id: &str) -> Result<(), String> {
    //     // Determine type and call appropriate method
    //     Err("Generic stop_pty is ambiguous. Use specific methods.".to_string())
    // }

    /// 检查PTY是否活跃
    pub async fn is_pty_active(&self, pty_id: u32) -> Result<bool, String> {
        // 先获取会话的Arc引用，然后释放外部锁
        let session_arc = {
            let sessions = self.sessions.lock().unwrap();
            match sessions.get(&pty_id) {
                Some(s) => Arc::clone(s),
                None => return Err(format!("Local PTY session {} not found", pty_id))
            }
        };

        // 现在可以安全地使用await，因为外部锁已经释放
        let session_guard = session_arc.lock().await;
        Ok(session_guard.active)
    }

    /// 订阅PTY输出
    pub async fn subscribe_to_output(&self, pty_id: u32) -> Result<(), String> {
        // 检查PTY是否存在
        let exists = {
            let sessions = self.sessions.lock().unwrap();
            sessions.contains_key(&pty_id)
        };

        if exists {
            // 输出已经通过事件发送，这里只需要确认PTY存在
            info!("Subscribed to PTY {} output", pty_id);
            Ok(())
        } else {
            Err(format!("Local PTY session {} not found", pty_id))
        }
    }
}

// Cleanup on drop (optional, as Arc/Mutex should handle drops)
// impl Drop for PtyManager {
//     fn drop(&mut self) {
//         info!("Dropping PtyManager, cleaning up sessions...");
//         let sessions = self.sessions.lock().unwrap();
//         for (pty_id, session_arc) in sessions.iter() {
//             // This might require async cleanup, which is tricky in drop
//             // Consider a dedicated cleanup method called before shutdown
//             info!("Cleaning up session {}", pty_id);
//             // let session_guard = session_arc.blocking_lock(); // Avoid blocking in async drop
//         }
//     }
// }
