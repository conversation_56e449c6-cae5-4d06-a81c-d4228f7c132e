// src-tauri/src/ai_chat.rs
use serde::{Serialize, Deserialize};
use crate::models::DbDateTime;

/// 聊天消息角色
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MessageRole {
    User,
    Assistant,
    System,
}

/// 聊天消息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub id: String,
    pub conversation_id: String,
    pub role: MessageRole,
    pub content: String,
    pub created_at: DbDateTime,
}

/// 聊天会话
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatConversation {
    pub id: String,
    pub title: String,
    pub created_at: DbDateTime,
    pub updated_at: DbDateTime,
    pub model: Option<String>,
}

/// API 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiConfig {
    pub id: String,
    pub name: String,
    pub provider: String,
    pub api_key: String,
    pub api_url: Option<String>,
    pub is_default: bool,
    pub created_at: DbDateTime,
    pub updated_at: DbDateTime,
}
