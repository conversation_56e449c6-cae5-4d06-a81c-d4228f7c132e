// src-tauri/src/domain/project/service.rs
use crate::error::{ProjectError, AppError};
use super::model::{Project, ProjectFile, ProjectStatus};
use super::repository::ProjectRepository;
use std::path::Path;
use std::sync::Arc;
use async_trait::async_trait;

/// 项目服务接口
#[async_trait]
pub trait ProjectService: Send + Sync {
    /// 获取所有项目
    async fn get_all_projects(&self) -> Result<Vec<Project>, ProjectError>;
    
    /// 根据ID获取项目
    async fn get_project(&self, id: &str) -> Result<Project, ProjectError>;
    
    /// 创建项目
    async fn create_project(&self, name: &str, description: Option<&str>, 
                           path: &str, created_by: &str) -> Result<Project, ProjectError>;
    
    /// 更新项目
    async fn update_project(&self, id: &str, name: Option<&str>, description: Option<&str>,
                           status: Option<ProjectStatus>, leader: Option<&str>, 
                           tags: Option<&str>) -> Result<Project, ProjectError>;
    
    /// 删除项目
    async fn delete_project(&self, id: &str) -> Result<(), ProjectError>;
    
    /// 获取项目文件列表
    async fn get_project_files(&self, project_id: &str) -> Result<Vec<ProjectFile>, ProjectError>;
    
    /// 获取项目文件
    async fn get_file(&self, file_id: &str) -> Result<ProjectFile, ProjectError>;
    
    /// 读取文件内容
    async fn read_file_content(&self, file_id: &str) -> Result<String, ProjectError>;
    
    /// 写入文件内容
    async fn write_file_content(&self, file_id: &str, content: &str) -> Result<(), ProjectError>;
    
    /// 创建文件或目录
    async fn create_file_or_dir(&self, project_id: &str, parent_id: Option<&str>, 
                               name: &str, is_directory: bool) -> Result<ProjectFile, ProjectError>;
    
    /// 删除文件或目录
    async fn delete_file_or_dir(&self, file_id: &str) -> Result<(), ProjectError>;
    
    /// 重命名文件或目录
    async fn rename_file_or_dir(&self, file_id: &str, new_name: &str) -> Result<(), ProjectError>;
    
    /// 扫描目录
    async fn scan_directory(&self, project_id: &str, dir_path: &str) -> Result<Vec<ProjectFile>, ProjectError>;
    
    /// 检查路径是否存在
    async fn check_path_exists(&self, path: &str) -> Result<bool, ProjectError>;
}

/// 项目服务实现
pub struct ProjectServiceImpl<R: ProjectRepository> {
    repository: Arc<R>,
}

impl<R: ProjectRepository> ProjectServiceImpl<R> {
    /// 创建新的项目服务
    pub fn new(repository: Arc<R>) -> Self {
        ProjectServiceImpl { repository }
    }
}

#[async_trait]
impl<R: ProjectRepository + 'static> ProjectService for ProjectServiceImpl<R> {
    async fn get_all_projects(&self) -> Result<Vec<Project>, ProjectError> {
        self.repository.get_all().await.map_err(ProjectError::Database)
    }
    
    async fn get_project(&self, id: &str) -> Result<Project, ProjectError> {
        self.repository.get_by_id(id.to_string()).await
            .map_err(|e| match e {
                DbError::NotFound(_) => ProjectError::NotFound(id.to_string()),
                _ => ProjectError::Database(e),
            })
    }
    
    async fn create_project(&self, name: &str, description: Option<&str>, 
                           path: &str, created_by: &str) -> Result<Project, ProjectError> {
        // 检查路径是否存在
        if !Path::new(path).exists() {
            return Err(ProjectError::InvalidRequest(format!("路径不存在: {}", path)));
        }
        
        // 检查是否已存在同名项目
        match self.repository.get_by_path(path).await {
            Ok(_) => return Err(ProjectError::InvalidRequest(format!("路径已被使用: {}", path))),
            Err(DbError::NotFound(_)) => {}, // 路径未被使用，可以继续
            Err(e) => return Err(ProjectError::Database(e)),
        }
        
        let mut project = Project::new(
            name.to_string(),
            path.to_string(),
            created_by.to_string(),
        );
        
        project.description = description.map(|s| s.to_string());
        
        // 验证项目
        project.validate().map_err(|e| ProjectError::InvalidRequest(e))?;
        
        // 保存项目
        self.repository.create(&project).await.map_err(ProjectError::Database)?;
        
        // 发布事件
        let payload = crate::events::create_payload(&project);
        crate::events::emit(crate::events::EventType::ProjectCreated, payload);
        
        // 添加到缓存
        crate::cache::get_project_cache().insert(project.id.clone(), project.clone()).await;
        
        Ok(project)
    }
    
    async fn update_project(&self, id: &str, name: Option<&str>, description: Option<&str>,
                           status: Option<ProjectStatus>, leader: Option<&str>, 
                           tags: Option<&str>) -> Result<Project, ProjectError> {
        // 获取项目
        let mut project = self.get_project(id).await?;
        
        // 更新项目
        project.update(
            name.map(|s| s.to_string()),
            description.map(|s| s.to_string()),
            status,
            leader.map(|s| s.to_string()),
            tags.map(|s| s.to_string()),
        );
        
        // 验证项目
        project.validate().map_err(|e| ProjectError::InvalidRequest(e))?;
        
        // 保存项目
        self.repository.update(&project).await.map_err(ProjectError::Database)?;
        
        // 发布事件
        let payload = crate::events::create_payload(&project);
        crate::events::emit(crate::events::EventType::ProjectUpdated, payload);
        
        // 更新缓存
        crate::cache::get_project_cache().insert(project.id.clone(), project.clone()).await;
        
        Ok(project)
    }
    
    async fn delete_project(&self, id: &str) -> Result<(), ProjectError> {
        // 获取项目
        let project = self.get_project(id).await?;
        
        // 删除项目
        self.repository.delete(id.to_string()).await.map_err(ProjectError::Database)?;
        
        // 发布事件
        let payload = crate::events::create_payload(&project);
        crate::events::emit(crate::events::EventType::ProjectDeleted, payload);
        
        // 从缓存中移除
        crate::cache::get_project_cache().invalidate(&project.id).await;
        
        Ok(())
    }
    
    async fn get_project_files(&self, project_id: &str) -> Result<Vec<ProjectFile>, ProjectError> {
        // 检查项目是否存在
        self.get_project(project_id).await?;
        
        // 获取项目文件列表
        self.repository.get_project_files(project_id).await.map_err(ProjectError::Database)
    }
    
    async fn get_file(&self, file_id: &str) -> Result<ProjectFile, ProjectError> {
        self.repository.get_file_by_id(file_id).await
            .map_err(|e| match e {
                DbError::NotFound(_) => ProjectError::FileNotFound(file_id.to_string()),
                _ => ProjectError::Database(e),
            })
    }
    
    async fn read_file_content(&self, file_id: &str) -> Result<String, ProjectError> {
        // 获取文件
        let file = self.get_file(file_id).await?;
        
        // 检查是否是目录
        if file.is_directory {
            return Err(ProjectError::InvalidRequest("无法读取目录内容".to_string()));
        }
        
        // 返回文件内容
        match file.content {
            Some(content) => Ok(content),
            None => Ok(String::new()),
        }
    }
    
    async fn write_file_content(&self, file_id: &str, content: &str) -> Result<(), ProjectError> {
        // 获取文件
        let mut file = self.get_file(file_id).await?;
        
        // 检查是否是目录
        if file.is_directory {
            return Err(ProjectError::InvalidRequest("无法写入目录内容".to_string()));
        }
        
        // 更新文件内容
        file.update_content(content.to_string());
        
        // 保存文件
        self.repository.update_file(&file).await.map_err(ProjectError::Database)?;
        
        // 发布事件
        let payload = crate::events::create_payload(&file);
        crate::events::emit(crate::events::EventType::ProjectFileUpdated, payload);
        
        Ok(())
    }
    
    async fn create_file_or_dir(&self, project_id: &str, parent_id: Option<&str>, 
                               name: &str, is_directory: bool) -> Result<ProjectFile, ProjectError> {
        // 检查项目是否存在
        let project = self.get_project(project_id).await?;
        
        // 确定父目录
        let parent_path = if let Some(parent_id) = parent_id {
            let parent = self.get_file(parent_id).await?;
            
            // 检查父目录是否是目录
            if !parent.is_directory {
                return Err(ProjectError::InvalidRequest("父节点不是目录".to_string()));
            }
            
            parent.path
        } else {
            project.path.clone()
        };
        
        // 构建文件路径
        let file_path = format!("{}/{}", parent_path, name);
        
        // 创建文件对象
        let file = ProjectFile::new(
            project_id.to_string(),
            name.to_string(),
            file_path,
            is_directory,
            parent_id.map(|s| s.to_string()),
        );
        
        // 验证文件
        file.validate().map_err(|e| ProjectError::InvalidRequest(e))?;
        
        // 保存文件
        self.repository.create_file(&file).await.map_err(ProjectError::Database)?;
        
        // 发布事件
        let payload = crate::events::create_payload(&file);
        crate::events::emit(crate::events::EventType::ProjectFileCreated, payload);
        
        Ok(file)
    }
    
    async fn delete_file_or_dir(&self, file_id: &str) -> Result<(), ProjectError> {
        // 获取文件
        let file = self.get_file(file_id).await?;
        
        // 如果是目录，先删除所有子文件
        if file.is_directory {
            let children = self.repository.get_children(file_id).await.map_err(ProjectError::Database)?;
            
            for child in children {
                self.delete_file_or_dir(&child.id).await?;
            }
        }
        
        // 删除文件
        self.repository.delete_file(file_id).await.map_err(ProjectError::Database)?;
        
        // 发布事件
        let payload = crate::events::create_payload(&file);
        crate::events::emit(crate::events::EventType::ProjectFileDeleted, payload);
        
        Ok(())
    }
    
    async fn rename_file_or_dir(&self, file_id: &str, new_name: &str) -> Result<(), ProjectError> {
        // 获取文件
        let mut file = self.get_file(file_id).await?;
        
        // 重命名文件
        file.rename(new_name.to_string());
        
        // 保存文件
        self.repository.update_file(&file).await.map_err(ProjectError::Database)?;
        
        // 发布事件
        let payload = crate::events::create_payload(&file);
        crate::events::emit(crate::events::EventType::ProjectFileUpdated, payload);
        
        Ok(())
    }
    
    async fn scan_directory(&self, project_id: &str, dir_path: &str) -> Result<Vec<ProjectFile>, ProjectError> {
        // 检查项目是否存在
        let project = self.get_project(project_id).await?;
        
        // 检查目录是否存在
        let path = Path::new(dir_path);
        if !path.exists() || !path.is_dir() {
            return Err(ProjectError::InvalidRequest(format!("目录不存在: {}", dir_path)));
        }
        
        // 扫描目录
        let mut files = Vec::new();
        
        // TODO: 实现目录扫描逻辑
        
        Ok(files)
    }
    
    async fn check_path_exists(&self, path: &str) -> Result<bool, ProjectError> {
        Ok(Path::new(path).exists())
    }
}
