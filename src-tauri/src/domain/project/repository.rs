// src-tauri/src/domain/project/repository.rs
use crate::error::DbError;
use crate::repository::Repository;
use super::model::{Project, ProjectFile};
use async_trait::async_trait;

/// 项目仓库接口
#[async_trait]
pub trait ProjectRepository: Repository<Project, String> {
    /// 根据路径获取项目
    async fn get_by_path(&self, path: &str) -> Result<Project, DbError>;
    
    /// 获取项目文件列表
    async fn get_project_files(&self, project_id: &str) -> Result<Vec<ProjectFile>, DbError>;
    
    /// 获取项目文件
    async fn get_file_by_id(&self, file_id: &str) -> Result<ProjectFile, DbError>;
    
    /// 创建项目文件
    async fn create_file(&self, file: &ProjectFile) -> Result<(), DbError>;
    
    /// 更新项目文件
    async fn update_file(&self, file: &ProjectFile) -> Result<(), DbError>;
    
    /// 删除项目文件
    async fn delete_file(&self, file_id: &str) -> Result<(), DbError>;
    
    /// 获取项目根目录
    async fn get_root_directory(&self, project_id: &str) -> Result<ProjectFile, DbError>;
    
    /// 获取子文件列表
    async fn get_children(&self, parent_id: &str) -> Result<Vec<ProjectFile>, DbError>;
}
