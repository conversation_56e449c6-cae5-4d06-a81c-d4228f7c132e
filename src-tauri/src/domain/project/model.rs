// src-tauri/src/domain/project/model.rs
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

/// 项目状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ProjectStatus {
    Active,
    Archived,
    Completed,
}

impl Default for ProjectStatus {
    fn default() -> Self {
        ProjectStatus::Active
    }
}

impl std::fmt::Display for ProjectStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ProjectStatus::Active => write!(f, "Active"),
            ProjectStatus::Archived => write!(f, "Archived"),
            ProjectStatus::Completed => write!(f, "Completed"),
        }
    }
}

impl ProjectStatus {
    /// 从字符串解析项目状态
    pub fn from_str(s: &str) -> Result<Self, String> {
        match s.to_lowercase().as_str() {
            "active" => Ok(ProjectStatus::Active),
            "archived" => Ok(ProjectStatus::Archived),
            "completed" => Ok(ProjectStatus::Completed),
            _ => Err(format!("无效的项目状态: {}", s)),
        }
    }
}

/// 项目模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Project {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub path: String,
    pub status: ProjectStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub created_by: String,
    pub leader: Option<String>,
    pub tags: Option<String>,
}

impl Default for Project {
    fn default() -> Self {
        let now = Utc::now();
        Project {
            id: Uuid::new_v4().to_string(),
            name: String::new(),
            description: None,
            path: String::new(),
            status: ProjectStatus::default(),
            created_at: now,
            updated_at: now,
            created_by: String::new(),
            leader: None,
            tags: None,
        }
    }
}

impl Project {
    /// 创建新项目
    pub fn new(name: String, path: String, created_by: String) -> Self {
        let now = Utc::now();
        Project {
            id: Uuid::new_v4().to_string(),
            name,
            description: None,
            path,
            status: ProjectStatus::Active,
            created_at: now,
            updated_at: now,
            created_by,
            leader: None,
            tags: None,
        }
    }
    
    /// 验证项目
    pub fn validate(&self) -> Result<(), String> {
        if self.name.trim().is_empty() {
            return Err("项目名称不能为空".to_string());
        }
        
        if self.path.trim().is_empty() {
            return Err("项目路径不能为空".to_string());
        }
        
        Ok(())
    }
    
    /// 更新项目
    pub fn update(&mut self, name: Option<String>, description: Option<String>, 
                  status: Option<ProjectStatus>, leader: Option<String>, tags: Option<String>) {
        if let Some(name) = name {
            self.name = name;
        }
        
        self.description = description;
        
        if let Some(status) = status {
            self.status = status;
        }
        
        self.leader = leader;
        self.tags = tags;
        self.updated_at = Utc::now();
    }
}

/// 项目文件模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectFile {
    pub id: String,
    pub project_id: String,
    pub name: String,
    pub path: String,
    pub is_directory: bool,
    pub size: Option<u64>,
    pub last_modified: Option<DateTime<Utc>>,
    pub content: Option<String>,
    pub parent_id: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl ProjectFile {
    /// 创建新的项目文件
    pub fn new(project_id: String, name: String, path: String, 
               is_directory: bool, parent_id: Option<String>) -> Self {
        let now = Utc::now();
        ProjectFile {
            id: Uuid::new_v4().to_string(),
            project_id,
            name,
            path,
            is_directory,
            size: None,
            last_modified: Some(now),
            content: None,
            parent_id,
            created_at: now,
            updated_at: now,
        }
    }
    
    /// 验证项目文件
    pub fn validate(&self) -> Result<(), String> {
        if self.name.trim().is_empty() {
            return Err("文件名不能为空".to_string());
        }
        
        if self.path.trim().is_empty() {
            return Err("文件路径不能为空".to_string());
        }
        
        Ok(())
    }
    
    /// 更新文件内容
    pub fn update_content(&mut self, content: String) {
        self.content = Some(content);
        self.size = Some(content.len() as u64);
        self.last_modified = Some(Utc::now());
        self.updated_at = Utc::now();
    }
    
    /// 重命名文件
    pub fn rename(&mut self, new_name: String) {
        self.name = new_name;
        self.last_modified = Some(Utc::now());
        self.updated_at = Utc::now();
    }
}
