// src-tauri/src/domain/project/command.rs
use super::model::{Project, ProjectFile, ProjectStatus};
use super::service::ProjectService;
use crate::error::{to_command_result, CommandResult};
use tauri::State;
use std::sync::Arc;
use log::{info, error, debug};

/// 应用状态
pub struct AppState {
    pub project_service: Arc<dyn ProjectService + Send + Sync>,
}

/// 获取项目列表
#[tauri::command]
pub async fn get_project_list(state: State<'_, AppState>) -> CommandResult<Vec<Project>> {
    info!("获取项目列表");
    
    let result = state.project_service.get_all_projects().await;
    to_command_result(result)
}

/// 获取项目详情
#[tauri::command]
pub async fn get_project_by_id(state: State<'_, AppState>, id: String) -> CommandResult<Project> {
    info!("获取项目详情: {}", id);
    
    let result = state.project_service.get_project(&id).await;
    to_command_result(result)
}

/// 创建新项目
#[tauri::command]
pub async fn create_new_project(
    state: State<'_, AppState>,
    name: String,
    description: Option<String>,
    path: String,
    created_by: String,
) -> CommandResult<Project> {
    info!("创建新项目: {}, 路径: {}", name, path);
    
    let result = state.project_service.create_project(
        &name,
        description.as_deref(),
        &path,
        &created_by,
    ).await;
    
    to_command_result(result)
}

/// 更新项目信息
#[tauri::command]
pub async fn update_project_info(
    state: State<'_, AppState>,
    id: String,
    name: Option<String>,
    description: Option<String>,
    status: String,
    leader: Option<String>,
    tags: Option<String>,
) -> CommandResult<Project> {
    info!("更新项目信息: {}", id);
    
    // 解析状态
    let project_status = match ProjectStatus::from_str(&status) {
        Ok(status) => status,
        Err(e) => {
            error!("无效的项目状态: {}", e);
            return Err(e);
        }
    };
    
    let result = state.project_service.update_project(
        &id,
        name.as_deref(),
        description.as_deref(),
        Some(project_status),
        leader.as_deref(),
        tags.as_deref(),
    ).await;
    
    to_command_result(result)
}

/// 删除项目
#[tauri::command]
pub async fn delete_project_by_id(state: State<'_, AppState>, id: String) -> CommandResult<()> {
    info!("删除项目: {}", id);
    
    let result = state.project_service.delete_project(&id).await;
    to_command_result(result)
}

/// 获取项目文件列表
#[tauri::command]
pub async fn get_project_files(state: State<'_, AppState>, project_id: String) -> CommandResult<Vec<ProjectFile>> {
    info!("获取项目文件列表: {}", project_id);
    
    let result = state.project_service.get_project_files(&project_id).await;
    to_command_result(result)
}

/// 获取文件详情
#[tauri::command]
pub async fn get_file_by_id(state: State<'_, AppState>, file_id: String) -> CommandResult<ProjectFile> {
    info!("获取文件详情: {}", file_id);
    
    let result = state.project_service.get_file(&file_id).await;
    to_command_result(result)
}

/// 读取文件内容
#[tauri::command]
pub async fn read_file(state: State<'_, AppState>, file_id: String) -> CommandResult<String> {
    info!("读取文件内容: {}", file_id);
    
    let result = state.project_service.read_file_content(&file_id).await;
    to_command_result(result)
}

/// 写入文件内容
#[tauri::command]
pub async fn write_file(state: State<'_, AppState>, file_id: String, content: String) -> CommandResult<()> {
    info!("写入文件内容: {}", file_id);
    
    let result = state.project_service.write_file_content(&file_id, &content).await;
    to_command_result(result)
}

/// 创建文件或目录
#[tauri::command]
pub async fn create_file_or_dir(
    state: State<'_, AppState>,
    project_id: String,
    parent_id: String,
    name: String,
    is_directory: bool,
) -> CommandResult<ProjectFile> {
    info!("创建文件或目录: {}, 父目录: {}, 是否是目录: {}", name, parent_id, is_directory);
    
    let parent_id = if parent_id.is_empty() { None } else { Some(parent_id.as_str()) };
    
    let result = state.project_service.create_file_or_dir(
        &project_id,
        parent_id,
        &name,
        is_directory,
    ).await;
    
    to_command_result(result)
}

/// 删除文件或目录
#[tauri::command]
pub async fn delete_file_or_dir(state: State<'_, AppState>, file_id: String) -> CommandResult<()> {
    info!("删除文件或目录: {}", file_id);
    
    let result = state.project_service.delete_file_or_dir(&file_id).await;
    to_command_result(result)
}

/// 重命名文件
#[tauri::command]
pub async fn rename_file(state: State<'_, AppState>, file_id: String, new_name: String) -> CommandResult<()> {
    info!("重命名文件: {} -> {}", file_id, new_name);
    
    let result = state.project_service.rename_file_or_dir(&file_id, &new_name).await;
    to_command_result(result)
}

/// 检查路径是否存在
#[tauri::command]
pub async fn check_path_exists(state: State<'_, AppState>, path: String) -> CommandResult<bool> {
    debug!("检查路径是否存在: {}", path);
    
    let result = state.project_service.check_path_exists(&path).await;
    to_command_result(result)
}
