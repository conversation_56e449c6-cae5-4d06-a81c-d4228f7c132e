// src-tauri/src/db/db_utils.rs
use rusqlite::{Connection, Result as SqliteResult, params, Row, Transaction};
use log::{error, info, warn};
use std::path::PathBuf;
use thiserror::Error;

/// 数据库操作错误类型
#[derive(Error, Debug)]
pub enum DbUtilsError {
    #[error("数据库连接失败: {0}")]
    ConnectionError(#[from] rusqlite::Error),

    #[error("数据库查询失败: {0}")]
    QueryError(rusqlite::Error),

    #[error("数据库事务失败: {0}")]
    TransactionError(rusqlite::Error),

    #[error("数据库操作失败: {0}")]
    OperationError(String),

    #[error("记录不存在")]
    RecordNotFound,

    #[error("序列化失败: {0}")]
    SerializationError(#[from] serde_json::Error),

    #[error("初始化失败: {0}")]
    InitializationError(String),
}

/// 获取数据库路径
pub fn get_db_path() -> PathBuf {
    // 使用项目根目录作为数据库路径
    let project_root = std::env::current_dir().unwrap_or_else(|_| std::path::PathBuf::from("."));
    project_root.join("tasks.db")
}

/// 打开数据库连接
pub fn open_connection() -> Result<Connection, DbUtilsError> {
    let db_path = get_db_path();
    info!("打开数据库连接: {:?}", db_path);

    match Connection::open(&db_path) {
        Ok(conn) => {
            // 启用外键约束
            let _ = conn.execute("PRAGMA foreign_keys = ON", []);
            Ok(conn)
        },
        Err(e) => {
            error!("无法打开数据库连接: {}", e);
            Err(DbUtilsError::ConnectionError(e))
        }
    }
}

/// 执行SQL查询并返回单个结果
pub fn query_one<T, F>(sql: &str, params: &[&dyn rusqlite::ToSql], row_mapper: F) -> Result<T, DbUtilsError>
where
    F: FnOnce(&Row) -> SqliteResult<T>,
{
    let conn = open_connection()?;
    let mut stmt = conn.prepare(sql)
        .map_err(|e| {
            error!("准备SQL语句失败: {}", e);
            DbUtilsError::QueryError(e)
        })?;

    match stmt.query_row(params, row_mapper) {
        Ok(result) => Ok(result),
        Err(rusqlite::Error::QueryReturnedNoRows) => {
            warn!("查询未返回任何行: {}", sql);
            Err(DbUtilsError::RecordNotFound)
        },
        Err(e) => {
            error!("执行查询失败: {}", e);
            Err(DbUtilsError::QueryError(e))
        }
    }
}

/// 执行SQL查询并返回多个结果
pub fn query_many<T, F>(sql: &str, params: &[&dyn rusqlite::ToSql], row_mapper: F) -> Result<Vec<T>, DbUtilsError>
where
    F: FnMut(&Row) -> SqliteResult<T>,
{
    let conn = open_connection()?;
    let mut stmt = conn.prepare(sql)
        .map_err(|e| {
            error!("准备SQL语句失败: {}", e);
            DbUtilsError::QueryError(e)
        })?;

    let rows = stmt.query_map(params, row_mapper)
        .map_err(|e| {
            error!("执行查询失败: {}", e);
            DbUtilsError::QueryError(e)
        })?;

    let results = rows.collect::<Result<Vec<_>, _>>()
        .map_err(|e| {
            error!("收集查询结果失败: {}", e);
            DbUtilsError::QueryError(e)
        })?;

    Ok(results)
}

/// 执行SQL更新操作
pub fn execute(sql: &str, params: &[&dyn rusqlite::ToSql]) -> Result<usize, DbUtilsError> {
    let conn = open_connection()?;
    conn.execute(sql, params)
        .map_err(|e| {
            error!("执行SQL更新失败: {}", e);
            DbUtilsError::QueryError(e)
        })
}

/// 在事务中执行多个SQL操作
pub fn execute_in_transaction<F, T>(operations: F) -> Result<T, DbUtilsError>
where
    F: FnOnce(&Connection) -> Result<T, DbUtilsError>,
{
    let mut conn = open_connection()?;
    let tx = conn.transaction()
        .map_err(|e| {
            error!("创建事务失败: {}", e);
            DbUtilsError::TransactionError(e)
        })?;

    match operations(&tx) {
        Ok(result) => {
            tx.commit().map_err(|e| {
                error!("提交事务失败: {}", e);
                DbUtilsError::TransactionError(e)
            })?;
            Ok(result)
        },
        Err(e) => {
            let _ = tx.rollback();
            Err(e)
        }
    }
}

/// 批量执行SQL操作
pub fn batch_execute(sql: &str) -> Result<(), DbUtilsError> {
    let conn = open_connection()?;
    conn.execute_batch(sql)
        .map_err(|e| {
            error!("批量执行SQL失败: {}", e);
            DbUtilsError::QueryError(e)
        })
}

/// 检查表是否存在
#[allow(dead_code)]
pub fn table_exists(table_name: &str) -> Result<bool, DbUtilsError> {
    let conn = open_connection()?;
    let mut stmt = conn.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name=?")
        .map_err(|e| DbUtilsError::QueryError(e))?;

    let exists = stmt.exists(params![table_name])
        .map_err(|e| DbUtilsError::QueryError(e))?;

    Ok(exists)
}

/// 通用的记录插入函数
#[allow(dead_code)]
pub fn insert_record<T: serde::Serialize>(
    table: &str,
    _record: &T,
    columns: &[&str],
    values: &[&dyn rusqlite::ToSql]
) -> Result<(), DbUtilsError> {
    if columns.len() != values.len() {
        return Err(DbUtilsError::OperationError(
            format!("列数 ({}) 与值数 ({}) 不匹配", columns.len(), values.len())
        ));
    }

    let placeholders = (0..columns.len())
        .map(|_| "?")
        .collect::<Vec<_>>()
        .join(", ");

    let columns_str = columns.join(", ");

    let sql = format!(
        "INSERT INTO {} ({}) VALUES ({})",
        table, columns_str, placeholders
    );

    execute(&sql, values)?;
    Ok(())
}

/// 通用的记录更新函数
#[allow(dead_code)]
pub fn update_record<T: serde::Serialize>(
    table: &str,
    id_column: &str,
    id_value: &dyn rusqlite::ToSql,
    _record: &T,
    columns: &[&str],
    values: &[&dyn rusqlite::ToSql]
) -> Result<(), DbUtilsError> {
    if columns.len() != values.len() {
        return Err(DbUtilsError::OperationError(
            format!("列数 ({}) 与值数 ({}) 不匹配", columns.len(), values.len())
        ));
    }

    let set_clauses = columns
        .iter()
        .map(|col| format!("{} = ?", col))
        .collect::<Vec<_>>()
        .join(", ");

    let sql = format!(
        "UPDATE {} SET {} WHERE {} = ?",
        table, set_clauses, id_column
    );

    // 创建包含所有参数的新数组
    let mut all_params: Vec<&dyn rusqlite::ToSql> = values.to_vec();
    all_params.push(id_value);

    execute(&sql, &all_params)?;
    Ok(())
}

/// 通用的记录删除函数
#[allow(dead_code)]
pub fn delete_record(
    table: &str,
    id_column: &str,
    id_value: &dyn rusqlite::ToSql
) -> Result<(), DbUtilsError> {
    let sql = format!("DELETE FROM {} WHERE {} = ?", table, id_column);
    execute(&sql, &[id_value])?;
    Ok(())
}

/// 通用的批量删除函数
#[allow(dead_code)]
pub fn batch_delete_records<T: rusqlite::ToSql>(
    table: &str,
    id_column: &str,
    ids: &[T]
) -> Result<(), DbUtilsError> {
    let mut conn = open_connection()?;
    let tx = conn.transaction()
        .map_err(|e| DbUtilsError::TransactionError(e))?;

    let sql = format!("DELETE FROM {} WHERE {} = ?", table, id_column);
    {
        let mut stmt = tx.prepare(&sql)
            .map_err(|e| DbUtilsError::QueryError(e))?;

        for id in ids {
            stmt.execute(params![id])
                .map_err(|e| DbUtilsError::QueryError(e))?;
        }
    }

    tx.commit().map_err(|e| DbUtilsError::TransactionError(e))?;
    Ok(())
}

/// 执行数据库事务并返回结果
#[allow(dead_code)]
pub fn with_transaction<F, T>(f: F) -> Result<T, DbUtilsError>
where
    F: FnOnce(&Transaction) -> Result<T, DbUtilsError>,
{
    let mut conn = open_connection()?;
    let tx = conn.transaction()
        .map_err(|e| DbUtilsError::TransactionError(e))?;

    match f(&tx) {
        Ok(result) => {
            tx.commit().map_err(|e| DbUtilsError::TransactionError(e))?;
            Ok(result)
        },
        Err(e) => {
            let _ = tx.rollback();
            Err(e)
        }
    }
}

/// 通用的分页查询函数
#[allow(dead_code)]
pub fn paginated_query<T, F>(
    sql: &str,
    params: &[&dyn rusqlite::ToSql],
    page: usize,
    page_size: usize,
    row_mapper: F
) -> Result<(Vec<T>, usize), DbUtilsError>
where
    F: FnMut(&Row) -> SqliteResult<T>,
{
    let conn = open_connection()?;

    // 计算总记录数
    let count_sql = format!("SELECT COUNT(*) FROM ({})", sql);
    let total_count: usize = conn.query_row(&count_sql, params, |row| row.get(0))
        .map_err(|e| DbUtilsError::QueryError(e))?;

    // 添加分页
    let offset = (page - 1) * page_size;
    let paginated_sql = format!("{} LIMIT {} OFFSET {}", sql, page_size, offset);

    // 执行查询
    let mut stmt = conn.prepare(&paginated_sql)
        .map_err(|e| DbUtilsError::QueryError(e))?;

    let rows = stmt.query_map(params, row_mapper)
        .map_err(|e| DbUtilsError::QueryError(e))?;

    let results = rows.collect::<Result<Vec<_>, _>>()
        .map_err(|e| DbUtilsError::QueryError(e))?;

    Ok((results, total_count))
}
