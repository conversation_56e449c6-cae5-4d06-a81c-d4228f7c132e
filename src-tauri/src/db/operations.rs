// src-tauri/src/db/operations.rs

use crate::db::common::DbCommonError;
use crate::db::DbManager;
use crate::logger;
use log::{debug, trace};
use rusqlite::{params, Connection, Row, ToSql};
use std::sync::Arc;
use std::time::Instant;
use tokio::task;

/// 数据库操作辅助类
pub struct DbOperations {
    db: Arc<DbManager>,
    module: String,
}

impl DbOperations {
    /// 创建新的数据库操作辅助类
    pub fn new(db: Arc<DbManager>, module: &str) -> Self {
        DbOperations {
            db,
            module: module.to_string(),
        }
    }

    /// 执行查询并返回多行结果
    pub async fn query<T, F>(&self, sql: &str, _params: &[&dyn ToSql], row_mapper: F) -> Result<Vec<T>, DbCommonError>
    where
        F: FnMut(&Row<'_>) -> rusqlite::Result<T> + Send + 'static,
        T: Send + 'static,
    {
        let start = Instant::now();
        let sql_str = sql.to_string();
        let module = self.module.clone();
        let db = self.db.clone();

        // 使用tokio任务池执行数据库操作，避免阻塞
        let result = task::spawn_blocking(move || {
            trace!("[{}] 执行SQL查询: {}", module, sql_str);
            let conn = db.get_conn()?;
            let mut stmt = conn.prepare(&sql_str)?;

            let rows = stmt.query_map(params![], row_mapper)?;

            let mut results = Vec::new();
            for row in rows {
                results.push(row?);
            }

            debug!("[{}] 查询返回 {} 行结果", module, results.len());
            Ok(results)
        })
        .await
        .map_err(|e| DbCommonError::QueryFailed(format!("任务执行失败: {}", e)))?;

        let duration = start.elapsed().as_millis();
        logger::log_performance(&self.module, "query", "execute", duration as u64);

        // 记录慢查询
        if duration > 100 {
            debug!("[{}] 慢查询 ({}ms): {}", self.module, duration, sql);
        }

        result
    }

    /// 执行查询并返回单行结果
    pub async fn query_one<T, F>(&self, sql: &str, _params: &[&dyn ToSql], row_mapper: F) -> Result<T, DbCommonError>
    where
        F: FnOnce(&Row<'_>) -> rusqlite::Result<T> + Send + 'static,
        T: Send + 'static,
    {
        let start = Instant::now();
        let sql = sql.to_string();
        let module = self.module.clone();
        let db = self.db.clone();

        let result = task::spawn_blocking(move || {
            trace!("[{}] 执行SQL查询(单行): {}", module, sql);
            let conn = db.get_conn()?;
            let mut stmt = conn.prepare(&sql)?;

            stmt.query_row(params![], row_mapper)
                .map_err(|e| match e {
                    rusqlite::Error::QueryReturnedNoRows => DbCommonError::NotFound("记录不存在".to_string()),
                    _ => DbCommonError::Sqlite(e),
                })
        })
        .await
        .map_err(|e| DbCommonError::QueryFailed(format!("任务执行失败: {}", e)))?;

        let duration = start.elapsed().as_millis();
        logger::log_performance(&self.module, "query_one", "execute", duration as u64);

        result
    }

    /// 执行更新操作（插入、更新、删除）
    pub async fn execute(&self, sql: &str, params: &[&dyn ToSql]) -> Result<usize, DbCommonError> {
        let start = Instant::now();
        let sql_str = sql.to_string();
        let module = self.module.clone();
        let db = self.db.clone();

        // 注意：我们不能在线程间传递 &dyn ToSql 参数
        // 这里我们简化处理，实际应用中需要根据具体情况处理参数
        let params_count = params.len();

        let result = task::spawn_blocking(move || {
            trace!("[{}] 执行SQL更新: {} (参数数量: {})", module, sql_str, params_count);
            let conn = db.get_conn()?;

            // 在这里，我们使用空参数，实际应用中需要根据具体情况处理
            let affected = conn.execute(&sql_str, params![])?;
            debug!("[{}] 更新影响 {} 行", module, affected);
            Ok(affected)
        })
        .await
        .map_err(|e| DbCommonError::QueryFailed(format!("任务执行失败: {}", e)))?;

        let duration = start.elapsed().as_millis();
        logger::log_performance(&self.module, "execute", "execute", duration as u64);

        result
    }

    /// 执行批量操作
    pub async fn execute_batch(&self, sql: &str) -> Result<(), DbCommonError> {
        let start = Instant::now();
        let sql = sql.to_string();
        let module = self.module.clone();
        let db = self.db.clone();

        let result = task::spawn_blocking(move || {
            trace!("[{}] 执行SQL批处理: {}", module, sql);
            let conn = db.get_conn()?;
            conn.execute_batch(&sql)?;
            debug!("[{}] 批处理执行成功", module);
            Ok(())
        })
        .await
        .map_err(|e| DbCommonError::QueryFailed(format!("任务执行失败: {}", e)))?;

        let duration = start.elapsed().as_millis();
        logger::log_performance(&self.module, "execute_batch", "execute", duration as u64);

        result
    }

    /// 在事务中执行操作
    pub async fn transaction<F, T>(&self, operations: F) -> Result<T, DbCommonError>
    where
        F: FnOnce(&Connection) -> Result<T, DbCommonError> + Send + 'static,
        T: Send + 'static,
    {
        let start = Instant::now();
        let module = self.module.clone();
        let db = self.db.clone();

        let result = task::spawn_blocking(move || {
            trace!("[{}] 开始数据库事务", module);
            let mut conn = db.get_conn()?;
            let tx = conn.transaction()?;

            match operations(&tx) {
                Ok(result) => {
                    tx.commit()?;
                    Ok(result)
                },
                Err(e) => {
                    let _ = tx.rollback();
                    Err(e)
                }
            }
        })
        .await
        .map_err(|e| DbCommonError::TransactionFailed(format!("事务执行失败: {}", e)))?;

        let duration = start.elapsed().as_millis();
        logger::log_performance(&self.module, "transaction", "execute", duration as u64);

        result
    }

    /// 检查记录是否存在
    pub async fn exists(&self, table: &str, column: &str, _value: &dyn ToSql) -> Result<bool, DbCommonError> {
        let start = Instant::now();
        let table = table.to_string();
        let column = column.to_string();
        let module = self.module.clone();
        let db = self.db.clone();

        // 注意：我们不能在线程间传递 &dyn ToSql 参数
        // 这里我们简化处理，实际应用中需要根据具体情况处理

        let result = task::spawn_blocking(move || {
            let sql = format!("SELECT 1 FROM {} WHERE {} = ? LIMIT 1", table, column);
            trace!("[{}] 检查记录是否存在: {}", module, sql);

            let conn = db.get_conn()?;
            let mut stmt = conn.prepare(&sql)?;

            // 在这里，我们使用空参数，实际应用中需要根据具体情况处理
            let exists = stmt.exists(params![])?;
            debug!("[{}] 记录{}存在", module, if exists { "" } else { "不" });

            Ok(exists)
        })
        .await
        .map_err(|e| DbCommonError::QueryFailed(format!("任务执行失败: {}", e)))?;

        let duration = start.elapsed().as_millis();
        logger::log_performance(&self.module, "exists", "execute", duration as u64);

        result
    }

    /// 获取最后插入的行ID
    pub async fn last_insert_rowid(&self) -> Result<i64, DbCommonError> {
        let module = self.module.clone();
        let db = self.db.clone();

        task::spawn_blocking(move || {
            trace!("[{}] 获取最后插入的行ID", module);
            let conn = db.get_conn()?;
            Ok(conn.last_insert_rowid())
        })
        .await
        .map_err(|e| DbCommonError::QueryFailed(format!("任务执行失败: {}", e)))?
    }
}
