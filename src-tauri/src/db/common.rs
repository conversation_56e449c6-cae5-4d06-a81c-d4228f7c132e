// src-tauri/src/db/common.rs
// use thiserror::Error;
use rusqlite::Error as SqliteError;
use std::fmt;

/// 数据库操作错误
#[derive(Debug)]
pub enum DbCommonError {
    /// SQLite 错误
    Sqlite(SqliteError),

    /// 数据库工具错误
    DbUtils(crate::db::db_utils::DbUtilsError),

    /// 数据库错误（通用）
    DatabaseError(SqliteError),

    /// 数据库错误（来自DbUtilsError）
    DbError(crate::db::db_utils::DbUtilsError),

    /// 记录不存在
    RecordNotFound,

    /// 记录不存在（带消息）
    NotFound(String),

    /// 无效的输入
    InvalidInput(String),

    /// 操作失败
    OperationFailed(String),

    /// 查询失败
    QueryFailed(String),

    /// 事务失败
    TransactionFailed(String),
}

impl fmt::Display for DbCommonError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            DbCommonError::Sqlite(e) => write!(f, "数据库错误: {}", e),
            DbCommonError::DbUtils(e) => write!(f, "数据库工具错误: {}", e),
            DbCommonError::DatabaseError(e) => write!(f, "数据库错误: {}", e),
            DbCommonError::DbError(e) => write!(f, "数据库错误: {}", e),
            DbCommonError::RecordNotFound => write!(f, "记录不存在"),
            DbCommonError::NotFound(msg) => write!(f, "记录不存在: {}", msg),
            DbCommonError::InvalidInput(msg) => write!(f, "无效的输入: {}", msg),
            DbCommonError::OperationFailed(msg) => write!(f, "操作失败: {}", msg),
            DbCommonError::QueryFailed(msg) => write!(f, "查询失败: {}", msg),
            DbCommonError::TransactionFailed(msg) => write!(f, "事务失败: {}", msg),
        }
    }
}

impl std::error::Error for DbCommonError {}

impl From<SqliteError> for DbCommonError {
    fn from(error: SqliteError) -> Self {
        DbCommonError::Sqlite(error)
    }
}

impl From<crate::db::db_utils::DbUtilsError> for DbCommonError {
    fn from(error: crate::db::db_utils::DbUtilsError) -> Self {
        DbCommonError::DbUtils(error)
    }
}

/// 数据库操作接口
pub trait DbOperations<T, ID> {
    /// 获取所有记录
    fn get_all(&self) -> Result<Vec<T>, DbCommonError>;

    /// 根据 ID 获取记录
    fn get_by_id(&self, id: ID) -> Result<T, DbCommonError>;

    /// 创建记录
    fn create(&self, item: &T) -> Result<(), DbCommonError>;

    /// 更新记录
    fn update(&self, item: &T) -> Result<(), DbCommonError>;

    /// 删除记录
    fn delete(&self, id: ID) -> Result<(), DbCommonError>;
}
