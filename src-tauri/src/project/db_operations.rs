// src-tauri/src/project/db_operations.rs
use crate::db::common::{DbCommonError, DbOperations};
use super::models::{Project, ProjectFile};
use crate::models::DbDateTime;
use rusqlite::{Connection, params};
use std::sync::{Arc, Mutex};
use log::{error, info};
use chrono::Utc;

// 全局数据库连接
lazy_static::lazy_static! {
    static ref DB_CONN: Arc<Mutex<Connection>> = {
        let conn = crate::db::db_utils::open_connection()
            .expect("Failed to open database connection");
        Arc::new(Mutex::new(conn))
    };
}

// 获取项目数据库操作实例
pub fn get_project_db_ops() -> ProjectDbOperations {
    ProjectDbOperations::new(DB_CONN.clone())
}

// 初始化项目数据库表
pub fn init_project_tables() -> Result<(), DbCommonError> {
    let db_ops = get_project_db_ops();
    db_ops.init_tables()
}

// 获取项目文件列表
pub fn get_project_files(project_id: &str) -> Result<Vec<ProjectFile>, DbCommonError> {
    let db_ops = get_project_db_ops();
    db_ops.get_project_files(project_id)
}

// 获取文件内容
pub fn get_file_content(file_id: &str) -> Result<String, DbCommonError> {
    let db_ops = get_project_db_ops();
    let file = db_ops.get_file_by_id(file_id)?;

    if file.is_directory {
        return Err(DbCommonError::InvalidInput("Cannot get content of a directory".to_string()));
    }

    match file.content {
        Some(content) => Ok(content),
        None => Ok(String::new())
    }
}

// 保存文件内容
pub fn save_file_content(file_id: &str, content: &str) -> Result<(), DbCommonError> {
    let db_ops = get_project_db_ops();
    let mut file = db_ops.get_file_by_id(file_id)?;

    if file.is_directory {
        return Err(DbCommonError::InvalidInput("Cannot save content to a directory".to_string()));
    }

    file.content = Some(content.to_string());
    file.size = Some(content.len() as u64);
    file.last_modified = Some(DbDateTime(Utc::now()));

    db_ops.update_file(&file)
}

// 创建文件
pub fn create_file(file: &ProjectFile) -> Result<(), DbCommonError> {
    let db_ops = get_project_db_ops();
    db_ops.create_file(file)
}

// 删除文件
pub fn delete_file(file_id: &str) -> Result<(), DbCommonError> {
    let db_ops = get_project_db_ops();
    db_ops.delete_file(file_id)
}

// 重命名文件
pub fn rename_file(file_id: &str, new_name: &str) -> Result<(), DbCommonError> {
    let db_ops = get_project_db_ops();
    let mut file = db_ops.get_file_by_id(file_id)?;

    // 更新文件名和路径
    let old_path = file.path.clone();
    let parent_path = match old_path.rfind('/') {
        Some(pos) => &old_path[0..pos],
        None => "",
    };

    let new_path = if parent_path.is_empty() {
        new_name.to_string()
    } else {
        format!("{}/{}", parent_path, new_name)
    };

    file.name = new_name.to_string();
    file.path = new_path;
    file.last_modified = Some(DbDateTime(Utc::now()));

    db_ops.update_file(&file)
}

/// 项目数据库操作
pub struct ProjectDbOperations {
    conn: Arc<Mutex<Connection>>,
}

impl ProjectDbOperations {
    /// 创建新的项目数据库操作
    pub fn new(conn: Arc<Mutex<Connection>>) -> Self {
        ProjectDbOperations { conn }
    }

    /// 初始化项目数据库表
    pub fn init_tables(&self) -> Result<(), DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        // 创建项目表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                path TEXT NOT NULL,
                status TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                created_by TEXT NOT NULL,
                leader TEXT,
                tags TEXT
            )",
            [],
        ).map_err(DbCommonError::DatabaseError)?;

        // 创建项目文件表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS project_files (
                id TEXT PRIMARY KEY,
                project_id TEXT NOT NULL,
                name TEXT NOT NULL,
                path TEXT NOT NULL,
                is_directory INTEGER NOT NULL,
                size INTEGER,
                last_modified TEXT,
                content TEXT,
                parent_id TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
            )",
            [],
        ).map_err(DbCommonError::DatabaseError)?;

        info!("项目数据库表初始化成功");
        Ok(())
    }

    /// 获取项目文件列表
    pub fn get_project_files(&self, project_id: &str) -> Result<Vec<ProjectFile>, DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        let mut stmt = conn.prepare(
            "SELECT id, project_id, name, path, is_directory, size, last_modified, content, parent_id
             FROM project_files
             WHERE project_id = ?"
        ).map_err(DbCommonError::DatabaseError)?;

        let files = stmt.query_map(params![project_id], |row| {
            let now = chrono::Utc::now();
            Ok(ProjectFile {
                id: row.get(0)?,
                project_id: row.get(1)?,
                name: row.get(2)?,
                path: row.get(3)?,
                is_directory: row.get(4)?,
                size: row.get(5)?,
                last_modified: row.get::<_, Option<String>>(6)?.map(|dt| {
                    DbDateTime(chrono::DateTime::parse_from_rfc3339(&dt)
                        .unwrap_or_else(|_| chrono::Utc::now().into())
                        .with_timezone(&chrono::Utc))
                }),
                content: row.get(7)?,
                parent_id: row.get(8)?,
                created_at: DbDateTime(now),
                updated_at: DbDateTime(now),
                created_by: "System".to_string(),
            })
        }).map_err(DbCommonError::DatabaseError)?;

        let mut result = Vec::new();
        for file in files {
            result.push(file.map_err(DbCommonError::DatabaseError)?);
        }

        Ok(result)
    }

    /// 根据 ID 获取项目文件
    pub fn get_file_by_id(&self, file_id: &str) -> Result<ProjectFile, DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        let mut stmt = conn.prepare(
            "SELECT id, project_id, name, path, is_directory, size, last_modified, content, parent_id
             FROM project_files
             WHERE id = ?"
        ).map_err(DbCommonError::DatabaseError)?;

        stmt.query_row(params![file_id], |row| {
            let now = chrono::Utc::now();
            Ok(ProjectFile {
                id: row.get(0)?,
                project_id: row.get(1)?,
                name: row.get(2)?,
                path: row.get(3)?,
                is_directory: row.get(4)?,
                size: row.get(5)?,
                last_modified: row.get::<_, Option<String>>(6)?.map(|dt| {
                    DbDateTime(chrono::DateTime::parse_from_rfc3339(&dt)
                        .unwrap_or_else(|_| chrono::Utc::now().into())
                        .with_timezone(&chrono::Utc))
                }),
                content: row.get(7)?,
                parent_id: row.get(8)?,
                created_at: DbDateTime(now),
                updated_at: DbDateTime(now),
                created_by: "System".to_string(),
            })
        }).map_err(|e| match e {
            rusqlite::Error::QueryReturnedNoRows => DbCommonError::RecordNotFound,
            _ => DbCommonError::DatabaseError(e),
        })
    }

    /// 创建项目文件
    pub fn create_file(&self, file: &ProjectFile) -> Result<(), DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        conn.execute(
            "INSERT INTO project_files (id, project_id, name, path, is_directory, size, last_modified, content, parent_id)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            params![
                file.id,
                file.project_id,
                file.name,
                file.path,
                file.is_directory,
                file.size,
                file.last_modified.as_ref().map(|dt| dt.0.to_rfc3339()),
                file.content,
                file.parent_id,
            ],
        ).map_err(DbCommonError::DatabaseError)?;

        Ok(())
    }

    /// 更新项目文件
    pub fn update_file(&self, file: &ProjectFile) -> Result<(), DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        conn.execute(
            "UPDATE project_files
             SET name = ?, path = ?, is_directory = ?, size = ?, last_modified = ?, content = ?, parent_id = ?
             WHERE id = ?",
            params![
                file.name,
                file.path,
                file.is_directory,
                file.size,
                file.last_modified.as_ref().map(|dt| dt.0.to_rfc3339()),
                file.content,
                file.parent_id,
                file.id,
            ],
        ).map_err(DbCommonError::DatabaseError)?;

        Ok(())
    }

    /// 删除项目文件
    pub fn delete_file(&self, file_id: &str) -> Result<(), DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        conn.execute(
            "DELETE FROM project_files WHERE id = ?",
            params![file_id],
        ).map_err(DbCommonError::DatabaseError)?;

        Ok(())
    }
}

impl DbOperations<Project, &str> for ProjectDbOperations {
    fn get_all(&self) -> Result<Vec<Project>, DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        let mut stmt = conn.prepare(
            "SELECT id, name, description, path, status, created_at, updated_at, created_by, leader, tags
             FROM projects"
        ).map_err(DbCommonError::DatabaseError)?;

        let projects = stmt.query_map([], |row| {
            Ok(Project {
                id: row.get(0)?,
                name: row.get(1)?,
                description: row.get(2)?,
                path: row.get(3)?,
                status: match row.get::<_, String>(4)?.as_str() {
                    "Active" => super::models::ProjectStatus::Active,
                    "Archived" => super::models::ProjectStatus::Archived,
                    "Completed" => super::models::ProjectStatus::Completed,
                    _ => super::models::ProjectStatus::Active,
                },
                created_at: DbDateTime(chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?)
                    .unwrap_or_else(|_| chrono::Utc::now().into())
                    .with_timezone(&chrono::Utc)),
                updated_at: DbDateTime(chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(6)?)
                    .unwrap_or_else(|_| chrono::Utc::now().into())
                    .with_timezone(&chrono::Utc)),
                created_by: row.get(7)?,
                leader: row.get(8)?,
                tags: row.get(9)?,
            })
        }).map_err(DbCommonError::DatabaseError)?;

        let mut result = Vec::new();
        for project in projects {
            result.push(project.map_err(DbCommonError::DatabaseError)?);
        }

        Ok(result)
    }

    fn get_by_id(&self, id: &str) -> Result<Project, DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        let mut stmt = conn.prepare(
            "SELECT id, name, description, path, status, created_at, updated_at, created_by, leader, tags
             FROM projects
             WHERE id = ?"
        ).map_err(DbCommonError::DatabaseError)?;

        stmt.query_row(params![id], |row| {
            Ok(Project {
                id: row.get(0)?,
                name: row.get(1)?,
                description: row.get(2)?,
                path: row.get(3)?,
                status: match row.get::<_, String>(4)?.as_str() {
                    "Active" => super::models::ProjectStatus::Active,
                    "Archived" => super::models::ProjectStatus::Archived,
                    "Completed" => super::models::ProjectStatus::Completed,
                    _ => super::models::ProjectStatus::Active,
                },
                created_at: DbDateTime(chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?)
                    .unwrap_or_else(|_| chrono::Utc::now().into())
                    .with_timezone(&chrono::Utc)),
                updated_at: DbDateTime(chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(6)?)
                    .unwrap_or_else(|_| chrono::Utc::now().into())
                    .with_timezone(&chrono::Utc)),
                created_by: row.get(7)?,
                leader: row.get(8)?,
                tags: row.get(9)?,
            })
        }).map_err(|e| match e {
            rusqlite::Error::QueryReturnedNoRows => DbCommonError::RecordNotFound,
            _ => DbCommonError::DatabaseError(e),
        })
    }

    fn create(&self, project: &Project) -> Result<(), DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        conn.execute(
            "INSERT INTO projects (id, name, description, path, status, created_at, updated_at, created_by, leader, tags)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            params![
                project.id,
                project.name,
                project.description,
                project.path,
                match project.status {
                    super::models::ProjectStatus::Active => "Active",
                    super::models::ProjectStatus::Archived => "Archived",
                    super::models::ProjectStatus::Completed => "Completed",
                },
                project.created_at.0.to_rfc3339(),
                project.updated_at.0.to_rfc3339(),
                project.created_by,
                project.leader,
                project.tags,
            ],
        ).map_err(DbCommonError::DatabaseError)?;

        Ok(())
    }

    fn update(&self, project: &Project) -> Result<(), DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        conn.execute(
            "UPDATE projects
             SET name = ?, description = ?, path = ?, status = ?, updated_at = ?, leader = ?, tags = ?
             WHERE id = ?",
            params![
                project.name,
                project.description,
                project.path,
                match project.status {
                    super::models::ProjectStatus::Active => "Active",
                    super::models::ProjectStatus::Archived => "Archived",
                    super::models::ProjectStatus::Completed => "Completed",
                },
                Utc::now().to_rfc3339(),
                project.leader,
                project.tags,
                project.id,
            ],
        ).map_err(DbCommonError::DatabaseError)?;

        Ok(())
    }

    fn delete(&self, id: &str) -> Result<(), DbCommonError> {
        let conn = self.conn.lock().map_err(|e| {
            error!("获取数据库连接锁失败: {}", e);
            DbCommonError::OperationFailed(format!("获取数据库连接锁失败: {}", e))
        })?;

        conn.execute(
            "DELETE FROM projects WHERE id = ?",
            params![id],
        ).map_err(DbCommonError::DatabaseError)?;

        Ok(())
    }
}
