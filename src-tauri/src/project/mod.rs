// src-tauri/src/project/mod.rs
pub mod commands;
pub mod service;
pub mod models;
pub mod db_operations;

use std::error::Error;
use log::{info};

/// 初始化项目模块
pub fn init() -> Result<(), Box<dyn Error>> {
    info!("初始化项目模块");

    // 初始化项目数据库表
    match db_operations::init_project_tables() {
        Ok(_) => {
            info!("项目数据库表初始化成功");
            Ok(())
        },
        Err(e) => {
            log::error!("项目数据库表初始化失败: {}", e);
            Err(Box::new(e))
        }
    }
}

// 导出项目命令
pub use commands::{
    get_project_list,
    get_projects,
    get_project_by_id,
    create_new_project,
    update_project_info,
    delete_project_by_id,
    get_project_files,
    get_file_by_id,
    read_file,
    write_file,
    create_file_or_dir,
    delete_file_or_dir,
    rename_file,
    scan_project_directory,
    scan_project_files
};
