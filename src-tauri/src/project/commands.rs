// src-tauri/src/project/commands.rs
// use tauri::State;
// use crate::models::AppState;
use super::models::{Project, ProjectFile, ProjectStatus};
use super::service::{
    get_all_projects, get_project, create_project, update_project, delete_project,
    get_files, get_file, read_file_content, write_file_content,
    create_new_file, remove_file, rename_file_or_dir, scan_directory
};
use log::{error, info, warn};
use crate::logger;
use std::path::Path;

#[tauri::command]
pub async fn get_project_list() -> Result<Vec<Project>, String> {
    info!("获取所有项目");
    logger::log_module_operation("project_commands", "get_projects", "获取所有项目", "");

    match get_all_projects() {
        Ok(projects) => {
            info!("成功获取 {} 个项目", projects.len());
            logger::log_module_operation("project_commands", "get_projects", "成功获取所有项目", &format!("共 {} 个项目", projects.len()));
            Ok(projects)
        },
        Err(e) => {
            error!("获取项目失败: {}", e);
            logger::log_error("project_commands", "get_projects", &e.to_string(), "获取项目失败");
            Err(e.to_string())
        }
    }
}

/// 获取项目列表（别名，与 get_project_list 功能相同）
#[tauri::command]
pub async fn get_projects() -> Result<Vec<Project>, String> {
    // 直接调用 get_project_list 函数
    get_project_list().await
}

#[tauri::command]
pub async fn get_project_by_id(id: String) -> Result<Project, String> {
    info!("获取项目详情: {}", id);
    logger::log_module_operation("project_commands", "get_project_by_id", "获取项目详情", &id);

    match get_project(&id) {
        Ok(project) => {
            info!("成功获取项目详情: {}", project.name);
            logger::log_module_operation("project_commands", "get_project_by_id", "成功获取项目详情", &project.name);
            Ok(project)
        },
        Err(e) => {
            error!("获取项目详情失败: {}", e);
            logger::log_error("project_commands", "get_project_by_id", &e.to_string(), "获取项目详情失败");
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn create_new_project(name: String, description: Option<String>, leader: Option<String>, tags: Option<String>) -> Result<Project, String> {
    info!("创建项目: {}", name);
    logger::log_module_operation("project_commands", "create_new_project", "创建项目", &name);

    match create_project(
        &name,
        description.as_deref(),
        leader.as_deref(),
        tags.as_deref()
    ) {
        Ok(project) => {
            info!("成功创建项目: {}", project.name);
            logger::log_module_operation("project_commands", "create_new_project", "成功创建项目", &project.name);
            Ok(project)
        },
        Err(e) => {
            error!("创建项目失败: {}", e);
            logger::log_error("project_commands", "create_new_project", &e.to_string(), "创建项目失败");
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn update_project_info(id: String, name: String, description: Option<String>, status: String, leader: Option<String>, tags: Option<String>) -> Result<(), String> {
    info!("更新项目: {}", id);
    logger::log_module_operation("project_commands", "update_project_info", "更新项目", &id);

    // 解析状态
    let project_status = match status.as_str() {
        "Active" => ProjectStatus::Active,
        "Completed" => ProjectStatus::Completed,
        "Archived" => ProjectStatus::Archived,
        _ => {
            warn!("无效的项目状态: {}", status);
            logger::log_warning("project_commands", "update_project_info", "无效的项目状态", &status);
            return Err(format!("无效的项目状态: {}", status));
        }
    };

    match update_project(
        &id,
        &name,
        description.as_deref(),
        project_status,
        leader.as_deref(),
        tags.as_deref()
    ) {
        Ok(_) => {
            info!("成功更新项目: {}", id);
            logger::log_module_operation("project_commands", "update_project_info", "成功更新项目", &id);
            Ok(())
        },
        Err(e) => {
            error!("更新项目失败: {}", e);
            logger::log_error("project_commands", "update_project_info", &e.to_string(), "更新项目失败");
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn delete_project_by_id(id: String) -> Result<(), String> {
    info!("删除项目: {}", id);
    logger::log_module_operation("project_commands", "delete_project_by_id", "删除项目", &id);

    match delete_project(&id) {
        Ok(_) => {
            info!("成功删除项目: {}", id);
            logger::log_module_operation("project_commands", "delete_project_by_id", "成功删除项目", &id);
            Ok(())
        },
        Err(e) => {
            error!("删除项目失败: {}", e);
            logger::log_error("project_commands", "delete_project_by_id", &e.to_string(), "删除项目失败");
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn get_project_files(project_id: String) -> Result<Vec<ProjectFile>, String> {
    info!("获取项目文件: {}", project_id);
    logger::log_module_operation("project_commands", "get_project_files", "获取项目文件", &project_id);

    match get_files(&project_id) {
        Ok(files) => {
            info!("成功获取项目文件: {} 个文件", files.len());
            logger::log_module_operation("project_commands", "get_project_files", "成功获取项目文件", &format!("共 {} 个文件", files.len()));
            Ok(files)
        },
        Err(e) => {
            error!("获取项目文件失败: {}", e);
            logger::log_error("project_commands", "get_project_files", &e.to_string(), "获取项目文件失败");
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn get_file_by_id(file_id: String) -> Result<ProjectFile, String> {
    info!("获取文件: {}", file_id);
    logger::log_module_operation("project_commands", "get_file_by_id", "获取文件", &file_id);

    match get_file(&file_id) {
        Ok(file) => {
            info!("成功获取文件: {}", file.path);
            logger::log_module_operation("project_commands", "get_file_by_id", "成功获取文件", &file.path);
            Ok(file)
        },
        Err(e) => {
            error!("获取文件失败: {}", e);
            logger::log_error("project_commands", "get_file_by_id", &e.to_string(), "获取文件失败");
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn read_file(file_id: String) -> Result<String, String> {
    info!("读取文件内容: {}", file_id);
    logger::log_module_operation("project_commands", "read_file", "读取文件内容", &file_id);

    match read_file_content(&file_id) {
        Ok(content) => {
            info!("成功读取文件内容: {} 字节", content.len());
            logger::log_module_operation("project_commands", "read_file", "成功读取文件内容", &format!("内容长度: {} 字节", content.len()));
            Ok(content)
        },
        Err(e) => {
            error!("读取文件内容失败: {}", e);
            logger::log_error("project_commands", "read_file", &e.to_string(), "读取文件内容失败");
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn write_file(file_id: String, content: String) -> Result<(), String> {
    info!("写入文件内容: {}", file_id);
    logger::log_module_operation("project_commands", "write_file", "写入文件内容", &file_id);

    match write_file_content(&file_id, &content) {
        Ok(_) => {
            info!("成功写入文件内容: {} 字节", content.len());
            logger::log_module_operation("project_commands", "write_file", "成功写入文件内容", &format!("内容长度: {} 字节", content.len()));
            Ok(())
        },
        Err(e) => {
            error!("写入文件内容失败: {}", e);
            logger::log_error("project_commands", "write_file", &e.to_string(), "写入文件内容失败");
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn create_file_or_dir(project_id: String, parent_id: String, name: String, is_directory: bool) -> Result<ProjectFile, String> {
    info!("创建{}: {}", if is_directory { "目录" } else { "文件" }, name);
    logger::log_module_operation("project_commands", "create_file_or_dir",
        &format!("创建{}: {}", if is_directory { "目录" } else { "文件" }, name),
        &format!("项目: {}, 父目录: {}", project_id, parent_id));

    match create_new_file(&project_id, &parent_id, &name, is_directory) {
        Ok(file) => {
            info!("成功创建{}: {}", if is_directory { "目录" } else { "文件" }, file.path);
            logger::log_module_operation("project_commands", "create_file_or_dir",
                &format!("成功创建{}", if is_directory { "目录" } else { "文件" }),
                &file.path);
            Ok(file)
        },
        Err(e) => {
            error!("创建{}失败: {}", if is_directory { "目录" } else { "文件" }, e);
            logger::log_error("project_commands", "create_file_or_dir", &e.to_string(),
                &format!("创建{}失败", if is_directory { "目录" } else { "文件" }));
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn delete_file_or_dir(file_id: String) -> Result<(), String> {
    info!("删除文件: {}", file_id);
    logger::log_module_operation("project_commands", "delete_file_or_dir", "删除文件", &file_id);

    match remove_file(&file_id) {
        Ok(_) => {
            info!("成功删除文件: {}", file_id);
            logger::log_module_operation("project_commands", "delete_file_or_dir", "成功删除文件", &file_id);
            Ok(())
        },
        Err(e) => {
            error!("删除文件失败: {}", e);
            logger::log_error("project_commands", "delete_file_or_dir", &e.to_string(), "删除文件失败");
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn rename_file(file_id: String, new_name: String) -> Result<(), String> {
    info!("重命名文件: {} -> {}", file_id, new_name);
    logger::log_module_operation("project_commands", "rename_file", "重命名文件", &format!("文件ID: {}, 新名称: {}", file_id, new_name));

    match rename_file_or_dir(&file_id, &new_name) {
        Ok(_) => {
            info!("成功重命名文件: {} -> {}", file_id, new_name);
            logger::log_module_operation("project_commands", "rename_file", "成功重命名文件", &format!("文件ID: {}, 新名称: {}", file_id, new_name));
            Ok(())
        },
        Err(e) => {
            error!("重命名文件失败: {}", e);
            logger::log_error("project_commands", "rename_file", &e.to_string(), "重命名文件失败");
            Err(e.to_string())
        }
    }
}

/// 扫描项目文件
#[tauri::command]
pub async fn scan_project_files(project_id: String) -> Result<(), String> {
    info!("扫描项目文件: {}", project_id);
    logger::log_module_operation("project_commands", "scan_project_files", "扫描项目文件", &project_id);

    // 获取项目信息
    let project = match get_project(&project_id) {
        Ok(p) => p,
        Err(e) => {
            error!("获取项目信息失败: {}", e);
            logger::log_error("project_commands", "scan_project_files", &e.to_string(), "获取项目信息失败");
            return Err(e.to_string());
        }
    };

    // 从项目描述中提取路径
    let path = match project.description {
        Some(desc) if desc.starts_with("项目路径: ") => {
            desc.replace("项目路径: ", "")
        },
        _ => {
            let error_msg = "项目描述中未包含路径信息".to_string();
            error!("{}", error_msg);
            logger::log_error("project_commands", "scan_project_files", &error_msg, "扫描项目文件失败");
            return Err(error_msg);
        }
    };

    // 检查路径是否存在
    if !Path::new(&path).exists() {
        let error_msg = format!("项目路径不存在: {}", path);
        error!("{}", error_msg);
        logger::log_error("project_commands", "scan_project_files", &error_msg, "扫描项目文件失败");
        return Err(error_msg);
    }

    // 扫描目录
    match scan_directory(&project_id, &path) {
        Ok(_) => {
            info!("成功扫描项目文件: {}", project_id);
            logger::log_module_operation("project_commands", "scan_project_files", "成功扫描项目文件", &project_id);
            Ok(())
        },
        Err(e) => {
            error!("扫描项目文件失败: {}", e);
            logger::log_error("project_commands", "scan_project_files", &e.to_string(), "扫描项目文件失败");
            Err(e.to_string())
        }
    }
}

/// 扫描项目目录
#[tauri::command]
pub async fn scan_project_directory(project_id: String, directory_path: String) -> Result<(), String> {
    info!("扫描项目目录: {}, 路径: {}", project_id, directory_path);
    logger::log_module_operation("project_commands", "scan_project_directory", "扫描项目目录",
        &format!("项目ID: {}, 路径: {}", project_id, directory_path));

    // 检查路径是否存在
    if !Path::new(&directory_path).exists() {
        let error_msg = format!("目录路径不存在: {}", directory_path);
        error!("{}", error_msg);
        logger::log_error("project_commands", "scan_project_directory", &error_msg, "扫描项目目录失败");
        return Err(error_msg);
    }

    // 扫描目录
    match scan_directory(&project_id, &directory_path) {
        Ok(_) => {
            info!("成功扫描项目目录: {}", project_id);
            logger::log_module_operation("project_commands", "scan_project_directory", "成功扫描项目目录", &project_id);
            Ok(())
        },
        Err(e) => {
            error!("扫描项目目录失败: {}", e);
            logger::log_error("project_commands", "scan_project_directory", &e.to_string(), "扫描项目目录失败");
            Err(e.to_string())
        }
    }
}
