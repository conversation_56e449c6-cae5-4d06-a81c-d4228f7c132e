// src-tauri/src/project/service.rs
use uuid::Uuid;
use crate::models::DbDateTime;
// use rusqlite::params;
// use crate::db::db_utils;
use crate::db::common::{DbCommonError, DbOperations};
use super::models::{Project, ProjectFile, ProjectStatus};
use super::db_operations::{
    get_project_db_ops, get_project_files,
    get_file_content, save_file_content, create_file, delete_file,
    rename_file, init_project_tables
};
use crate::logger;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ProjectServiceError {
    #[error("数据库错误: {0}")]
    DbError(#[from] DbCommonError),

    #[error("无效请求: {0}")]
    InvalidRequest(String),

    #[error("项目未找到: {0}")]
    ProjectNotFound(String),

    #[error("文件未找到: {0}")]
    FileNotFound(String),

    #[error("文件已存在: {0}")]
    FileExists(String),

    #[error("操作失败: {0}")]
    OperationFailed(String),
}

/// 获取所有项目
pub fn get_all_projects() -> Result<Vec<Project>, ProjectServiceError> {
    logger::log_module_operation("project", "get_all_projects", "获取所有项目", "");

    let db_ops = get_project_db_ops();
    match db_ops.get_all() {
        Ok(projects) => {
            logger::log_module_operation("project", "get_all_projects", "成功获取所有项目", &format!("共 {} 个项目", projects.len()));
            Ok(projects)
        },
        Err(e) => {
            logger::log_error("project", "get_all_projects", &e.to_string(), "获取所有项目失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 获取项目详情
pub fn get_project(project_id: &str) -> Result<Project, ProjectServiceError> {
    logger::log_module_operation("project", "get_project", "获取项目详情", project_id);

    let db_ops = get_project_db_ops();
    match db_ops.get_by_id(project_id) {
        Ok(project) => {
            logger::log_module_operation("project", "get_project", "成功获取项目详情", &project.name);
            Ok(project)
        },
        Err(DbCommonError::NotFound(_)) => {
            logger::log_warning("project", "get_project", "项目未找到", project_id);
            Err(ProjectServiceError::ProjectNotFound(project_id.to_string()))
        },
        Err(e) => {
            logger::log_error("project", "get_project", &e.to_string(), "获取项目详情失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 创建项目
pub fn create_project(name: &str, description: Option<&str>, leader: Option<&str>, tags: Option<&str>) -> Result<Project, ProjectServiceError> {
    // 验证参数
    if name.trim().is_empty() {
        return Err(ProjectServiceError::InvalidRequest("项目名称不能为空".to_string()));
    }

    logger::log_module_operation("project", "create_project", "创建项目", name);

    let now = DbDateTime(chrono::Utc::now());
    let project_id = Uuid::new_v4().to_string();

    let project = Project {
        id: project_id.clone(),
        name: name.to_string(),
        description: description.map(|s| s.to_string()),
        path: name.to_string(), // 使用项目名称作为路径
        status: ProjectStatus::Active,
        created_at: now.clone(),
        updated_at: now.clone(),
        created_by: "System".to_string(), // 后续可以改为当前用户
        leader: leader.map(|s| s.to_string()),
        tags: tags.map(|s| s.to_string()),
    };

    let db_ops = get_project_db_ops();
    match db_ops.create(&project) {
        Ok(_) => {
            // 创建项目根目录
            let root_dir = ProjectFile {
                id: Uuid::new_v4().to_string(),
                project_id: project_id.clone(),
                name: name.to_string(),
                path: name.to_string(),
                content: None,
                size: Some(0),
                is_directory: true,
                last_modified: Some(now.clone()),
                parent_id: None,
                created_at: now.clone(),
                updated_at: now,
                created_by: "System".to_string(), // 后续可以改为当前用户
            };

            match create_file(&root_dir) {
                Ok(_) => {
                    logger::log_module_operation("project", "create_project", "成功创建项目", &project.name);
                    Ok(project)
                },
                Err(e) => {
                    // 如果创建根目录失败，删除项目
                    let _ = db_ops.delete(&project_id);
                    logger::log_error("project", "create_project", &e.to_string(), "创建项目根目录失败");
                    Err(ProjectServiceError::DbError(e))
                }
            }
        },
        Err(e) => {
            logger::log_error("project", "create_project", &e.to_string(), "创建项目失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 更新项目
pub fn update_project(project_id: &str, name: &str, description: Option<&str>, status: ProjectStatus, leader: Option<&str>, tags: Option<&str>) -> Result<(), ProjectServiceError> {
    // 验证参数
    if name.trim().is_empty() {
        return Err(ProjectServiceError::InvalidRequest("项目名称不能为空".to_string()));
    }

    logger::log_module_operation("project", "update_project", "更新项目", project_id);

    // 先获取项目
    let db_ops = get_project_db_ops();
    let mut project = match db_ops.get_by_id(project_id) {
        Ok(project) => project,
        Err(DbCommonError::NotFound(_)) => {
            logger::log_warning("project", "update_project", "项目未找到", project_id);
            return Err(ProjectServiceError::ProjectNotFound(project_id.to_string()));
        },
        Err(e) => {
            logger::log_error("project", "update_project", &e.to_string(), "获取项目失败");
            return Err(ProjectServiceError::DbError(e));
        }
    };

    // 更新项目信息
    project.name = name.to_string();
    project.description = description.map(|s| s.to_string());
    project.status = status;
    project.updated_at = DbDateTime(chrono::Utc::now());
    project.leader = leader.map(|s| s.to_string());
    project.tags = tags.map(|s| s.to_string());

    match db_ops.update(&project) {
        Ok(_) => {
            logger::log_module_operation("project", "update_project", "成功更新项目", &project.name);
            Ok(())
        },
        Err(e) => {
            logger::log_error("project", "update_project", &e.to_string(), "更新项目失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 删除项目
pub fn delete_project(project_id: &str) -> Result<(), ProjectServiceError> {
    logger::log_module_operation("project", "delete_project", "删除项目", project_id);

    // 先删除项目的所有文件
    match get_project_files(project_id) {
        Ok(files) => {
            for file in files {
                if let Err(e) = delete_file(&file.id) {
                    logger::log_warning("project", "delete_project", &e.to_string(), &format!("删除文件失败: {}", file.path));
                }
            }
        },
        Err(e) => {
            logger::log_warning("project", "delete_project", &e.to_string(), "获取项目文件失败");
        }
    }

    // 删除项目
    let db_ops = get_project_db_ops();
    match db_ops.delete(project_id) {
        Ok(_) => {
            logger::log_module_operation("project", "delete_project", "成功删除项目", project_id);
            Ok(())
        },
        Err(DbCommonError::NotFound(_)) => {
            logger::log_warning("project", "delete_project", "项目未找到", project_id);
            Err(ProjectServiceError::ProjectNotFound(project_id.to_string()))
        },
        Err(e) => {
            logger::log_error("project", "delete_project", &e.to_string(), "删除项目失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 获取项目文件列表
pub fn get_files(project_id: &str) -> Result<Vec<ProjectFile>, ProjectServiceError> {
    logger::log_module_operation("project", "get_files", "获取项目文件列表", project_id);

    // 先检查项目是否存在
    let db_ops = get_project_db_ops();
    match db_ops.get_by_id(project_id) {
        Ok(_) => {
            match get_project_files(project_id) {
                Ok(files) => {
                    logger::log_module_operation("project", "get_files", "成功获取项目文件列表", &format!("共 {} 个文件", files.len()));
                    Ok(files)
                },
                Err(e) => {
                    logger::log_error("project", "get_files", &e.to_string(), "获取项目文件列表失败");
                    Err(ProjectServiceError::DbError(e))
                }
            }
        },
        Err(DbCommonError::NotFound(_)) => {
            logger::log_warning("project", "get_files", "项目未找到", project_id);
            Err(ProjectServiceError::ProjectNotFound(project_id.to_string()))
        },
        Err(e) => {
            logger::log_error("project", "get_files", &e.to_string(), "检查项目是否存在失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 获取文件内容
pub fn get_file(file_id: &str) -> Result<ProjectFile, ProjectServiceError> {
    logger::log_module_operation("project", "get_file", "获取文件", file_id);

    let db_ops = get_project_db_ops();
    match db_ops.get_file_by_id(file_id) {
        Ok(file) => {
            logger::log_module_operation("project", "get_file", "成功获取文件", &file.path);
            Ok(file)
        },
        Err(DbCommonError::NotFound(_)) => {
            logger::log_warning("project", "get_file", "文件未找到", file_id);
            Err(ProjectServiceError::FileNotFound(file_id.to_string()))
        },
        Err(e) => {
            logger::log_error("project", "get_file", &e.to_string(), "获取文件失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 获取文件内容
pub fn read_file_content(file_id: &str) -> Result<String, ProjectServiceError> {
    logger::log_module_operation("project", "read_file_content", "读取文件内容", file_id);

    match get_file_content(file_id) {
        Ok(content) => {
            logger::log_module_operation("project", "read_file_content", "成功读取文件内容", &format!("内容长度: {} 字节", content.len()));
            Ok(content)
        },
        Err(DbCommonError::NotFound(_)) => {
            logger::log_warning("project", "read_file_content", "文件未找到", file_id);
            Err(ProjectServiceError::FileNotFound(file_id.to_string()))
        },
        Err(e) => {
            logger::log_error("project", "read_file_content", &e.to_string(), "读取文件内容失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 保存文件内容
pub fn write_file_content(file_id: &str, content: &str) -> Result<(), ProjectServiceError> {
    logger::log_module_operation("project", "write_file_content", "写入文件内容", file_id);

    match save_file_content(file_id, content) {
        Ok(_) => {
            logger::log_module_operation("project", "write_file_content", "成功写入文件内容", &format!("内容长度: {} 字节", content.len()));
            Ok(())
        },
        Err(DbCommonError::NotFound(_)) => {
            logger::log_warning("project", "write_file_content", "文件未找到", file_id);
            Err(ProjectServiceError::FileNotFound(file_id.to_string()))
        },
        Err(e) => {
            logger::log_error("project", "write_file_content", &e.to_string(), "写入文件内容失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 创建文件
pub fn create_new_file(project_id: &str, parent_id: &str, name: &str, is_directory: bool) -> Result<ProjectFile, ProjectServiceError> {
    // 验证参数
    if name.trim().is_empty() {
        return Err(ProjectServiceError::InvalidRequest("文件名不能为空".to_string()));
    }

    logger::log_module_operation("project", "create_new_file",
        &format!("创建{}: {}", if is_directory { "目录" } else { "文件" }, name),
        &format!("项目: {}, 父目录: {}", project_id, parent_id));

    // 先检查项目是否存在
    let db_ops = get_project_db_ops();
    match db_ops.get_by_id(project_id) {
        Ok(_) => {
            // 获取父目录
            let db_ops = get_project_db_ops();
            let parent = match db_ops.get_file_by_id(parent_id) {
                Ok(file) => {
                    if !file.is_directory {
                        logger::log_warning("project", "create_new_file", "父目录不是目录", parent_id);
                        return Err(ProjectServiceError::InvalidRequest("父目录不是目录".to_string()));
                    }
                    file
                },
                Err(DbCommonError::NotFound(_)) => {
                    logger::log_warning("project", "create_new_file", "父目录未找到", parent_id);
                    return Err(ProjectServiceError::FileNotFound(parent_id.to_string()));
                },
                Err(e) => {
                    logger::log_error("project", "create_new_file", &e.to_string(), "获取父目录失败");
                    return Err(ProjectServiceError::DbError(e));
                }
            };

            // 检查同名文件是否已存在
            let path = format!("{}/{}", parent.path, name);
            let sql = "SELECT COUNT(*) FROM project_files WHERE project_id = ? AND path = ?";
            let conn = crate::db::db_utils::open_connection().map_err(DbCommonError::DbError)?;
            let count: i64 = conn.query_row(sql, rusqlite::params![project_id, path], |row| row.get(0))
                .map_err(|e| {
                    logger::log_error("project", "create_new_file", &e.to_string(), "检查同名文件是否存在失败");
                    ProjectServiceError::DbError(DbCommonError::DbError(crate::db::db_utils::DbUtilsError::QueryError(e)))
                })?;

            if count > 0 {
                logger::log_warning("project", "create_new_file", "同名文件已存在", &path);
                return Err(ProjectServiceError::FileExists(path));
            }

            // 创建文件
            let now = DbDateTime(chrono::Utc::now());
            let file = ProjectFile {
                id: Uuid::new_v4().to_string(),
                project_id: project_id.to_string(),
                name: name.to_string(),
                path,
                content: if is_directory { None } else { Some(String::new()) },
                size: Some(0),
                is_directory,
                last_modified: Some(now.clone()),
                parent_id: Some(parent_id.to_string()),
                created_at: now.clone(),
                updated_at: now,
                created_by: "System".to_string(), // 后续可以改为当前用户
            };

            match create_file(&file) {
                Ok(_) => {
                    logger::log_module_operation("project", "create_new_file",
                        &format!("成功创建{}", if is_directory { "目录" } else { "文件" }),
                        &file.path);
                    Ok(file)
                },
                Err(e) => {
                    logger::log_error("project", "create_new_file", &e.to_string(), "创建文件失败");
                    Err(ProjectServiceError::DbError(e))
                }
            }
        },
        Err(DbCommonError::NotFound(_)) => {
            logger::log_warning("project", "create_new_file", "项目未找到", project_id);
            Err(ProjectServiceError::ProjectNotFound(project_id.to_string()))
        },
        Err(e) => {
            logger::log_error("project", "create_new_file", &e.to_string(), "检查项目是否存在失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 删除文件
pub fn remove_file(file_id: &str) -> Result<(), ProjectServiceError> {
    logger::log_module_operation("project", "remove_file", "删除文件", file_id);

    match delete_file(file_id) {
        Ok(_) => {
            logger::log_module_operation("project", "remove_file", "成功删除文件", file_id);
            Ok(())
        },
        Err(DbCommonError::NotFound(_)) => {
            logger::log_warning("project", "remove_file", "文件未找到", file_id);
            Err(ProjectServiceError::FileNotFound(file_id.to_string()))
        },
        Err(e) => {
            logger::log_error("project", "remove_file", &e.to_string(), "删除文件失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 重命名文件
pub fn rename_file_or_dir(file_id: &str, new_name: &str) -> Result<(), ProjectServiceError> {
    // 验证参数
    if new_name.trim().is_empty() {
        return Err(ProjectServiceError::InvalidRequest("文件名不能为空".to_string()));
    }

    logger::log_module_operation("project", "rename_file_or_dir", "重命名文件", &format!("文件ID: {}, 新名称: {}", file_id, new_name));

    match rename_file(file_id, new_name) {
        Ok(_) => {
            logger::log_module_operation("project", "rename_file_or_dir", "成功重命名文件", &format!("文件ID: {}, 新名称: {}", file_id, new_name));
            Ok(())
        },
        Err(DbCommonError::NotFound(_)) => {
            logger::log_warning("project", "rename_file_or_dir", "文件未找到", file_id);
            Err(ProjectServiceError::FileNotFound(file_id.to_string()))
        },
        Err(e) => {
            logger::log_error("project", "rename_file_or_dir", &e.to_string(), "重命名文件失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 初始化项目模块
pub fn init() -> Result<(), ProjectServiceError> {
    logger::log_module_operation("project", "init", "初始化项目模块", "");

    match init_project_tables() {
        Ok(_) => {
            logger::log_module_operation("project", "init", "成功初始化项目模块", "");
            Ok(())
        },
        Err(e) => {
            logger::log_error("project", "init", &e.to_string(), "初始化项目模块失败");
            Err(ProjectServiceError::DbError(e))
        }
    }
}

/// 扫描目录并将文件添加到项目中
pub fn scan_directory(project_id: &str, directory_path: &str) -> Result<(), ProjectServiceError> {
    // use std::fs;
    // use std::path::Path;
    use std::time::Instant;

    let start_time = Instant::now();
    logger::log_module_operation("project", "scan_directory", "扫描目录",
        &format!("项目ID: {}, 路径: {}", project_id, directory_path));
    logger::log_debug("project", "scan_directory", &format!("开始扫描目录: {}", directory_path));

    // 检查项目是否存在
    let project = match get_project(project_id) {
        Ok(p) => p,
        Err(e) => {
            logger::log_error("project", "scan_directory", &e.to_string(), "获取项目信息失败");
            return Err(e);
        }
    };

    // 获取项目根目录
    let root_files = match get_files(project_id) {
        Ok(files) => files,
        Err(e) => {
            logger::log_error("project", "scan_directory", &e.to_string(), "获取项目文件失败");
            return Err(e);
        }
    };

    let root_dir = match root_files.iter().find(|f| f.is_directory && f.path == project.name) {
        Some(dir) => dir,
        None => {
            let error_msg = format!("项目根目录未找到: {}", project.name);
            logger::log_error("project", "scan_directory", &error_msg, "扫描目录失败");
            return Err(ProjectServiceError::FileNotFound(error_msg));
        }
    };

    // 清空现有文件（除了根目录）
    for file in root_files.iter().filter(|f| f.id != root_dir.id) {
        if let Err(e) = remove_file(&file.id) {
            logger::log_warning("project", "scan_directory", &e.to_string(),
                &format!("删除文件失败: {}", file.path));
        }
    }

    // 递归扫描目录
    scan_directory_recursive(project_id, directory_path, &root_dir.id, &root_dir.path)?;

    // 记录性能日志
    let duration = start_time.elapsed().as_millis();
    logger::log_performance("project", "scan_directory", &format!("扫描目录: {}", directory_path), duration as u64);
    logger::log_module_operation("project", "scan_directory", "成功扫描目录",
        &format!("项目ID: {}, 路径: {}, 耗时: {}ms", project_id, directory_path, duration));
    logger::log_debug("project", "scan_directory", &format!("完成扫描目录: {}, 总耗时: {}ms", directory_path, duration));

    Ok(())
}

/// 递归扫描目录
fn scan_directory_recursive(project_id: &str, fs_path: &str, parent_id: &str, db_path: &str) -> Result<(), ProjectServiceError> {
    use std::fs;
    use std::path::Path;
    use std::time::Instant;

    let start_time = Instant::now();
    logger::log_debug("project", "scan_directory_recursive", &format!("开始扫描目录: {}", fs_path));

    let path = Path::new(fs_path);

    if !path.exists() {
        let error_msg = format!("路径不存在: {}", fs_path);
        logger::log_error("project", "scan_directory_recursive", &error_msg, "扫描目录失败");
        return Err(ProjectServiceError::InvalidRequest(error_msg));
    }

    logger::log_debug_detail("project", "scan_directory_recursive", "路径信息",
        &format!("文件系统路径: {}, 数据库路径: {}, 父ID: {}", fs_path, db_path, parent_id), None);

    if path.is_dir() {
        // 读取目录内容
        let entries = match fs::read_dir(path) {
            Ok(entries) => entries,
            Err(e) => {
                let error_msg = format!("读取目录失败: {}, 错误: {}", fs_path, e);
                logger::log_error("project", "scan_directory_recursive", &error_msg, "扫描目录失败");
                return Err(ProjectServiceError::OperationFailed(error_msg));
            }
        };

        // 处理每个条目
        for entry in entries {
            let entry = match entry {
                Ok(e) => e,
                Err(e) => {
                    logger::log_warning("project", "scan_directory_recursive", &e.to_string(),
                        &format!("读取目录条目失败: {}", fs_path));
                    continue;
                }
            };

            let entry_path = entry.path();
            let file_name = match entry_path.file_name() {
                Some(name) => match name.to_str() {
                    Some(s) => s,
                    None => {
                        logger::log_warning("project", "scan_directory_recursive",
                            "文件名包含无效字符", &format!("{:?}", entry_path));
                        continue;
                    }
                },
                None => {
                    logger::log_warning("project", "scan_directory_recursive",
                        "无法获取文件名", &format!("{:?}", entry_path));
                    continue;
                }
            };

            // 跳过隐藏文件和目录
            if file_name.starts_with(".") {
                continue;
            }

            // 跳过node_modules、target等大型目录
            if entry_path.is_dir() && (
                file_name == "node_modules" ||
                file_name == "target" ||
                file_name == "dist" ||
                file_name == "build" ||
                file_name == ".git"
            ) {
                continue;
            }

            let is_directory = entry_path.is_dir();
            let entry_path_str = match entry_path.to_str() {
                Some(s) => s,
                None => {
                    logger::log_warning("project", "scan_directory_recursive",
                        "路径包含无效字符", &format!("{:?}", entry_path));
                    continue;
                }
            };

            // 创建文件或目录
            let file = match create_new_file(project_id, parent_id, file_name, is_directory) {
                Ok(f) => f,
                Err(e) => {
                    logger::log_warning("project", "scan_directory_recursive", &e.to_string(),
                        &format!("创建文件失败: {}", file_name));
                    continue;
                }
            };

            // 如果是目录，递归扫描
            if is_directory {
                if let Err(e) = scan_directory_recursive(project_id, entry_path_str, &file.id, &file.path) {
                    logger::log_warning("project", "scan_directory_recursive", &e.to_string(),
                        &format!("递归扫描目录失败: {}", entry_path_str));
                }
            } else {
                // 如果是文件，读取内容
                match fs::read_to_string(&entry_path) {
                    Ok(content) => {
                        if let Err(e) = write_file_content(&file.id, &content) {
                            logger::log_warning("project", "scan_directory_recursive", &e.to_string(),
                                &format!("写入文件内容失败: {}", file.path));
                        }
                    },
                    Err(e) => {
                        // 如果读取失败，可能是二进制文件，跳过
                        logger::log_warning("project", "scan_directory_recursive", &e.to_string(),
                            &format!("读取文件内容失败: {}", entry_path_str));
                    }
                }
            }
        }
    }

    // 记录性能日志
    let duration = start_time.elapsed().as_millis();
    logger::log_performance("project", "scan_directory_recursive", &format!("扫描目录: {}", fs_path), duration as u64);
    logger::log_debug("project", "scan_directory_recursive", &format!("完成扫描目录: {}, 耗时: {}ms", fs_path, duration));

    Ok(())
}
