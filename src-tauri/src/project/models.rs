// src-tauri/src/project/models.rs
use serde::{Serialize, Deserialize};
use crate::models::DbDateTime;

/// 项目状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ProjectStatus {
    Active,
    Archived,
    Completed,
}

/// 项目模型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Project {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub path: String,
    pub status: ProjectStatus,
    pub created_at: DbDateTime,
    pub updated_at: DbDateTime,
    pub created_by: String,
    pub leader: Option<String>,
    pub tags: Option<String>,
}

/// 项目文件模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectFile {
    pub id: String,
    pub project_id: String,
    pub name: String,
    pub path: String,
    pub is_directory: bool,
    pub size: Option<u64>,
    pub last_modified: Option<DbDateTime>,
    pub content: Option<String>,
    pub parent_id: Option<String>,
    pub created_at: DbDateTime,
    pub updated_at: DbDateTime,
    pub created_by: String,
}
