// src-tauri/src/events/mod.rs
use once_cell::sync::Lazy;
use parking_lot::RwLock;
use std::collections::HashMap;
use std::sync::Arc;
use log::{debug, error};

/// 事件类型
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum EventType {
    // 项目相关事件
    ProjectCreated,
    ProjectUpdated,
    ProjectDeleted,
    ProjectFileCreated,
    ProjectFileUpdated,
    ProjectFileDeleted,
    
    // 任务相关事件
    TaskCreated,
    TaskUpdated,
    TaskDeleted,
    TaskStarted,
    TaskCompleted,
    TaskFailed,
    
    // 执行机相关事件
    AgentCreated,
    AgentUpdated,
    AgentDeleted,
    AgentStatusChanged,
    
    // AI聊天相关事件
    ConversationCreated,
    ConversationUpdated,
    MessageCreated,
    MessageDeleted,
    
    // 测试用例相关事件
    TestCaseCreated,
    TestCaseUpdated,
    TestCaseDeleted,
    TestCaseExecuted,
    
    // 系统事件
    SystemStarted,
    SystemShutdown,
    DatabaseInitialized,
}

/// 事件数据
#[derive(Debug, <PERSON><PERSON>)]
pub struct EventData {
    pub event_type: EventType,
    pub payload: serde_json::Value,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// 事件处理函数类型
type EventHandler = Box<dyn Fn(&EventData) + Send + Sync + 'static>;

/// 事件总线
pub struct EventBus {
    handlers: RwLock<HashMap<EventType, Vec<EventHandler>>>,
}

impl EventBus {
    /// 创建新的事件总线
    pub fn new() -> Self {
        EventBus {
            handlers: RwLock::new(HashMap::new()),
        }
    }
    
    /// 订阅事件
    pub fn subscribe<F>(&self, event_type: EventType, handler: F)
    where
        F: Fn(&EventData) + Send + Sync + 'static,
    {
        let mut handlers = self.handlers.write();
        let event_handlers = handlers.entry(event_type.clone()).or_insert_with(Vec::new);
        event_handlers.push(Box::new(handler));
        debug!("已订阅事件: {:?}, 当前订阅数: {}", event_type, event_handlers.len());
    }
    
    /// 发布事件
    pub fn publish(&self, event: EventData) {
        let handlers = self.handlers.read();
        if let Some(event_handlers) = handlers.get(&event.event_type) {
            debug!("发布事件: {:?}, 订阅数: {}", event.event_type, event_handlers.len());
            for handler in event_handlers {
                handler(&event);
            }
        } else {
            debug!("发布事件: {:?}, 无订阅者", event.event_type);
        }
    }
    
    /// 创建并发布事件
    pub fn emit(&self, event_type: EventType, payload: serde_json::Value) {
        let event = EventData {
            event_type,
            payload,
            timestamp: chrono::Utc::now(),
        };
        self.publish(event);
    }
}

// 全局事件总线
static EVENT_BUS: Lazy<Arc<EventBus>> = Lazy::new(|| {
    debug!("初始化全局事件总线");
    Arc::new(EventBus::new())
});

/// 获取全局事件总线
pub fn get_event_bus() -> Arc<EventBus> {
    EVENT_BUS.clone()
}

/// 订阅事件的便捷函数
pub fn subscribe<F>(event_type: EventType, handler: F)
where
    F: Fn(&EventData) + Send + Sync + 'static,
{
    get_event_bus().subscribe(event_type, handler);
}

/// 发布事件的便捷函数
pub fn emit(event_type: EventType, payload: serde_json::Value) {
    get_event_bus().emit(event_type, payload);
}

/// 创建事件负载
pub fn create_payload<T: serde::Serialize>(data: &T) -> serde_json::Value {
    match serde_json::to_value(data) {
        Ok(value) => value,
        Err(e) => {
            error!("创建事件负载失败: {}", e);
            serde_json::Value::Null
        }
    }
}
