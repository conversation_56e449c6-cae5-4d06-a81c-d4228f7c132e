// src-tauri/src/logger/file_logger.rs
use log::{Log, Metadata, Record};
use chrono::Local;
use std::fs::{File, OpenOptions, create_dir_all};
use std::io::Write;
use std::sync::Mutex;
use std::path::Path;

pub struct FileLogger {
    file: Mutex<File>,
    max_size: u64,
    current_file_path: String,
}

impl FileLogger {
    pub fn new(log_path: &str) -> Result<Self, std::io::Error> {
        // 确保日志目录存在
        if let Some(parent) = Path::new(log_path).parent() {
            create_dir_all(parent)?;
        }

        let file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(log_path)?;

        Ok(FileLogger {
            file: Mutex::new(file),
            max_size: 10 * 1024 * 1024, // 10MB
            current_file_path: log_path.to_string(),
        })
    }

    fn rotate_log(&self) -> Result<(), std::io::Error> {
        let path = Path::new(&self.current_file_path);
        let parent = path.parent().unwrap_or(Path::new("."));
        let file_name = path.file_name().unwrap().to_str().unwrap();
        let timestamp = Local::now().format("%Y%m%d_%H%M%S");
        let new_name = format!("{}.{}", file_name, timestamp);
        let new_path = parent.join(new_name);

        std::fs::rename(&self.current_file_path, new_path)?;

        let new_file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&self.current_file_path)?;

        if let Ok(mut file_lock) = self.file.lock() {
            *file_lock = new_file;
        }

        Ok(())
    }
}

impl Log for FileLogger {
    fn enabled(&self, metadata: &Metadata) -> bool {
        // 确保所有级别的日志都能被记录，包括 Debug 和 Trace
        metadata.level() <= log::Level::Trace
    }

    fn log(&self, record: &Record) {
        if self.enabled(record.metadata()) {
            // 格式：时间点 [级别] 模块名::文件名:行号 - 消息
            let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S%.3f");
            let module = record.module_path().unwrap_or("unknown");
            let file = record.file().unwrap_or("unknown");
            let line = record.line().unwrap_or(0);
            let level = record.level();

            // 根据日志级别添加不同的颜色标记（仅在终端中显示）
            // 这里我们不使用这个变量，但保留代码以便将来可能在终端中使用
            let _level_str = match level {
                log::Level::Error => format!("\x1B[31m{}\x1B[0m", level), // 红色
                log::Level::Warn => format!("\x1B[33m{}\x1B[0m", level),  // 黄色
                log::Level::Info => format!("\x1B[32m{}\x1B[0m", level),  // 绿色
                log::Level::Debug => format!("\x1B[36m{}\x1B[0m", level), // 青色
                log::Level::Trace => format!("\x1B[35m{}\x1B[0m", level), // 紫色
            };

            // 日志文件中使用普通格式，不带颜色
            // 增强日志格式，添加线程ID和更多上下文信息
            let thread_id = std::thread::current().id();
            let message = format!("{} [{}] [Thread:{:?}] {}:{}:{} - {}\n",
                timestamp,
                level,
                thread_id,
                module,
                file,
                line,
                record.args()
            );

            if let Ok(mut file) = self.file.lock() {
                // 检查文件大小并在需要时轮转
                if let Ok(metadata) = file.metadata() {
                    if metadata.len() >= self.max_size {
                        let _ = self.rotate_log();
                    }
                }

                let _ = file.write_all(message.as_bytes());
                let _ = file.flush();
            }
        }
    }

    fn flush(&self) {
        if let Ok(mut file) = self.file.lock() {
            let _ = file.flush();
        }
    }
}
