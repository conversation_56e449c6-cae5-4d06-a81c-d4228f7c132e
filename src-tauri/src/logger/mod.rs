// src-tauri/src/logger/mod.rs
mod file_logger;
// pub use file_logger::FileLogger; // 暂时注释掉未使用的导入

use log::LevelFilter;
use chrono::Local;
use std::fs::create_dir_all;
use std::path::PathBuf;

/// 初始化日志系统
pub fn init() -> Result<(), String> {
    // 打印当前目录，帮助调试
    let current_dir = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
    println!("Current directory: {:?}", current_dir);

    // 使用项目目录下的logs文件夹
    let log_dir = current_dir.join("logs");
    println!("Log directory: {:?}", log_dir);

    // 打印日志目录的绝对路径，帮助调试
    if let Ok(abs_path) = log_dir.canonicalize() {
        println!("Absolute log directory path: {:?}", abs_path);
    }

    // 确保日志目录存在
    if !log_dir.exists() {
        match create_dir_all(&log_dir) {
            Ok(_) => println!("Created log directory: {:?}", log_dir),
            Err(e) => {
                let error_msg = format!("Failed to create log directory: {}", e);
                println!("{}", error_msg);

                // 如果创建失败，使用当前目录
                println!("Failed to create log directory, using current directory");
                let app_data_dir = current_dir.clone();

                // 在应用数据目录中创建 logs 文件夹
                let fallback_log_dir = app_data_dir.join("logs");
                match create_dir_all(&fallback_log_dir) {
                    Ok(_) => {
                        println!("Created fallback log directory: {:?}", fallback_log_dir);
                        return init_logger(fallback_log_dir);
                    },
                    Err(e) => {
                        let error_msg = format!("Failed to create fallback log directory: {}", e);
                        println!("{}", error_msg);
                        return Err(error_msg);
                    }
                }
            }
        };
    }

    // 初始化日志器
    init_logger(log_dir)
}

/// 初始化日志器
fn init_logger(log_dir: PathBuf) -> Result<(), String> {
    // 生成日志文件名 tasker-时间戳.log
    let timestamp = Local::now().format("%Y%m%d-%H%M%S").to_string();
    let log_path = log_dir.join(format!("tasker-{}.log", timestamp)).to_string_lossy().to_string();
    println!("Log file path: {}", log_path);

    // 创建日志器
    let logger = match file_logger::FileLogger::new(&log_path) {
        Ok(logger) => {
            println!("Created logger successfully");
            logger
        },
        Err(e) => {
            let error_msg = format!("Failed to create logger: {}", e);
            println!("{}", error_msg);
            return Err(error_msg);
        }
    };

    // 设置日志器
    match log::set_boxed_logger(Box::new(logger)) {
        Ok(_) => println!("Set logger successfully"),
        Err(e) => {
            let error_msg = format!("Failed to set logger: {}", e);
            println!("{}", error_msg);
            return Err(error_msg);
        }
    };

    // 设置日志级别为 Debug，确保调试日志可以被记录
    log::set_max_level(LevelFilter::Debug);

    // 记录初始化信息
    log::info!("Logger initialized successfully");
    log::info!("Application started");
    log::info!("Log file: {}", log_path);
    log::info!("Log directory: {:?}", log_dir);
    log::debug!("Debug logging enabled");
    log::debug!("Current log level: Debug");

    Ok(())
}

/// 记录模块操作日志
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_module_operation(module: &str, function: &str, operation: &str, details: &str) {
    log::info!("[MODULE_OP] {} - {} - {} - {}", module, function, operation, details);
}

/// 记录数据库操作日志
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_db_operation(module: &str, table: &str, operation: &str, record_id: &str, details: &str) {
    log::info!("[DB_OP] {} - {} - {} - {} - {}", module, table, operation, record_id, details);
}

/// 记录API操作日志
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_api_operation(module: &str, endpoint: &str, method: &str, status: &str, details: &str) {
    log::info!("[API_OP] {} - {} - {} - {} - {}", module, endpoint, method, status, details);
}

/// 记录用户操作日志
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_user_operation(module: &str, action: &str, user: &str, details: &str) {
    log::info!("[USER_OP] {} - {} - {} - {}", module, action, user, details);
}

/// 记录系统操作日志
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_system_operation(module: &str, operation: &str, status: &str, details: &str) {
    log::info!("[SYSTEM_OP] {} - {} - {} - {}", module, operation, status, details);
}

/// 记录错误日志
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_error(module: &str, function: &str, error: &str, details: &str) {
    // 记录到日志
    log::error!("[ERROR] {} - {} - {} - {}", module, function, error, details);

    // 记录到错误计数器
    let key = format!("{}:{}", module, function);

    // 使用 once_cell 和 lazy_static 来存储错误计数
    use std::collections::HashMap;
    use std::sync::Mutex;
    use once_cell::sync::Lazy;

    static ERROR_COUNTERS: Lazy<Mutex<HashMap<String, u32>>> = Lazy::new(|| {
        Mutex::new(HashMap::new())
    });

    // 更新错误计数
    if let Ok(mut counters) = ERROR_COUNTERS.lock() {
        let counter = counters.entry(key.clone()).or_insert(0);
        *counter += 1;

        // 如果错误频繁发生，记录警告
        if *counter > 5 {
            log::warn!("[ERROR_ALERT] {} - {} - 错误频繁发生 (计数: {}) - {}",
                      module, function, counter, error);
        }
    }
}

/// 记录警告日志
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_warning(module: &str, function: &str, warning: &str, details: &str) {
    log::warn!("[WARNING] {} - {} - {} - {}", module, function, warning, details);
}

/// 记录调试日志
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_debug(module: &str, function: &str, message: &str) {
    log::debug!("[DEBUG] {} - {} - {}", module, function, message);
}

/// 记录详细调试日志，包含更多上下文信息
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_debug_detail(module: &str, function: &str, context: &str, message: &str, data: Option<&str>) {
    if let Some(data_str) = data {
        log::debug!("[DEBUG_DETAIL] {} - {} - {} - {} - {}", module, function, context, message, data_str);
    } else {
        log::debug!("[DEBUG_DETAIL] {} - {} - {} - {}", module, function, context, message);
    }
}

/// 记录性能调试日志，用于性能分析
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_performance(module: &str, function: &str, operation: &str, duration_ms: u64) {
    // 记录到日志
    log::debug!("[PERFORMANCE] {} - {} - {} - {}ms", module, function, operation, duration_ms);

    // 记录到性能指标
    let key = format!("{}:{}:{}", module, function, operation);

    // 使用 once_cell 和 lazy_static 来存储性能指标
    use std::collections::HashMap;
    use std::sync::Mutex;
    use once_cell::sync::Lazy;

    static PERFORMANCE_METRICS: Lazy<Mutex<HashMap<String, Vec<u64>>>> = Lazy::new(|| {
        Mutex::new(HashMap::new())
    });

    // 更新性能指标
    if let Ok(mut metrics) = PERFORMANCE_METRICS.lock() {
        let durations = metrics.entry(key.clone()).or_insert_with(Vec::new);
        durations.push(duration_ms);

        // 只保留最近的100个数据点
        if durations.len() > 100 {
            durations.remove(0);
        }

        // 计算平均值和最大值
        if !durations.is_empty() {
            let avg = durations.iter().sum::<u64>() / durations.len() as u64;
            let max = *durations.iter().max().unwrap_or(&0);

            // 如果当前值超过平均值的2倍，记录警告
            if duration_ms > avg * 2 && duration_ms > 100 {
                log::warn!("[PERFORMANCE_ALERT] {} - {} - {} - 当前: {}ms, 平均: {}ms, 最大: {}ms",
                          module, function, operation, duration_ms, avg, max);
            }
        }
    }
}

/// 记录跟踪日志
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn log_trace(module: &str, function: &str, message: &str) {
    log::trace!("[TRACE] {} - {} - {}", module, function, message);
}

/// 获取性能指标报告
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn get_performance_report() -> Vec<(String, u64, u64, u64)> {
    use std::collections::HashMap;
    use std::sync::Mutex;
    use once_cell::sync::Lazy;

    static PERFORMANCE_METRICS: Lazy<Mutex<HashMap<String, Vec<u64>>>> = Lazy::new(|| {
        Mutex::new(HashMap::new())
    });

    let mut report = Vec::new();

    if let Ok(metrics) = PERFORMANCE_METRICS.lock() {
        for (key, durations) in metrics.iter() {
            if durations.is_empty() {
                continue;
            }

            let count = durations.len() as u64;
            let avg = durations.iter().sum::<u64>() / count;
            let max = *durations.iter().max().unwrap_or(&0);

            report.push((key.clone(), count, avg, max));
        }
    }

    // 按平均时间降序排序
    report.sort_by(|a, b| b.2.cmp(&a.2));

    report
}

/// 获取错误计数报告
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn get_error_report() -> Vec<(String, u32)> {
    use std::collections::HashMap;
    use std::sync::Mutex;
    use once_cell::sync::Lazy;

    static ERROR_COUNTERS: Lazy<Mutex<HashMap<String, u32>>> = Lazy::new(|| {
        Mutex::new(HashMap::new())
    });

    let mut report = Vec::new();

    if let Ok(counters) = ERROR_COUNTERS.lock() {
        for (key, count) in counters.iter() {
            report.push((key.clone(), *count));
        }
    }

    // 按错误计数降序排序
    report.sort_by(|a, b| b.1.cmp(&a.1));

    report
}

/// 重置所有计数器和指标
#[allow(dead_code)] // 添加注解以允许未使用的函数
pub fn reset_metrics() {
    use std::collections::HashMap;
    use std::sync::Mutex;
    use once_cell::sync::Lazy;

    static PERFORMANCE_METRICS: Lazy<Mutex<HashMap<String, Vec<u64>>>> = Lazy::new(|| {
        Mutex::new(HashMap::new())
    });

    static ERROR_COUNTERS: Lazy<Mutex<HashMap<String, u32>>> = Lazy::new(|| {
        Mutex::new(HashMap::new())
    });

    if let Ok(mut metrics) = PERFORMANCE_METRICS.lock() {
        metrics.clear();
    }

    if let Ok(mut counters) = ERROR_COUNTERS.lock() {
        counters.clear();
    }

    log::info!("所有性能指标和错误计数器已重置");
}
