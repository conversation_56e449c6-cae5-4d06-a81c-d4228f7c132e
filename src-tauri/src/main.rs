#![allow(dead_code)]
#![cfg_attr(
  all(not(debug_assertions), target_os = "windows"),
  windows_subsystem = "windows"
)]

mod db;
mod models;
mod scheduler;
mod executor;
mod operation_log;
mod commands;
mod ssh_manager;
mod pty_manager; // <-- Ensure pty_manager is included
mod web_server;
mod test_case;
mod test_case_commands;
mod ai_chat;
mod ai_chat_commands;
mod project;
mod logger;
mod monitor;
mod process_manager;

use tauri::Manager;
use tauri::Size;
use tauri::PhysicalSize;
use db::DbManager;
use models::AppState;
use std::sync::Arc;
use log::{error, info, debug};
// Removed unused import: use std::path::Path;
use std::process::Command;
use std::net::TcpListener;
use std::time::Instant;

// 导入commands.rs中的命令处理函数
use crate::commands::{get_tasks, create_task, update_task, delete_task, run_task, stop_task, get_agents, create_agent, update_agent, delete_agent, get_operation_logs, ping_agents, connect_ssh, disconnect_ssh, execute_ssh_command, transfer_file, test_agent_connection, start_pty, stop_pty, check_pty_status, write_to_pty, resize_pty, subscribe_to_pty_output, check_path_exists, get_performance_metrics, get_error_metrics, reset_metrics};
use crate::test_case_commands::{get_test_cases, create_test_case, update_test_case, delete_test_case, import_test_cases_from_excel};
use crate::ai_chat_commands::{send_chat_message, create_conversation, get_conversations, get_messages, delete_message, save_api_config, get_api_configs, update_api_config, delete_api_config, update_conversation_model};
use crate::project::{get_project_list, get_projects, get_project_by_id, create_new_project, update_project_info, delete_project_by_id, get_project_files, get_file_by_id, read_file, write_file, create_file_or_dir, delete_file_or_dir, rename_file, scan_project_directory, scan_project_files};

fn main() {
    // 记录应用程序启动时间
    let app_start_time = Instant::now();

    // 在最早阶段初始化日志系统
    match logger::init() {
        Ok(_) => println!("Logger initialized successfully"),
        Err(e) => println!("Failed to initialize logger: {}", e)
    }

    // 立即记录启动信息
    log::info!("=== Application starting ===");
    log::info!("Current directory: {:?}", std::env::current_dir().unwrap_or_default());

    // 记录日志初始化耗时
    let logger_init_duration = app_start_time.elapsed().as_millis();
    logger::log_performance("main", "main", "logger_initialization", logger_init_duration as u64);
    debug!("Logger initialization took {} ms", logger_init_duration);

    // 设置全局错误处理
    std::panic::set_hook(Box::new(|panic_info| {
        let location = if let Some(loc) = panic_info.location() {
            format!("{}:{}", loc.file(), loc.line())
        } else {
            "unknown location".to_string()
        };

        let message = match panic_info.payload().downcast_ref::<&str>() {
            Some(s) => *s,
            None => match panic_info.payload().downcast_ref::<String>() {
                Some(s) => s.as_str(),
                None => "Unknown panic message",
            },
        };

        log::error!("PANIC: '{}' at {}", message, location);
    }));

    log::info!("Starting Taskers application");

    // 创建 Tokio 运行时
    let rt = match tokio::runtime::Runtime::new() {
        Ok(runtime) => {
            log::info!("Tokio runtime created successfully");
            runtime
        },
        Err(e) => {
            log::error!("Failed to create Tokio runtime: {}", e);
            panic!("Failed to create Tokio runtime: {}", e);
        }
    };

    let _guard = rt.enter();

    // 初始化web服务器
    web_server::init();

    // 释放端口
    free_port_with_node_script(1420);

    // 使用项目根目录作为数据库路径
    let project_root = std::env::current_dir().unwrap_or_else(|_| std::path::PathBuf::from("."));
    let db_path = project_root.join("tasks.db");
    let db_exists = db_path.exists();

    info!("数据库路径: {:?}, 是否存在: {}", db_path, db_exists);

    let conn = match rusqlite::Connection::open(&db_path) {
        Ok(conn) => {
            info!("成功打开数据库连接");
            conn
        },
        Err(e) => {
            error!("无法打开数据库: {}", e);
            panic!("Failed to open database: {}", e);
        }
    };

    let db_manager = DbManager::new(conn);
    let db_manager = Arc::new(db_manager);

    // 无论数据库文件是否存在，都尝试初始化数据库结构
    info!("开始初始化数据库结构");
    debug!("数据库路径: {:?}", db_path);

    let db_init_start = Instant::now();
    rt.block_on(async {
        // 记录主数据库初始化开始时间
        let main_db_init_start = Instant::now();
        match db_manager.init_database().await {
            Ok(_) => {
                let duration = main_db_init_start.elapsed().as_millis();
                info!("数据库初始化成功，耗时: {}ms", duration);
                logger::log_performance("main", "init_database", "main_database_initialization", duration as u64);
            },
            Err(e) => {
                error!("Failed to initialize database: {}", e);
                logger::log_error("main", "init_database", &e.to_string(), "数据库初始化失败");
                panic!("Database initialization failed");
            }
        }

        // 初始化AI聊天相关的数据库表
        let ai_chat_db_init_start = Instant::now();
        match db_manager.init_ai_chat_database().await {
            Ok(_) => {
                let duration = ai_chat_db_init_start.elapsed().as_millis();
                info!("AI聊天数据库初始化成功，耗时: {}ms", duration);
                logger::log_performance("main", "init_ai_chat_database", "ai_chat_database_initialization", duration as u64);
            },
            Err(e) => {
                error!("Failed to initialize AI chat database: {}", e);
                logger::log_error("main", "init_ai_chat_database", &e.to_string(), "AI聊天数据库初始化失败");
                panic!("AI chat database initialization failed");
            }
        }

        // 初始化项目管理模块
        let project_init_start = Instant::now();
        match project::init() {
            Ok(_) => {
                let duration = project_init_start.elapsed().as_millis();
                info!("Project management module initialized successfully, took: {}ms", duration);
                logger::log_performance("main", "init_project", "project_module_initialization", duration as u64);
            },
            Err(e) => {
                error!("Failed to initialize project management module: {}", e);
                logger::log_error("main", "init_project", &e.to_string(), "项目管理模块初始化失败");
                panic!("Project management module initialization failed");
            }
        }
    });

    let total_db_init_duration = db_init_start.elapsed().as_millis();
    debug!("数据库初始化总耗时: {}ms", total_db_init_duration);
    logger::log_performance("main", "main", "total_database_initialization", total_db_init_duration as u64);

    // 检查数据库中是否有任务数据
    rt.block_on(async {
        match db_manager.get_tasks().await {
            Ok(tasks) => info!("数据库中已有 {} 个任务", tasks.len()),
            Err(e) => error!("获取任务失败: {}", e)
        }
    });

    // 检查并初始化默认的Master执行机
    rt.block_on(async {
        // 获取所有执行机
        match db_manager.get_agents().await {
            Ok(agents) => {
                // 检查是否已存在Master执行机
                let master_exists = agents.iter().any(|agent| agent.name == "Master" && agent.ip == "127.0.0.1");

                if !master_exists {
                    info!("创建默认的Master执行机");
                    // 创建默认的Master执行机
                    let master_agent = models::Agent {
                        id: uuid::Uuid::new_v4().to_string(),
                        name: String::from("Master"),
                        ip: String::from("127.0.0.1"),
                        username: String::from("localhost"),
                        password: String::from(""),
                        work_dir: String::from("/tmp"),
                        status: models::AgentStatus::Online,
                        last_seen: models::DbDateTime(chrono::Utc::now()),
                        created_at: models::DbDateTime(chrono::Utc::now()),
                        ping_latency: Some(0),
                        labels: Some(String::from("master,local")),
                        description: Some(String::from("本地主机执行机，类似Jenkins master")),
                        max_jobs: Some(10),
                    };

                    match db_manager.create_agent(&master_agent).await {
                        Ok(_) => info!("成功创建默认的Master执行机"),
                        Err(e) => error!("创建默认的Master执行机失败: {}", e)
                    }
                } else {
                    info!("默认的Master执行机已存在，无需创建")
                }
            },
            Err(e) => error!("获取执行机列表失败: {}", e)
        }
    });


    let app_state = AppState::new(db_manager);

    // 使用kill-port.mjs脚本释放端口
    free_port_with_node_script(1420);

    // 初始化资源监控器
    info!("初始化资源监控器");
    let resource_monitor = monitor::init_resource_monitor();
    info!("资源监控器初始化完成");

    // 创建一个资源监控器的Arc引用，以便在窗口关闭事件中使用
    let resource_monitor_ref = std::sync::Arc::new(resource_monitor);

    tauri::Builder::default()
        .setup(move |app| {
            let window = app.get_webview_window("main").unwrap();
            window.set_min_size(Some(Size::Physical(PhysicalSize { width: 800, height: 600 }))).unwrap();
            window.show().unwrap();

            // 创建一个克隆的窗口引用，用于关闭事件处理
            let window_clone = window.clone();

            // 克隆资源监控器引用，用于关闭事件处理
            let resource_monitor_clone = std::sync::Arc::clone(&resource_monitor_ref);

            // 监听窗口关闭事件
            window.on_window_event(move |event| {
                if let tauri::WindowEvent::CloseRequested { api, .. } = event {
                    // 阻止默认的关闭行为
                    api.prevent_close();

                    info!("窗口关闭事件触发，正在清理资源...");

                    // 在这里执行清理操作
                    info!("正在执行应用程序清理操作...");
                    // 关闭web服务器
                    web_server::shutdown();
                    // 停止资源监控器
                    resource_monitor_clone.stop();
                    // 释放端口资源
                    free_port_with_node_script(1420);

                    // 等待资源清理完成
                    std::thread::sleep(std::time::Duration::from_millis(500));

                    // 关闭窗口
                    info!("清理完成，正在关闭窗口...");
                    window_clone.close().unwrap();
                }
            });

            Ok(())
        })
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            get_tasks,
            create_task,
            update_task,
            delete_task,
            run_task,
            stop_task,
            get_agents,
            create_agent,
            update_agent,
            delete_agent,
            get_operation_logs,
            ping_agents,
            connect_ssh,
            disconnect_ssh,
            execute_ssh_command,
            transfer_file,
            test_agent_connection,
            start_pty,
            stop_pty,
            check_pty_status,
            write_to_pty,
            resize_pty,
            subscribe_to_pty_output,
            get_test_cases,
            create_test_case,
            update_test_case,
            delete_test_case,
            import_test_cases_from_excel,
            send_chat_message,
            create_conversation,
            get_conversations,
            get_messages,
            delete_message,
            save_api_config,
            get_api_configs,
            update_api_config,
            delete_api_config,
            update_conversation_model, // Now correctly expects AppState
            // Project management commands
            get_project_list,
            get_projects,
            get_project_by_id,
            create_new_project,
            update_project_info,
            delete_project_by_id,
            get_project_files,
            get_file_by_id,
            read_file,
            write_file,
            create_file_or_dir,
            delete_file_or_dir,
            rename_file,
            scan_project_directory,
            scan_project_files,
            check_path_exists,
            get_performance_metrics,
            get_error_metrics,
            reset_metrics,
            // 进程管理命令
            process_manager::launch_process,
            process_manager::kill_process,
            process_manager::write_process,
            process_manager::read_process,
            process_manager::list_processes
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

// 使用Node.js脚本释放端口
fn free_port_with_node_script(port: u16) {
    info!("使用kill-port.mjs脚本释放端口 {}", port);

    // 获取项目根目录路径
    let current_dir = std::env::current_dir().unwrap_or_else(|_| std::path::PathBuf::from("."));
    // 优先检查src-tauri目录下的脚本
    let mut script_path = current_dir.join("src-tauri").join("kill-port.mjs");

    // 如果src-tauri目录不存在，尝试项目根目录的脚本
    if !script_path.exists() {
        script_path = current_dir.join("kill-port.mjs");
        if script_path.exists() {
            info!("使用项目根目录的kill-port.mjs脚本");
        } else {
            error!("kill-port.mjs脚本不存在: {:?}", script_path);
            return;
        }
    }

    // 先检查端口是否已经可用
    if is_port_available(port) {
        info!("端口 {} 已经可用，无需释放", port);
        return;
    }

    // 执行Node.js脚本释放端口
    let node_result = Command::new("node")
        .arg(&script_path)
        .output();

    match node_result {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);

            if !stderr.is_empty() {
                error!("执行kill-port.mjs脚本出错: {}", stderr);
            }

            info!("kill-port.mjs脚本执行结果: {}", stdout.trim());

            // 添加延迟，确保端口完全释放
            std::thread::sleep(std::time::Duration::from_millis(500));

            // 检查端口是否真的可用
            if !is_port_available(port) {
                error!("端口 {} 仍然被占用，尝试强制释放", port);
                force_kill_port(port);

                // 再次检查端口是否可用
                std::thread::sleep(std::time::Duration::from_millis(500));
                if !is_port_available(port) {
                    error!("端口 {} 释放失败，应用可能无法正常启动", port);
                }
            }
        },
        Err(e) => {
            error!("执行kill-port.mjs脚本失败: {}", e);
            // 脚本执行失败，尝试直接使用force_kill_port
            force_kill_port(port);
        }
    }
}

// 检查端口是否可用
fn is_port_available(port: u16) -> bool {
    match TcpListener::bind(format!("127.0.0.1:{}", port)) {
        Ok(_) => true,
        Err(e) => {
            error!("端口 {} 不可用: {}", port, e);
            false
        }
    }
}

// 强制结束占用端口的进程
fn force_kill_port(port: u16) {
    info!("强制结束占用端口 {} 的进程", port);

    #[cfg(target_os = "macos")]
    {
        let kill_cmd = Command::new("sh")
            .arg("-c")
            .arg(format!("lsof -ti:{} | xargs kill -9", port))
            .output();

        match kill_cmd {
            Ok(output) => {
                if !output.status.success() {
                    error!("强制结束进程失败: {}", String::from_utf8_lossy(&output.stderr));
                } else {
                    info!("成功结束占用端口 {} 的进程", port);
                }
            },
            Err(e) => error!("执行强制结束进程命令失败: {}", e)
        }
    }

    #[cfg(target_os = "windows")]
    {
        let kill_cmd = Command::new("cmd")
            .arg("/C")
            .arg(format!("for /f \"tokens=5\" %a in ('netstat -ano ^| findstr :{} ^| findstr LISTENING') do taskkill /F /PID %a", port))
            .output();

        match kill_cmd {
            Ok(output) => {
                if !output.status.success() {
                    error!("强制结束进程失败: {}", String::from_utf8_lossy(&output.stderr));
                } else {
                    info!("成功结束占用端口 {} 的进程", port);
                }
            },
            Err(e) => error!("执行强制结束进程命令失败: {}", e)
        }
    }

    #[cfg(target_os = "linux")]
    {
        let kill_cmd = Command::new("sh")
            .arg("-c")
            .arg(format!("fuser -k -n tcp {}", port))
            .output();

        match kill_cmd {
            Ok(output) => {
                if !output.status.success() {
                    error!("强制结束进程失败: {}", String::from_utf8_lossy(&output.stderr));
                } else {
                    info!("成功结束占用端口 {} 的进程", port);
                }
            },
            Err(e) => error!("执行强制结束进程命令失败: {}", e)
        }
    }

    // 再次检查端口是否可用
    std::thread::sleep(std::time::Duration::from_millis(500));
    if !is_port_available(port) {
        error!("端口 {} 仍然被占用，无法释放", port);
    } else {
        info!("端口 {} 已成功释放", port);
    }
}