// src-tauri/src/models.rs
use serde::{Serialize, Deserialize};
use std::sync::Arc;
use chrono::{DateTime, Utc};

/// 数据库日期时间包装器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DbDateTime(pub DateTime<Utc>);

/// 任务状态
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum TaskStatus {
    Pending,
    Running,
    Completed,
    Failed,
}

/// 执行模式
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ExecutionMode {
    Serial,
    Parallel,
}

/// 任务模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Task {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub working_dir: Option<String>,
    pub schedule: Option<String>,
    pub status: TaskStatus,
    pub agent_id: Option<String>,
    pub parent_id: Option<String>,
    pub is_group: bool,
    pub execution_mode: Option<ExecutionMode>,
    pub child_tasks: Option<Vec<String>>,
    pub test_cases: Option<Vec<String>>,
    pub loop_count: Option<u32>,
    pub custom_command: Option<String>,
}

/// 执行机状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AgentStatus {
    Online,
    Offline,
    Busy,
}

/// 执行机模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Agent {
    pub id: String,
    pub name: String,
    pub ip: String,
    pub username: String,
    pub password: String,
    pub work_dir: String,
    pub status: AgentStatus,
    pub last_seen: DbDateTime,
    pub created_at: DbDateTime,
    pub ping_latency: Option<u32>,
    pub labels: Option<String>,
    pub description: Option<String>,
    pub max_jobs: Option<u32>,
}

/// 项目模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Project {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub status: ProjectStatus,
    pub created_at: DbDateTime,
    pub updated_at: DbDateTime,
    pub created_by: String,
    pub leader: Option<String>,
    pub tags: Option<String>,
}

/// 项目状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ProjectStatus {
    Active,
    Archived,
    Completed,
}

/// 应用状态
pub struct AppState {
    pub db: Arc<crate::db::DbManager>,
}

impl AppState {
    pub fn new(db: Arc<crate::db::DbManager>) -> Self {
        AppState { db }
    }
}
