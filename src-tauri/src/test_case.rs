// src-tauri/src/test_case.rs
use serde::{Serialize, Deserialize};
use crate::models::DbDateTime;

/// 测试用例状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TestCaseStatus {
    Pending,
    Passed,
    Failed,
    Skipped,
}

/// 测试用例
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestCase {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub status: TestCaseStatus,
    pub created_at: DbDateTime,
    pub updated_at: DbDateTime,
    pub expected_result: String,
    pub actual_result: Option<String>,
    pub tags: Option<String>,
}
