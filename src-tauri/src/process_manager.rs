use log::{info, error};
use std::collections::HashMap;
use std::process::{Command, Stdio, Child};
use std::io::{Read, Write};
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::time::timeout;


/// 进程管理器，用于管理终端进程
pub struct ProcessManager {
    processes: Arc<Mutex<HashMap<u32, ProcessInfo>>>,
}

/// 进程信息
pub struct ProcessInfo {
    pub id: u32,
    pub child: Child,
    pub command: String,
    pub working_dir: Option<String>,
}

impl ProcessManager {
    /// 创建新的进程管理器
    pub fn new() -> Self {
        ProcessManager {
            processes: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 启动新进程
    pub fn launch_process(&self, command: &str, wait: bool, max_wait_seconds: u64, cwd: Option<&str>) -> Result<u32, String> {
        info!("启动进程: {}, 工作目录: {:?}", command, cwd);

        // 确定工作目录
        let working_dir = if let Some(dir) = cwd {
            info!("使用指定的工作目录: {}", dir);
            Some(dir.to_string())
        } else {
            info!("未指定工作目录，使用当前目录");
            None
        };

        // 构建命令
        let mut cmd = if cfg!(target_os = "windows") {
            info!("检测到Windows系统，使用cmd.exe");
            let mut c = Command::new("cmd");
            c.args(["/C", command]);
            c
        } else {
            info!("检测到非Windows系统，使用sh");
            let shell_path = std::path::Path::new("/bin/sh");
            if !shell_path.exists() {
                error!("找不到/bin/sh，尝试使用/bin/bash");
                let bash_path = std::path::Path::new("/bin/bash");
                if !bash_path.exists() {
                    return Err("找不到可用的shell（/bin/sh或/bin/bash）".to_string());
                }
                let mut c = Command::new("/bin/bash");
                c.args(["-c", command]);
                c
            } else {
                let mut c = Command::new("sh");
                c.args(["-c", command]);
                c
            }
        };

        // 设置工作目录
        if let Some(dir) = &working_dir {
            cmd.current_dir(dir);
        }

        // 设置标准输入输出
        cmd.stdin(Stdio::piped())
           .stdout(Stdio::piped())
           .stderr(Stdio::piped());

        // 启动进程
        info!("准备启动进程，命令行: {:?}", cmd);

        match cmd.spawn() {
            Ok(child) => {
                let process_id = match child.id() {
                    id => {
                        info!("进程启动成功，进程ID: {}", id);
                        id
                    }
                };

                // 生成唯一的终端ID
                let terminal_id = process_id;
                info!("分配终端ID: {}", terminal_id);

                // 存储进程信息
                let process_info = ProcessInfo {
                    id: terminal_id,
                    child,
                    command: command.to_string(),
                    working_dir,
                };

                info!("将进程信息存入进程管理器");
                self.processes.lock().unwrap().insert(terminal_id, process_info);

                // 如果需要等待进程完成
                if wait {
                    // 在另一个线程中等待进程完成
                    let processes = Arc::clone(&self.processes);
                    let terminal_id_clone = terminal_id;

                    tokio::spawn(async move {
                        // 等待进程完成，最多等待max_wait_seconds秒
                        match timeout(Duration::from_secs(max_wait_seconds), async move {
                            let mut processes_guard = processes.lock().unwrap();
                            if let Some(process_info) = processes_guard.get_mut(&terminal_id_clone) {
                                match process_info.child.wait() {
                                    Ok(status) => {
                                        info!("进程 {} 已完成，退出状态: {:?}", terminal_id_clone, status);
                                    },
                                    Err(e) => {
                                        error!("等待进程 {} 完成时出错: {}", terminal_id_clone, e);
                                    }
                                }
                            }
                        }).await {
                            Ok(_) => {
                                info!("进程 {} 在超时前完成", terminal_id_clone);
                            },
                            Err(_) => {
                                info!("进程 {} 超时，但仍在后台运行", terminal_id_clone);
                            }
                        }
                    });
                }

                Ok(terminal_id)
            },
            Err(e) => {
                error!("启动进程失败: {}", e);
                // 尝试获取更详细的错误信息
                let error_kind = match e.kind() {
                    std::io::ErrorKind::NotFound => "找不到命令或程序",
                    std::io::ErrorKind::PermissionDenied => "权限被拒绝",
                    std::io::ErrorKind::ConnectionRefused => "连接被拒绝",
                    std::io::ErrorKind::ConnectionReset => "连接被重置",
                    std::io::ErrorKind::ConnectionAborted => "连接被中止",
                    std::io::ErrorKind::NotConnected => "未连接",
                    std::io::ErrorKind::AddrInUse => "地址已被使用",
                    std::io::ErrorKind::AddrNotAvailable => "地址不可用",
                    std::io::ErrorKind::BrokenPipe => "管道已断开",
                    std::io::ErrorKind::AlreadyExists => "已经存在",
                    std::io::ErrorKind::WouldBlock => "操作将阻塞",
                    std::io::ErrorKind::InvalidInput => "无效的输入参数",
                    std::io::ErrorKind::InvalidData => "无效的数据",
                    std::io::ErrorKind::TimedOut => "操作超时",
                    std::io::ErrorKind::WriteZero => "写入零字节",
                    std::io::ErrorKind::Interrupted => "操作被中断",
                    std::io::ErrorKind::Other => "其他错误",
                    std::io::ErrorKind::UnexpectedEof => "意外的文件结束",
                    _ => "未知错误",
                };
                error!("错误类型: {}", error_kind);

                // 尝试使用绝对路径
                if error_kind == "找不到命令或程序" {
                    if cfg!(target_os = "windows") {
                        error!("尝试使用绝对路径: C:\\Windows\\System32\\cmd.exe");
                    } else {
                        error!("尝试使用绝对路径: /bin/bash 或 /bin/sh");
                    }
                }

                Err(format!("启动进程失败: {} ({})", e, error_kind))
            }
        }
    }

    /// 终止进程
    pub fn kill_process(&self, terminal_id: u32) -> Result<(), String> {
        info!("终止进程: {}", terminal_id);

        let mut processes = self.processes.lock().unwrap();

        if let Some(mut process_info) = processes.remove(&terminal_id) {
            // 尝试终止进程
            match process_info.child.kill() {
                Ok(_) => {
                    info!("进程 {} 已终止", terminal_id);
                    Ok(())
                },
                Err(e) => {
                    error!("终止进程 {} 失败: {}", terminal_id, e);
                    Err(format!("终止进程失败: {}", e))
                }
            }
        } else {
            Err(format!("找不到进程 {}", terminal_id))
        }
    }

    /// 向进程写入数据
    pub fn write_to_process(&self, terminal_id: u32, input: &str) -> Result<(), String> {
        let mut processes = self.processes.lock().unwrap();

        if let Some(process_info) = processes.get_mut(&terminal_id) {
            if let Some(stdin) = process_info.child.stdin.as_mut() {
                match stdin.write_all(input.as_bytes()) {
                    Ok(_) => {
                        stdin.flush().map_err(|e| format!("刷新标准输入失败: {}", e))?;
                        Ok(())
                    },
                    Err(e) => {
                        error!("向进程 {} 写入数据失败: {}", terminal_id, e);
                        Err(format!("写入数据失败: {}", e))
                    }
                }
            } else {
                Err("进程的标准输入不可用".to_string())
            }
        } else {
            Err(format!("找不到进程 {}", terminal_id))
        }
    }

    /// 从进程读取数据
    pub fn read_process(&self, terminal_id: u32, _wait: bool, _max_wait_seconds: u64) -> Result<String, String> {
        let mut processes = self.processes.lock().unwrap();

        if let Some(process_info) = processes.get_mut(&terminal_id) {
            // 检查进程是否仍在运行
            match process_info.child.try_wait() {
                Ok(Some(status)) => {
                    info!("进程 {} 已结束，退出状态: {:?}", terminal_id, status);
                    // 进程已结束，读取剩余输出
                    let mut output = String::new();
                    if let Some(stdout) = process_info.child.stdout.as_mut() {
                        stdout.read_to_string(&mut output).map_err(|e| format!("读取标准输出失败: {}", e))?;
                    }
                    if let Some(stderr) = process_info.child.stderr.as_mut() {
                        let mut err_output = String::new();
                        stderr.read_to_string(&mut err_output).map_err(|e| format!("读取标准错误失败: {}", e))?;
                        if !err_output.is_empty() {
                            output.push_str(&err_output);
                        }
                    }
                    return Ok(output);
                },
                Ok(None) => {
                    // 进程仍在运行
                },
                Err(e) => {
                    error!("检查进程 {} 状态失败: {}", terminal_id, e);
                    return Err(format!("检查进程状态失败: {}", e));
                }
            }

            // 读取可用的输出
            let mut output = String::new();
            let mut buffer = [0; 1024];

            if let Some(stdout) = process_info.child.stdout.as_mut() {
                // 非阻塞读取
                match stdout.read(&mut buffer) {
                    Ok(n) if n > 0 => {
                        output.push_str(&String::from_utf8_lossy(&buffer[0..n]));
                    },
                    Ok(_) => {},
                    Err(e) => {
                        error!("读取进程 {} 标准输出失败: {}", terminal_id, e);
                    }
                }
            }

            if let Some(stderr) = process_info.child.stderr.as_mut() {
                // 非阻塞读取
                match stderr.read(&mut buffer) {
                    Ok(n) if n > 0 => {
                        output.push_str(&String::from_utf8_lossy(&buffer[0..n]));
                    },
                    Ok(_) => {},
                    Err(e) => {
                        error!("读取进程 {} 标准错误失败: {}", terminal_id, e);
                    }
                }
            }

            Ok(output)
        } else {
            Err(format!("找不到进程 {}", terminal_id))
        }
    }

    /// 列出所有进程
    pub fn list_processes(&self) -> Vec<(u32, String, Option<String>)> {
        let processes = self.processes.lock().unwrap();

        processes.iter()
            .map(|(id, info)| (*id, info.command.clone(), info.working_dir.clone()))
            .collect()
    }
}

// 全局进程管理器实例
lazy_static::lazy_static! {
    static ref PROCESS_MANAGER: ProcessManager = ProcessManager::new();
}

// 获取进程管理器实例
pub fn get_process_manager() -> &'static ProcessManager {
    &PROCESS_MANAGER
}

// 命令处理函数

/// 启动新进程
#[tauri::command]
pub fn launch_process(command: String, wait: bool, max_wait_seconds: u64, cwd: Option<String>) -> Result<serde_json::Value, String> {
    let process_manager = get_process_manager();

    match process_manager.launch_process(&command, wait, max_wait_seconds, cwd.as_deref()) {
        Ok(terminal_id) => {
            let result = serde_json::json!({
                "terminalId": terminal_id
            });
            Ok(result)
        },
        Err(e) => Err(e)
    }
}

/// 终止进程
#[tauri::command]
pub fn kill_process(terminal_id: u32) -> Result<(), String> {
    let process_manager = get_process_manager();
    process_manager.kill_process(terminal_id)
}

/// 向进程写入数据
#[tauri::command]
pub fn write_process(terminal_id: u32, input_text: String) -> Result<(), String> {
    let process_manager = get_process_manager();
    process_manager.write_to_process(terminal_id, &input_text)
}

/// 从进程读取数据
#[tauri::command]
pub fn read_process(terminal_id: u32, wait: bool, max_wait_seconds: u64) -> Result<String, String> {
    let process_manager = get_process_manager();
    process_manager.read_process(terminal_id, wait, max_wait_seconds)
}

/// 列出所有进程
#[tauri::command]
pub fn list_processes() -> Vec<serde_json::Value> {
    let process_manager = get_process_manager();
    let processes = process_manager.list_processes();

    processes.into_iter()
        .map(|(id, command, working_dir)| {
            serde_json::json!({
                "terminalId": id,
                "command": command,
                "workingDir": working_dir
            })
        })
        .collect()
}
