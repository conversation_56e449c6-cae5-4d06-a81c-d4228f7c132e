{"$schema": "https://raw.githubusercontent.com/tauri-apps/tauri/dev/tooling/cli/schema.json", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "productName": "taskers", "version": "0.0.1", "identifier": "com.taskers.dev", "app": {"security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "Taskers", "width": 1200, "height": 800, "center": true, "visible": true}]}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"shell": {"open": true}, "window": {"center": true, "setSize": true, "startDragging": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "show": true, "hide": true, "close": true, "setResizable": true, "setTitle": true, "setFullscreen": true}, "dialog": {"all": true}, "fs": {"readFile": true, "writeFile": true, "readDir": true, "exists": true, "scope": ["**"]}, "process": {"relaunchDangerousAllowSymlinkMacos": true, "all": true, "scope": [{"name": "sh", "cmd": "sh", "args": true}, {"name": "bash", "cmd": "bash", "args": true}, {"name": "cmd", "cmd": "cmd", "args": true}]}}}